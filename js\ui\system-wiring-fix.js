/**
 * System Wiring Fix for StarCrypt
 * Resolves conflicts, duplicate loading, and improves professional presentation
 * Ensures single source of truth from server.js
 */

class SystemWiringFix {
  constructor() {
    this.isInitialized = false;
    this.strategyLoadTracker = new Set();
    this.activeTooltip = null;
    this.init();
  }

  init() {
    console.log('[SystemWiringFix] Initializing comprehensive system fixes...');
    
    try {
      // Wait for DOM and essential components
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
          setTimeout(() => this.executeSystemFixes(), 2000);
        });
      } else {
        setTimeout(() => this.executeSystemFixes(), 2000);
      }
    } catch (error) {
      console.error('[SystemWiringFix] Error during initialization:', error);
    }
  }

  executeSystemFixes() {
    if (this.isInitialized) {
      console.log('[SystemWiringFix] Already initialized, skipping...');
      return;
    }

    console.log('[SystemWiringFix] Executing comprehensive system fixes...');
    
    try {
      // 1. Fix duplicate strategy loading
      this.fixDuplicateStrategyLoading();
      
      // 2. Fix signal lights matrix uniformity
      this.fixSignalLightsMatrix();
      
      // 3. Fix ML containers positioning
      this.fixMLContainerPositioning();
      
      // 4. Fix mini chart sizing and alignment
      this.fixMiniChartSizing();
      
      // 5. Fix tooltip system
      this.fixTooltipSystem();
      
      // 6. Fix starfield animation
      this.fixStarfieldAnimation();
      
      // 7. Add strategy helper steps
      this.addStrategyHelperSteps();
      
      // 8. Enhance professional presentation
      this.enhanceProfessionalPresentation();
      
      // 9. Fix WebSocket error handling
      this.fixWebSocketErrorHandling();
      
      this.isInitialized = true;
      console.log('[SystemWiringFix] ✅ All system fixes applied successfully');
      
    } catch (error) {
      console.error('[SystemWiringFix] Error executing system fixes:', error);
    }
  }

  fixDuplicateStrategyLoading() {
    console.log('[SystemWiringFix] 🔄 Fixing duplicate strategy loading...');
    
    // Remove duplicate strategy selectors
    const strategySelectors = document.querySelectorAll('[id*="strategy"], [id*="Strategy"]');
    const seenIds = new Set();
    
    strategySelectors.forEach(selector => {
      if (seenIds.has(selector.id)) {
        console.log(`Removing duplicate strategy selector: ${selector.id}`);
        selector.remove();
      } else {
        seenIds.add(selector.id);
      }
    });

    // Override strategy loading functions to prevent duplicates
    const originalApplyStrategy = window.applySelectedStrategy;
    window.applySelectedStrategy = (strategyId) => {
      if (this.strategyLoadTracker.has(strategyId)) {
        console.log(`Strategy ${strategyId} already loading, skipping duplicate...`);
        return;
      }
      
      this.strategyLoadTracker.add(strategyId);
      
      try {
        if (originalApplyStrategy) {
          originalApplyStrategy(strategyId);
        }
      } finally {
        // Clear tracker after a delay
        setTimeout(() => {
          this.strategyLoadTracker.delete(strategyId);
        }, 1000);
      }
    };

    // Ensure single strategy change event handler
    this.consolidateStrategyEventHandlers();
  }

  consolidateStrategyEventHandlers() {
    // Remove all existing strategy change listeners
    const newDocument = document.cloneNode(true);
    
    // Add single, authoritative strategy change handler
    document.addEventListener('strategyChanged', (e) => {
      if (e.detail && e.detail.strategyId) {
        console.log(`[SystemWiringFix] Strategy changed to: ${e.detail.strategyId}`);
        
        // Update global strategy
        window.currentStrategy = e.detail.strategyId;
        
        // Save to localStorage
        localStorage.setItem('currentStrategy', e.detail.strategyId);
        
        // Update Oracle Matrix
        if (typeof window.updateOracleMatrixForStrategy === 'function') {
          window.updateOracleMatrixForStrategy(e.detail.strategyId);
        }
        
        // Update signal lights
        if (typeof window.updateAllSignalLights === 'function') {
          setTimeout(() => window.updateAllSignalLights(), 500);
        }
      }
    }, { once: false });
  }

  fixSignalLightsMatrix() {
    console.log('[SystemWiringFix] 🔧 Fixing signal lights matrix uniformity...');
    
    const style = document.createElement('style');
    style.id = 'signal-matrix-fix';
    style.textContent = `
      /* Oracle Matrix Uniformity Fix */
      #momentum-table {
        width: 100% !important;
        table-layout: fixed !important;
        border-collapse: separate !important;
        border-spacing: 2px !important;
      }
      
      #momentum-table td {
        width: calc(100% / 8) !important; /* 8 columns for 7 timeframes + indicator name */
        text-align: center !important;
        vertical-align: middle !important;
        padding: 4px !important;
      }
      
      #momentum-table td:first-child {
        width: 120px !important; /* Fixed width for indicator names */
        text-align: left !important;
        padding-left: 8px !important;
      }
      
      .signal-circle, .circle {
        width: 20px !important;
        height: 20px !important;
        border-radius: 50% !important;
        margin: 0 auto !important;
        display: block !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
      }
      
      .signal-circle:hover, .circle:hover {
        transform: scale(1.1) !important;
        border-color: rgba(0, 255, 255, 0.6) !important;
        box-shadow: 0 0 8px rgba(0, 255, 255, 0.4) !important;
      }
    `;
    
    // Remove existing style if present
    const existingStyle = document.getElementById('signal-matrix-fix');
    if (existingStyle) existingStyle.remove();
    
    document.head.appendChild(style);
  }

  fixMLContainerPositioning() {
    console.log('[SystemWiringFix] 📊 Fixing ML container positioning...');
    
    // Move ML containers to proper overlay positions
    const mlContainers = document.querySelectorAll('.ml-advanced-options, .ml-historical-analysis');
    
    mlContainers.forEach(container => {
      if (container) {
        container.style.position = 'fixed';
        container.style.top = '50%';
        container.style.left = '50%';
        container.style.transform = 'translate(-50%, -50%)';
        container.style.zIndex = '10000';
        container.style.background = 'rgba(0, 20, 40, 0.95)';
        container.style.border = '2px solid rgba(0, 255, 255, 0.5)';
        container.style.borderRadius = '8px';
        container.style.padding = '20px';
        container.style.maxWidth = '600px';
        container.style.maxHeight = '80vh';
        container.style.overflow = 'auto';
        container.style.backdropFilter = 'blur(10px)';
        container.style.boxShadow = '0 8px 32px rgba(0, 255, 255, 0.3)';
        container.style.display = 'none'; // Hidden by default
      }
    });

    // Ensure ML buttons work properly
    this.fixMLButtonFunctionality();
  }

  fixMLButtonFunctionality() {
    const mlControlBtn = document.getElementById('mlControlCenterButton');
    const mlHistoryBtn = document.getElementById('mlHistoricalButton');
    
    if (mlControlBtn) {
      // Remove existing listeners
      const newBtn = mlControlBtn.cloneNode(true);
      mlControlBtn.parentNode.replaceChild(newBtn, mlControlBtn);
      
      newBtn.addEventListener('click', () => {
        const panel = document.getElementById('compactMLControlPanel');
        if (panel) {
          const isVisible = panel.style.display !== 'none';
          panel.style.display = isVisible ? 'none' : 'block';
          newBtn.classList.toggle('active', !isVisible);
        }
      });
    }
    
    if (mlHistoryBtn) {
      // Remove existing listeners
      const newBtn = mlHistoryBtn.cloneNode(true);
      mlHistoryBtn.parentNode.replaceChild(newBtn, mlHistoryBtn);
      
      newBtn.addEventListener('click', () => {
        const panel = document.getElementById('compactMLHistoryPanel');
        if (panel) {
          const isVisible = panel.style.display !== 'none';
          panel.style.display = isVisible ? 'none' : 'block';
          newBtn.classList.toggle('active', !isVisible);
        }
      });
    }
  }

  fixMiniChartSizing() {
    console.log('[SystemWiringFix] 📈 Fixing mini chart sizing and alignment...');
    
    const style = document.createElement('style');
    style.id = 'mini-chart-fix';
    style.textContent = `
      /* Mini Chart Sizing Fix */
      .mini-chart-container {
        width: 60px !important;
        height: 30px !important;
        margin: 0 auto !important;
        position: relative !important;
        overflow: hidden !important;
        border: 1px solid rgba(0, 255, 255, 0.3) !important;
        border-radius: 4px !important;
        background: rgba(0, 20, 40, 0.3) !important;
      }
      
      .mini-chart-container canvas {
        width: 100% !important;
        height: 100% !important;
        display: block !important;
      }
      
      /* Indicator name styling */
      .indicator-name {
        font-size: 11px !important;
        color: #00ffff !important;
        font-weight: bold !important;
        text-align: left !important;
        padding: 4px 8px !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
      }
      
      /* Enhanced indicator row layout */
      #momentum-table tr {
        height: 40px !important;
      }
      
      #momentum-table td {
        vertical-align: middle !important;
        padding: 2px 4px !important;
      }
    `;
    
    // Remove existing style if present
    const existingStyle = document.getElementById('mini-chart-fix');
    if (existingStyle) existingStyle.remove();
    
    document.head.appendChild(style);
  }

  fixTooltipSystem() {
    console.log('[SystemWiringFix] 💬 Fixing tooltip system...');
    
    // Remove all title attributes to prevent default tooltips
    document.querySelectorAll('[title]').forEach(element => {
      if (!element.hasAttribute('data-keep-title')) {
        element.removeAttribute('title');
      }
    });

    // Create single tooltip system
    this.createEnhancedTooltipSystem();
  }

  createEnhancedTooltipSystem() {
    // Remove existing tooltips
    document.querySelectorAll('.enhanced-tooltip, #global-tooltip').forEach(tooltip => {
      tooltip.remove();
    });

    // Create new tooltip
    const tooltip = document.createElement('div');
    tooltip.id = 'system-tooltip';
    tooltip.className = 'system-enhanced-tooltip';
    tooltip.style.cssText = `
      position: fixed;
      background: rgba(0, 20, 40, 0.95);
      border: 2px solid rgba(0, 255, 255, 0.6);
      border-radius: 8px;
      padding: 8px 12px;
      color: #ffffff;
      font-family: 'Orbitron', sans-serif;
      font-size: 12px;
      max-width: 250px;
      z-index: 999999;
      pointer-events: none;
      opacity: 0;
      transition: opacity 0.2s ease;
      backdrop-filter: blur(10px);
      box-shadow: 0 8px 32px rgba(0, 255, 255, 0.3);
      display: none;
    `;
    
    document.body.appendChild(tooltip);

    // Add tooltip event handlers
    this.addTooltipEventHandlers(tooltip);
  }

  addTooltipEventHandlers(tooltip) {
    document.addEventListener('mouseover', (e) => {
      const target = e.target.closest('.signal-circle, .circle');
      if (target) {
        const indicator = target.getAttribute('data-ind');
        const timeframe = target.getAttribute('data-tf');
        const color = target.style.backgroundColor;
        
        if (indicator && timeframe) {
          const content = this.generateActionableTooltip(indicator, timeframe, color);
          tooltip.innerHTML = content;
          tooltip.style.display = 'block';
          tooltip.style.opacity = '1';
          this.positionTooltip(tooltip, e);
        }
      }
    });

    document.addEventListener('mouseout', (e) => {
      const target = e.target.closest('.signal-circle, .circle');
      if (target) {
        tooltip.style.opacity = '0';
        setTimeout(() => {
          tooltip.style.display = 'none';
        }, 200);
      }
    });

    document.addEventListener('mousemove', (e) => {
      if (tooltip.style.display === 'block') {
        this.positionTooltip(tooltip, e);
      }
    });
  }

  generateActionableTooltip(indicator, timeframe, color) {
    const signalStrength = this.getSignalStrength(color);
    const actionableAdvice = this.getActionableAdvice(indicator, signalStrength);
    
    return `
      <div style="font-weight: bold; color: #00ffff; margin-bottom: 4px;">
        ${indicator.toUpperCase()} - ${timeframe}
      </div>
      <div style="color: #ffa502; margin-bottom: 4px;">
        ${signalStrength}
      </div>
      <div style="color: #96ceb4; font-size: 11px; font-style: italic;">
        ${actionableAdvice}
      </div>
    `;
  }

  getSignalStrength(color) {
    const rgb = color.toLowerCase();
    if (rgb.includes('255, 0, 0') || rgb.includes('red')) return 'Strong Sell Signal';
    if (rgb.includes('255, 165, 0') || rgb.includes('orange')) return 'Mild Sell Signal';
    if (rgb.includes('128, 128, 128') || rgb.includes('gray')) return 'Neutral Signal';
    if (rgb.includes('0, 0, 255') || rgb.includes('blue')) return 'Mild Buy Signal';
    if (rgb.includes('0, 255, 0') || rgb.includes('green')) return 'Strong Buy Signal';
    return 'Unknown Signal';
  }

  getActionableAdvice(indicator, signalStrength) {
    const advice = {
      'rsi': {
        'Strong Sell Signal': 'Consider taking profits or setting stop losses',
        'Mild Sell Signal': 'Watch for reversal confirmation',
        'Neutral Signal': 'Wait for clearer directional signals',
        'Mild Buy Signal': 'Consider small position entry',
        'Strong Buy Signal': 'Good entry opportunity with proper risk management'
      },
      'macd': {
        'Strong Sell Signal': 'Momentum turning bearish - consider exits',
        'Mild Sell Signal': 'Weakening momentum - reduce exposure',
        'Neutral Signal': 'Momentum unclear - wait for confirmation',
        'Mild Buy Signal': 'Building bullish momentum',
        'Strong Buy Signal': 'Strong momentum - good entry signal'
      }
    };
    
    return advice[indicator]?.[signalStrength] || 'Monitor for trend continuation or reversal';
  }

  positionTooltip(tooltip, event) {
    const tooltipRect = tooltip.getBoundingClientRect();
    let x = event.clientX + 15;
    let y = event.clientY - 10;

    if (x + tooltipRect.width > window.innerWidth) {
      x = event.clientX - tooltipRect.width - 15;
    }
    if (y + tooltipRect.height > window.innerHeight) {
      y = event.clientY - tooltipRect.height - 10;
    }
    if (y < 0) y = event.clientY + 15;
    if (x < 0) x = 10;

    tooltip.style.left = `${x}px`;
    tooltip.style.top = `${y}px`;
  }

  fixStarfieldAnimation() {
    console.log('[SystemWiringFix] 🌟 Fixing starfield animation...');

    // Test starfield functionality
    const canvas = document.getElementById('starfield');
    if (canvas) {
      // Remove any existing starfield instances
      if (window.starfield) {
        delete window.starfield;
      }
      if (window.starfieldAnimation) {
        delete window.starfieldAnimation;
      }

      // Initialize starfield from external file
      if (typeof StarfieldAnimation !== 'undefined') {
        try {
          window.starfieldAnimation = new StarfieldAnimation();
          console.log('[SystemWiringFix] ✅ Starfield animation initialized successfully');
        } catch (error) {
          console.error('[SystemWiringFix] Error initializing starfield:', error);
        }
      } else {
        console.warn('[SystemWiringFix] StarfieldAnimation class not found');
      }
    } else {
      console.warn('[SystemWiringFix] Starfield canvas not found');
    }
  }

  addStrategyHelperSteps() {
    console.log('[SystemWiringFix] 📋 Adding strategy helper steps...');

    const helperSteps = {
      'admiral_toa': [
        '🎯 Monitor all indicators for convergence signals',
        '⚡ Wait for 3+ indicators to align in same direction',
        '🔍 Confirm with volume and momentum indicators',
        '💰 Enter position when confidence >70%',
        '🛡️ Set stop loss at 2-3% below entry',
        '📈 Take profits at resistance levels'
      ],
      'momentum_blast': [
        '🚀 Watch for RSI breaking above 60',
        '📊 Confirm with MACD bullish crossover',
        '💥 Enter on momentum acceleration',
        '⏰ Best during high volume periods',
        '🎯 Target 5-10% quick gains',
        '⚠️ Exit if momentum stalls'
      ],
      'tight_convergence': [
        '🔗 Look for multiple indicators converging',
        '📐 Wait for tight price consolidation',
        '⚡ Enter on breakout with volume',
        '🎯 Target measured move distance',
        '🛡️ Stop loss below consolidation',
        '📈 Scale out at resistance levels'
      ]
    };

    // Update helper text when strategy changes
    document.addEventListener('strategyChanged', (e) => {
      const strategyId = e.detail.strategyId;
      const steps = helperSteps[strategyId];

      if (steps) {
        const helperContainer = document.querySelector('.helper-container, .strategy-helper');
        if (helperContainer) {
          helperContainer.innerHTML = `
            <div class="helper-steps">
              <h4>📋 ${window.TRADING_STRATEGIES[strategyId]?.name || strategyId} Action Steps:</h4>
              <ul>
                ${steps.map(step => `<li>${step}</li>`).join('')}
              </ul>
            </div>
          `;
        }
      }
    });
  }

  enhanceProfessionalPresentation() {
    console.log('[SystemWiringFix] ✨ Enhancing professional presentation...');

    const style = document.createElement('style');
    style.id = 'professional-enhancement';
    style.textContent = `
      /* Professional Enhancement Styles */
      .oracle-matrix {
        background: linear-gradient(135deg, rgba(0, 20, 40, 0.9), rgba(0, 40, 80, 0.7)) !important;
        border: 2px solid rgba(0, 255, 255, 0.4) !important;
        border-radius: 8px !important;
        box-shadow:
          0 0 20px rgba(0, 255, 255, 0.2),
          inset 0 0 20px rgba(0, 255, 255, 0.1) !important;
      }

      .section-header {
        background: linear-gradient(90deg, rgba(0, 255, 255, 0.2), rgba(0, 200, 255, 0.1)) !important;
        border-bottom: 2px solid rgba(0, 255, 255, 0.3) !important;
        text-shadow: 0 0 10px rgba(0, 255, 255, 0.5) !important;
      }

      .menu-button {
        background: linear-gradient(135deg, rgba(0, 50, 100, 0.8), rgba(0, 30, 60, 0.9)) !important;
        border: 1px solid rgba(0, 255, 255, 0.3) !important;
        color: #00ffff !important;
        transition: all 0.3s ease !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
      }

      .menu-button:hover {
        background: linear-gradient(135deg, rgba(0, 100, 200, 0.8), rgba(0, 60, 120, 0.9)) !important;
        border-color: #00ffff !important;
        box-shadow: 0 4px 16px rgba(0, 255, 255, 0.3) !important;
        transform: translateY(-1px) !important;
      }

      .menu-button.active {
        background: linear-gradient(135deg, rgba(0, 255, 255, 0.2), rgba(0, 200, 255, 0.3)) !important;
        border-color: #00ffff !important;
        color: #ffffff !important;
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.5) !important;
      }

      /* Enhanced signal light animations */
      .signal-circle, .circle {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        position: relative !important;
      }

      .signal-circle::after, .circle::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border-radius: 50%;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
      }

      .signal-circle:hover::after, .circle:hover::after {
        opacity: 1;
        box-shadow: 0 0 15px currentColor;
      }

      /* Professional typography */
      .indicator-name {
        font-family: 'Orbitron', 'Roboto Mono', monospace !important;
        letter-spacing: 0.5px !important;
        text-transform: uppercase !important;
      }

      /* Enhanced scrollbars */
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      ::-webkit-scrollbar-track {
        background: rgba(0, 20, 40, 0.5);
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, rgba(0, 255, 255, 0.3), rgba(0, 200, 255, 0.5));
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, rgba(0, 255, 255, 0.5), rgba(0, 200, 255, 0.7));
      }
    `;

    // Remove existing style if present
    const existingStyle = document.getElementById('professional-enhancement');
    if (existingStyle) existingStyle.remove();

    document.head.appendChild(style);
  }

  fixWebSocketErrorHandling() {
    console.log('[SystemWiringFix] 🔌 Fixing WebSocket error handling...');

    // Enhanced WebSocket message validation
    const originalWebSocketHandler = window.handleWebSocketMessage;

    window.handleWebSocketMessage = (message) => {
      try {
        // Validate message structure
        if (!message || typeof message !== 'object') {
          console.warn('[SystemWiringFix] Invalid WebSocket message format');
          return;
        }

        // Validate required fields
        if (!message.type) {
          console.warn('[SystemWiringFix] WebSocket message missing type field');
          return;
        }

        // Validate indicator data
        if (message.type === 'indicators' && message.data) {
          Object.keys(message.data).forEach(timeframe => {
            const tfData = message.data[timeframe];
            Object.keys(tfData).forEach(indicator => {
              if (indicator === 'undefined' || indicator === 'null') {
                console.warn(`[SystemWiringFix] Invalid indicator name: ${indicator}`);
                delete tfData[indicator];
              }
            });
          });
        }

        // Call original handler if validation passes
        if (originalWebSocketHandler) {
          originalWebSocketHandler(message);
        }

      } catch (error) {
        console.error('[SystemWiringFix] Error handling WebSocket message:', error);
      }
    };

    // Add connection stability monitoring
    this.monitorWebSocketStability();
  }

  monitorWebSocketStability() {
    let errorCount = 0;
    const maxErrors = 10;
    const resetInterval = 60000; // 1 minute

    // Reset error count periodically
    setInterval(() => {
      errorCount = 0;
    }, resetInterval);

    // Monitor WebSocket errors
    const originalConsoleError = console.error;
    console.error = (...args) => {
      const message = args.join(' ');
      if (message.includes('WebSocket') || message.includes('indicator is not defined')) {
        errorCount++;

        if (errorCount > maxErrors) {
          console.warn('[SystemWiringFix] High WebSocket error rate detected, implementing throttling...');
          // Implement error throttling here if needed
        }
      }

      originalConsoleError.apply(console, args);
    };
  }
}

// Initialize system wiring fix
document.addEventListener('DOMContentLoaded', () => {
  window.systemWiringFix = new SystemWiringFix();
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SystemWiringFix;
}
