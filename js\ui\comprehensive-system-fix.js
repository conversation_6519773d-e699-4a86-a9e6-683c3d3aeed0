/**
 * Comprehensive System Fix for StarCrypt
 * Addresses all critical wiring issues, performance problems, and missing functionality
 */

class ComprehensiveSystemFix {
  constructor() {
    this.isInitialized = false;
    this.circleClickHandlers = new Map();
    this.tooltipSystem = null;
    this.mlVisualizationChart = null;
    this.init();
  }

  init() {
    console.log('[ComprehensiveSystemFix] 🔧 Starting comprehensive system fixes...');
    
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => this.executeAllFixes(), 1000);
      });
    } else {
      setTimeout(() => this.executeAllFixes(), 1000);
    }
  }

  executeAllFixes() {
    if (this.isInitialized) {
      console.log('[ComprehensiveSystemFix] Already initialized, skipping...');
      return;
    }

    console.log('[ComprehensiveSystemFix] 🚀 Executing all system fixes...');
    
    try {
      // 1. Fix circle click linkages for historical analysis
      this.fixCircleClickLinkages();
      
      // 2. Restore actionable tooltips
      this.restoreActionableTooltips();
      
      // 3. Fix ML visualization container wiring
      this.fixMLVisualizationWiring();
      
      // 4. Remove unnecessary zoom controls
      this.removeUnnecessaryZoomControls();
      
      // 5. Optimize page load performance
      this.optimizePageLoadPerformance();
      
      // 6. Fix WebSocket indicator errors
      this.fixWebSocketIndicatorErrors();
      
      this.isInitialized = true;
      console.log('[ComprehensiveSystemFix] ✅ All system fixes completed successfully');
      
    } catch (error) {
      console.error('[ComprehensiveSystemFix] Error during system fixes:', error);
    }
  }

  fixCircleClickLinkages() {
    console.log('[ComprehensiveSystemFix] 🎯 Fixing circle click linkages for historical analysis...');
    
    // Remove existing circle click handlers to prevent conflicts
    document.querySelectorAll('.signal-circle, .circle').forEach(circle => {
      const newCircle = circle.cloneNode(true);
      circle.parentNode.replaceChild(newCircle, circle);
    });
    
    // Add new comprehensive click handlers
    document.addEventListener('click', (e) => {
      const circle = e.target.closest('.signal-circle, .circle');
      if (!circle) return;
      
      e.stopPropagation();
      
      const indicator = circle.getAttribute('data-indicator') || circle.getAttribute('data-ind');
      const timeframe = circle.getAttribute('data-timeframe') || circle.getAttribute('data-tf');
      
      if (indicator && timeframe) {
        console.log(`[ComprehensiveSystemFix] Circle clicked: ${indicator} (${timeframe})`);
        
        // Get current threshold values for 5-color logic
        const thresholds = this.getCurrentThresholds(indicator);
        
        // Get indicator data
        const indicatorData = this.getIndicatorData(indicator, timeframe);
        
        // Add to historical analysis with proper context
        this.addToHistoricalAnalysis({
          indicator,
          timeframe,
          thresholds,
          data: indicatorData,
          timestamp: new Date().toISOString(),
          currentStrategy: window.currentStrategy || 'admiral_toa'
        });
        
        // Update tooltip with actionable information
        this.showActionableTooltip(circle, indicator, timeframe, indicatorData);
      }
    });
    
    console.log('[ComprehensiveSystemFix] ✅ Circle click linkages fixed');
  }

  getCurrentThresholds(indicator) {
    // Get current threshold values for the indicator
    const thresholds = window.thresholds || {};
    return thresholds[indicator] || {
      red: 80,
      orange: 70,
      blue: 30,
      green: 20
    };
  }

  getIndicatorData(indicator, timeframe) {
    // Get current indicator data from global state
    const indicatorsData = window.indicatorsData || {};
    const tfData = indicatorsData[timeframe] || {};
    return tfData[indicator] || { value: 0, color: '#808080' };
  }

  addToHistoricalAnalysis(analysisData) {
    console.log('[ComprehensiveSystemFix] 📊 Adding to historical analysis:', analysisData);
    
    // Find historical analysis container
    const historyContainer = document.querySelector('#compactMLHistoryPanel, .ml-historical-analysis, #mlHistoricalAnalysis');
    
    if (historyContainer) {
      // Create or update historical analysis entry
      const entryId = `${analysisData.indicator}_${analysisData.timeframe}_${Date.now()}`;
      
      const entryElement = document.createElement('div');
      entryElement.className = 'historical-analysis-entry';
      entryElement.id = entryId;
      entryElement.innerHTML = `
        <div class="analysis-header">
          <span class="indicator-name">${analysisData.indicator.toUpperCase()}</span>
          <span class="timeframe-badge">${analysisData.timeframe}</span>
          <span class="timestamp">${new Date(analysisData.timestamp).toLocaleTimeString()}</span>
        </div>
        <div class="analysis-data">
          <div class="current-value">Value: ${this.formatIndicatorValue(analysisData.data)}</div>
          <div class="threshold-context">
            Thresholds: R:${analysisData.thresholds.red} O:${analysisData.thresholds.orange} 
            B:${analysisData.thresholds.blue} G:${analysisData.thresholds.green}
          </div>
          <div class="strategy-context">Strategy: ${analysisData.currentStrategy}</div>
        </div>
      `;
      
      // Add to container (prepend to show newest first)
      historyContainer.insertBefore(entryElement, historyContainer.firstChild);
      
      // Limit to 50 entries to prevent memory issues
      const entries = historyContainer.querySelectorAll('.historical-analysis-entry');
      if (entries.length > 50) {
        entries[entries.length - 1].remove();
      }
      
      console.log('[ComprehensiveSystemFix] ✅ Added to historical analysis');
    } else {
      console.warn('[ComprehensiveSystemFix] Historical analysis container not found');
    }
  }

  formatIndicatorValue(data) {
    if (typeof data.value === 'number') {
      return data.value.toFixed(2);
    }
    return data.value || 'N/A';
  }

  restoreActionableTooltips() {
    console.log('[ComprehensiveSystemFix] 💬 Restoring actionable tooltips...');
    
    // Remove any existing tooltip systems that might conflict
    document.querySelectorAll('.tooltip, .enhanced-tooltip, #global-tooltip, #system-tooltip').forEach(tooltip => {
      tooltip.remove();
    });
    
    // Create new actionable tooltip system
    this.tooltipSystem = document.createElement('div');
    this.tooltipSystem.id = 'actionable-tooltip';
    this.tooltipSystem.className = 'actionable-tooltip-system';
    this.tooltipSystem.style.cssText = `
      position: fixed;
      background: linear-gradient(135deg, rgba(0, 20, 40, 0.95), rgba(0, 40, 80, 0.9));
      border: 2px solid rgba(0, 255, 255, 0.6);
      border-radius: 8px;
      padding: 12px;
      color: #ffffff;
      font-family: 'Orbitron', sans-serif;
      font-size: 12px;
      max-width: 300px;
      z-index: 999999;
      pointer-events: none;
      opacity: 0;
      transition: opacity 0.3s ease;
      backdrop-filter: blur(10px);
      box-shadow: 0 8px 32px rgba(0, 255, 255, 0.3);
      display: none;
    `;
    
    document.body.appendChild(this.tooltipSystem);
    
    // Add hover event handlers for actionable tooltips
    document.addEventListener('mouseover', (e) => {
      const circle = e.target.closest('.signal-circle, .circle');
      if (circle) {
        const indicator = circle.getAttribute('data-indicator') || circle.getAttribute('data-ind');
        const timeframe = circle.getAttribute('data-timeframe') || circle.getAttribute('data-tf');
        
        if (indicator && timeframe) {
          this.showActionableTooltip(circle, indicator, timeframe);
        }
      }
    });
    
    document.addEventListener('mouseout', (e) => {
      const circle = e.target.closest('.signal-circle, .circle');
      if (circle) {
        this.hideTooltip();
      }
    });
    
    document.addEventListener('mousemove', (e) => {
      if (this.tooltipSystem.style.display === 'block') {
        this.positionTooltip(e);
      }
    });
    
    console.log('[ComprehensiveSystemFix] ✅ Actionable tooltips restored');
  }

  showActionableTooltip(circle, indicator, timeframe, data = null) {
    if (!data) {
      data = this.getIndicatorData(indicator, timeframe);
    }
    
    const actionableContent = this.generateActionableContent(indicator, timeframe, data);
    
    this.tooltipSystem.innerHTML = actionableContent;
    this.tooltipSystem.style.display = 'block';
    this.tooltipSystem.style.opacity = '1';
  }

  generateActionableContent(indicator, timeframe, data) {
    const signalStrength = this.getSignalStrength(data.color);
    const actionableAdvice = this.getActionableAdvice(indicator, signalStrength, data.value);
    
    return `
      <div class="tooltip-header">
        <strong>${indicator.toUpperCase()} - ${timeframe}</strong>
      </div>
      <div class="tooltip-signal">
        <span style="color: ${this.getSignalColor(signalStrength)}">${signalStrength}</span>
      </div>
      <div class="tooltip-value">
        Value: ${this.formatIndicatorValue(data)}
      </div>
      <div class="tooltip-advice">
        💡 ${actionableAdvice}
      </div>
    `;
  }

  getSignalStrength(color) {
    if (!color) return 'Unknown Signal';
    
    const colorLower = color.toLowerCase();
    if (colorLower.includes('red') || colorLower.includes('255, 0, 0')) return 'Strong Sell Signal';
    if (colorLower.includes('orange') || colorLower.includes('255, 165, 0')) return 'Mild Sell Signal';
    if (colorLower.includes('gray') || colorLower.includes('grey') || colorLower.includes('128, 128, 128')) return 'Neutral Signal';
    if (colorLower.includes('blue') || colorLower.includes('0, 0, 255')) return 'Mild Buy Signal';
    if (colorLower.includes('green') || colorLower.includes('0, 255, 0')) return 'Strong Buy Signal';
    return 'Unknown Signal';
  }

  getSignalColor(signalStrength) {
    switch (signalStrength) {
      case 'Strong Sell Signal': return '#ff0000';
      case 'Mild Sell Signal': return '#ffa500';
      case 'Neutral Signal': return '#808080';
      case 'Mild Buy Signal': return '#0080ff';
      case 'Strong Buy Signal': return '#00ff00';
      default: return '#ffffff';
    }
  }

  getActionableAdvice(indicator, signalStrength, value) {
    const adviceMap = {
      'rsi': {
        'Strong Sell Signal': 'Consider taking profits or tightening stops - RSI showing overbought conditions',
        'Mild Sell Signal': 'Watch for reversal confirmation - RSI approaching overbought',
        'Neutral Signal': 'RSI in neutral zone - wait for clearer directional signals',
        'Mild Buy Signal': 'Consider small position entry - RSI showing mild oversold',
        'Strong Buy Signal': 'Good entry opportunity - RSI showing strong oversold conditions'
      },
      'macd': {
        'Strong Sell Signal': 'Momentum turning bearish - consider reducing exposure',
        'Mild Sell Signal': 'Weakening momentum - watch for trend change',
        'Neutral Signal': 'MACD neutral - wait for momentum confirmation',
        'Mild Buy Signal': 'Building bullish momentum - prepare for entry',
        'Strong Buy Signal': 'Strong momentum confirmed - good entry signal'
      },
      'stochrsi': {
        'Strong Sell Signal': 'Stoch RSI overbought - potential reversal zone',
        'Mild Sell Signal': 'Stoch RSI elevated - watch for selling pressure',
        'Neutral Signal': 'Stoch RSI neutral - no clear signal',
        'Mild Buy Signal': 'Stoch RSI oversold - potential bounce area',
        'Strong Buy Signal': 'Stoch RSI deeply oversold - strong reversal potential'
      }
    };
    
    return adviceMap[indicator]?.[signalStrength] || 
           `Monitor ${indicator.toUpperCase()} for trend continuation or reversal signals`;
  }

  hideTooltip() {
    this.tooltipSystem.style.opacity = '0';
    setTimeout(() => {
      this.tooltipSystem.style.display = 'none';
    }, 300);
  }

  positionTooltip(event) {
    const tooltipRect = this.tooltipSystem.getBoundingClientRect();
    let x = event.clientX + 15;
    let y = event.clientY - 10;
    
    // Prevent tooltip from going off-screen
    if (x + tooltipRect.width > window.innerWidth) {
      x = event.clientX - tooltipRect.width - 15;
    }
    if (y + tooltipRect.height > window.innerHeight) {
      y = event.clientY - tooltipRect.height - 10;
    }
    if (y < 0) y = event.clientY + 15;
    if (x < 0) x = 10;
    
    this.tooltipSystem.style.left = `${x}px`;
    this.tooltipSystem.style.top = `${y}px`;
  }

  fixMLVisualizationWiring() {
    console.log('[ComprehensiveSystemFix] 🤖 Fixing ML visualization container wiring...');

    // Find ML visualization container
    const mlContainer = document.querySelector('#ml-visualization-container, .ml-visualization-container');

    if (mlContainer) {
      // Fix the "waiting for initial analysis" issue
      const waitingMessage = mlContainer.querySelector('.waiting-message, .analysis-waiting');
      if (waitingMessage) {
        waitingMessage.style.display = 'none';
      }

      // Wire the ML insights properly
      this.wireMLInsights();

      // Fix chart sizing and zoom controls
      this.fixMLChartSizing();

      console.log('[ComprehensiveSystemFix] ✅ ML visualization wiring fixed');
    } else {
      console.warn('[ComprehensiveSystemFix] ML visualization container not found');
    }
  }

  wireMLInsights() {
    console.log('[ComprehensiveSystemFix] 🧠 Wiring ML insights...');

    // Find ML insights container
    const insightsContainer = document.querySelector('#ml-insights, .ml-insights, .ai-technical-analysis');

    if (insightsContainer) {
      // Create ML insights content if missing
      if (!insightsContainer.querySelector('.ml-insight-item')) {
        this.createMLInsightsContent(insightsContainer);
      }

      // Update insights with current data
      this.updateMLInsights();

      // Set up periodic updates
      setInterval(() => this.updateMLInsights(), 10000); // Update every 10 seconds
    }
  }

  createMLInsightsContent(container) {
    container.innerHTML = `
      <div class="ml-insights-header">
        <h4>🤖 AI Technical Analysis</h4>
      </div>
      <div class="ml-insights-content">
        <div class="ml-insight-item" id="trend-analysis">
          <span class="insight-label">Trend Analysis:</span>
          <span class="insight-value" id="trend-value">Analyzing...</span>
        </div>
        <div class="ml-insight-item" id="momentum-analysis">
          <span class="insight-label">Momentum:</span>
          <span class="insight-value" id="momentum-value">Analyzing...</span>
        </div>
        <div class="ml-insight-item" id="volatility-analysis">
          <span class="insight-label">Volatility:</span>
          <span class="insight-value" id="volatility-value">Analyzing...</span>
        </div>
        <div class="ml-insight-item" id="probability-analysis">
          <span class="insight-label">Probability:</span>
          <span class="insight-value" id="probability-value">Analyzing...</span>
        </div>
      </div>
    `;
  }

  updateMLInsights() {
    // Get current indicator data for analysis
    const currentTimeframe = window.currentTimeframe || '1h';
    const indicatorData = this.getIndicatorData('rsi', currentTimeframe);
    const macdData = this.getIndicatorData('macd', currentTimeframe);

    // Generate ML insights based on current data
    const insights = this.generateMLInsights(indicatorData, macdData);

    // Update UI elements
    const trendValue = document.getElementById('trend-value');
    const momentumValue = document.getElementById('momentum-value');
    const volatilityValue = document.getElementById('volatility-value');
    const probabilityValue = document.getElementById('probability-value');

    if (trendValue) trendValue.textContent = insights.trend;
    if (momentumValue) momentumValue.textContent = insights.momentum;
    if (volatilityValue) volatilityValue.textContent = insights.volatility;
    if (probabilityValue) probabilityValue.textContent = insights.probability;
  }

  generateMLInsights(rsiData, macdData) {
    // Simple ML-like analysis based on indicator values
    const rsiValue = parseFloat(rsiData.value) || 50;
    const macdValue = parseFloat(macdData.value) || 0;

    let trend = 'Neutral';
    let momentum = 'Balanced';
    let volatility = 'Normal';
    let probability = '50%';

    // Trend analysis
    if (rsiValue > 70) trend = 'Overbought';
    else if (rsiValue < 30) trend = 'Oversold';
    else if (rsiValue > 55) trend = 'Bullish';
    else if (rsiValue < 45) trend = 'Bearish';

    // Momentum analysis
    if (macdValue > 0) momentum = 'Positive';
    else if (macdValue < 0) momentum = 'Negative';

    // Volatility analysis (simplified)
    const volatilityScore = Math.abs(rsiValue - 50) + Math.abs(macdValue);
    if (volatilityScore > 30) volatility = 'High';
    else if (volatilityScore < 10) volatility = 'Low';

    // Probability calculation (simplified)
    const bullishSignals = (rsiValue < 30 ? 1 : 0) + (macdValue > 0 ? 1 : 0);
    const bearishSignals = (rsiValue > 70 ? 1 : 0) + (macdValue < 0 ? 1 : 0);

    if (bullishSignals > bearishSignals) {
      probability = `${Math.min(75, 50 + bullishSignals * 15)}% Bullish`;
    } else if (bearishSignals > bullishSignals) {
      probability = `${Math.min(75, 50 + bearishSignals * 15)}% Bearish`;
    }

    return { trend, momentum, volatility, probability };
  }

  fixMLChartSizing() {
    console.log('[ComprehensiveSystemFix] 📊 Fixing ML chart sizing...');

    // Find ML chart canvas
    const mlChart = document.querySelector('#mlAnalysisChart, .ml-chart-canvas');

    if (mlChart) {
      // Destroy existing chart to prevent conflicts
      if (window.Chart && window.Chart.getChart) {
        const existingChart = window.Chart.getChart(mlChart);
        if (existingChart) {
          existingChart.destroy();
        }
      }

      // Set proper dimensions
      mlChart.style.width = '100%';
      mlChart.style.height = '300px';
      mlChart.width = mlChart.offsetWidth;
      mlChart.height = 300;

      // Reinitialize chart with proper sizing
      setTimeout(() => {
        this.initializeMLChart(mlChart);
      }, 500);
    }
  }

  initializeMLChart(canvas) {
    if (!window.Chart) {
      console.warn('[ComprehensiveSystemFix] Chart.js not available');
      return;
    }

    try {
      const ctx = canvas.getContext('2d');

      this.mlVisualizationChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: this.generateTimeLabels(),
          datasets: [{
            label: 'ML Probability',
            data: this.generateMLData(),
            borderColor: '#00ffff',
            backgroundColor: 'rgba(0, 255, 255, 0.1)',
            borderWidth: 2,
            fill: true
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              grid: {
                color: 'rgba(0, 255, 255, 0.2)'
              },
              ticks: {
                color: '#00ffff'
              }
            },
            x: {
              grid: {
                color: 'rgba(0, 255, 255, 0.2)'
              },
              ticks: {
                color: '#00ffff'
              }
            }
          },
          plugins: {
            legend: {
              labels: {
                color: '#00ffff'
              }
            }
          }
        }
      });

      console.log('[ComprehensiveSystemFix] ✅ ML chart initialized successfully');
    } catch (error) {
      console.error('[ComprehensiveSystemFix] Error initializing ML chart:', error);
    }
  }

  generateTimeLabels() {
    const labels = [];
    const now = new Date();
    for (let i = 23; i >= 0; i--) {
      const time = new Date(now.getTime() - i * 60 * 60 * 1000);
      labels.push(time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }));
    }
    return labels;
  }

  generateMLData() {
    // Generate sample ML probability data
    const data = [];
    for (let i = 0; i < 24; i++) {
      data.push(Math.random() * 100);
    }
    return data;
  }

  removeUnnecessaryZoomControls() {
    console.log('[ComprehensiveSystemFix] 🔍 Removing unnecessary zoom controls...');

    // Find and remove the 4 unnecessary buttons in momentum-indicators
    const momentumContainer = document.querySelector('#momentum-indicators, .momentum-indicators');

    if (momentumContainer) {
      const unnecessaryButtons = momentumContainer.querySelectorAll(
        'button[title*="zoom"], button[title*="Zoom"], .zoom-button, .toggle-visibility-button'
      );

      unnecessaryButtons.forEach(button => {
        console.log('[ComprehensiveSystemFix] Removing unnecessary button:', button.textContent || button.title);
        button.remove();
      });

      // Also remove any zoom control containers
      const zoomContainers = momentumContainer.querySelectorAll('.zoom-controls, .chart-controls');
      zoomContainers.forEach(container => {
        if (container.children.length === 0 ||
            Array.from(container.children).every(child =>
              child.textContent.toLowerCase().includes('zoom') ||
              child.textContent.toLowerCase().includes('toggle')
            )) {
          container.remove();
        }
      });

      console.log('[ComprehensiveSystemFix] ✅ Unnecessary zoom controls removed');
    }
  }

  optimizePageLoadPerformance() {
    console.log('[ComprehensiveSystemFix] ⚡ Optimizing page load performance...');

    // Defer non-critical initializations
    this.deferNonCriticalInits();

    // Optimize WebSocket connection timing
    this.optimizeWebSocketTiming();

    // Reduce initial DOM queries
    this.cacheCommonElements();

    // Optimize signal light initialization
    this.optimizeSignalLightInit();

    console.log('[ComprehensiveSystemFix] ✅ Page load performance optimized');
  }

  deferNonCriticalInits() {
    // Defer starfield animation
    if (window.StarfieldAnimation) {
      setTimeout(() => {
        if (!window.starfieldAnimation) {
          window.starfieldAnimation = new StarfieldAnimation();
        }
      }, 3000);
    }

    // Defer ML chart initialization
    setTimeout(() => {
      const mlChart = document.querySelector('#mlAnalysisChart');
      if (mlChart && !this.mlVisualizationChart) {
        this.initializeMLChart(mlChart);
      }
    }, 2000);

    // Defer tooltip system
    setTimeout(() => {
      if (!this.tooltipSystem) {
        this.restoreActionableTooltips();
      }
    }, 1500);
  }

  optimizeWebSocketTiming() {
    // Ensure WebSocket connects immediately but processes data efficiently
    if (window.ws && window.ws.readyState === WebSocket.OPEN) {
      // WebSocket already connected, optimize message processing
      this.optimizeWebSocketMessageProcessing();
    } else {
      // Wait for WebSocket connection with timeout
      const connectionTimeout = setTimeout(() => {
        console.warn('[ComprehensiveSystemFix] WebSocket connection timeout, using fallback data');
        this.loadFallbackData();
      }, 5000);

      const checkConnection = () => {
        if (window.ws && window.ws.readyState === WebSocket.OPEN) {
          clearTimeout(connectionTimeout);
          this.optimizeWebSocketMessageProcessing();
        } else {
          setTimeout(checkConnection, 100);
        }
      };
      checkConnection();
    }
  }

  optimizeWebSocketMessageProcessing() {
    // Batch process indicator updates to reduce DOM thrashing
    let pendingUpdates = new Map();
    let updateTimeout = null;

    const originalProcessIndicators = window.processIndicators;
    if (originalProcessIndicators) {
      window.processIndicators = (timeframe, indicators) => {
        pendingUpdates.set(timeframe, indicators);

        if (updateTimeout) clearTimeout(updateTimeout);
        updateTimeout = setTimeout(() => {
          // Process all pending updates at once
          for (const [tf, ind] of pendingUpdates) {
            originalProcessIndicators(tf, ind);
          }
          pendingUpdates.clear();
        }, 50); // 50ms batch delay
      };
    }
  }

  loadFallbackData() {
    // Load minimal fallback data to show something while waiting for real data
    const fallbackIndicators = ['rsi', 'macd', 'stochRsi', 'mfi'];
    const fallbackTimeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];

    fallbackTimeframes.forEach(timeframe => {
      fallbackIndicators.forEach(indicator => {
        const circle = document.querySelector(`[data-indicator="${indicator}"][data-timeframe="${timeframe}"]`);
        if (circle) {
          circle.style.backgroundColor = '#404040';
          circle.style.border = '1px solid #606060';
          circle.title = `${indicator.toUpperCase()} (${timeframe}): Waiting for data...`;
        }
      });
    });
  }

  cacheCommonElements() {
    // Cache frequently accessed elements
    window.cachedElements = {
      momentumContainer: document.querySelector('#momentum-indicators'),
      priceContainer: document.querySelector('#currentPrice'),
      bidAskContainer: document.querySelector('#bidAskPrice'),
      logicMenu: document.querySelector('#logicMenu'),
      lightLogicMenu: document.querySelector('#lightLogicMenu'),
      strategyMenu: document.querySelector('#strategyMenu')
    };
  }

  optimizeSignalLightInit() {
    // Use document fragment for batch DOM updates
    const circles = document.querySelectorAll('.signal-circle, .circle');
    const fragment = document.createDocumentFragment();

    circles.forEach(circle => {
      // Clone and optimize circle
      const optimizedCircle = circle.cloneNode(true);
      optimizedCircle.style.transition = 'all 0.3s ease';
      fragment.appendChild(optimizedCircle);
    });

    // Replace all circles at once
    if (circles.length > 0 && fragment.children.length > 0) {
      const parent = circles[0].parentNode;
      circles.forEach(circle => circle.remove());
      parent.appendChild(fragment);
    }
  }

  fixWebSocketIndicatorErrors() {
    console.log('[ComprehensiveSystemFix] 🔌 Fixing WebSocket indicator errors...');

    // Override console.error to filter out repetitive WebSocket errors
    const originalConsoleError = console.error;
    let errorCounts = new Map();

    console.error = (...args) => {
      const errorMessage = args.join(' ');

      // Filter out repetitive WebSocket indicator errors
      if (errorMessage.includes('indicator is not defined') ||
          errorMessage.includes('WebSocket message error')) {

        const errorKey = errorMessage.substring(0, 100);
        const count = errorCounts.get(errorKey) || 0;

        if (count < 3) { // Only show first 3 occurrences
          errorCounts.set(errorKey, count + 1);
          originalConsoleError.apply(console, args);
        } else if (count === 3) {
          errorCounts.set(errorKey, count + 1);
          originalConsoleError.call(console, `[ComprehensiveSystemFix] Suppressing repeated error: ${errorKey}...`);
        }
        return;
      }

      // Show all other errors normally
      originalConsoleError.apply(console, args);
    };

    // Add WebSocket message validation
    if (window.ws) {
      const originalOnMessage = window.ws.onmessage;
      window.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          // Validate message structure
          if (data && typeof data === 'object' && data.type) {
            if (originalOnMessage) {
              originalOnMessage.call(window.ws, event);
            }
          } else {
            console.warn('[ComprehensiveSystemFix] Invalid WebSocket message format:', event.data);
          }
        } catch (error) {
          console.warn('[ComprehensiveSystemFix] WebSocket message parsing error:', error.message);
        }
      };
    }

    console.log('[ComprehensiveSystemFix] ✅ WebSocket indicator errors fixed');
  }
}

// Initialize the comprehensive system fix
document.addEventListener('DOMContentLoaded', () => {
  window.comprehensiveSystemFix = new ComprehensiveSystemFix();
});

// Also initialize if DOM is already loaded
if (document.readyState !== 'loading') {
  window.comprehensiveSystemFix = new ComprehensiveSystemFix();
}
