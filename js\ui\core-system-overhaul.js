/**
 * Core System Overhaul - Complete fix for all fundamental issues
 * This replaces all the broken modular fixes with a single, working system
 */

class CoreSystemOverhaul {
  constructor() {
    this.isInitialized = false;
    this.circleClickHandler = null;
    this.miniCharts = new Map();
    this.thresholdSliders = new Map();
    this.matrixContainer = null;
    
    // Bind methods
    this.handleCircleClick = this.handleCircleClick.bind(this);
    
    this.init();
  }

  init() {
    console.log('[CoreSystemOverhaul] 🚀 Starting complete system overhaul...');
    
    // Wait for DOM and WebSocket
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.executeOverhaul());
    } else {
      this.executeOverhaul();
    }
  }

  executeOverhaul() {
    if (this.isInitialized) return;

    console.log('[CoreSystemOverhaul] 🔧 Executing complete system overhaul...');

    try {
      // 1. Remove all zoom buttons permanently
      this.removeAllZoomButtons();

      // 2. Fix matrix structure and alignment
      this.fixMatrixStructure();

      // 3. Fix signal lights alignment to right
      this.fixSignalLightsAlignment();

      // 4. Fix AI chart sizing and visibility
      this.fixAIChartSizing();

      // 5. Fix circle click handlers (remove all conflicts)
      this.fixCircleClickHandlers();

      // 6. Fix mini charts completely
      this.fixMiniCharts();

      // 7. Fix threshold sliders completely (proper 5-color system)
      this.fixThresholdSliders();

      // 8. Wire ML/AI linkage properly
      this.wireMLAILinkage();

      // 9. Wire historical analysis properly
      this.wireHistoricalAnalysis();

      // 10. Apply professional styling
      this.applyProfessionalStyling();

      this.isInitialized = true;
      console.log('[CoreSystemOverhaul] ✅ Complete system overhaul successful');

    } catch (error) {
      console.error('[CoreSystemOverhaul] ❌ System overhaul failed:', error);
    }
  }

  fixMatrixStructure() {
    console.log('[CoreSystemOverhaul] 📊 Working with existing matrix structure...');

    // Find the momentum table - DON'T rebuild it, just enhance it
    this.matrixContainer = document.querySelector('#momentum-indicators');
    if (!this.matrixContainer) {
      console.error('[CoreSystemOverhaul] Momentum indicators container not found');
      return;
    }

    // The table already exists and is managed by the main system
    // We just need to ensure proper styling and functionality
    const table = this.matrixContainer.querySelector('#momentum-table');
    if (table) {
      // Add our enhancement class without disrupting existing structure
      table.classList.add('enhanced-matrix-table');

      // Remove any unnecessary headers that might have been added
      const headers = table.querySelectorAll('th');
      headers.forEach(header => {
        if (header.textContent.includes('Indicator') ||
            header.textContent.includes('Chart') ||
            header.textContent.includes('1m') ||
            header.textContent.includes('5m')) {
          header.parentElement?.remove();
        }
      });
    }

    console.log('[CoreSystemOverhaul] ✅ Matrix structure enhanced (not rebuilt)');
  }

  removeAllZoomButtons() {
    console.log('[CoreSystemOverhaul] 🗑️ Removing ALL zoom buttons permanently...');

    // Find all possible zoom button selectors
    const zoomSelectors = [
      'button[id*="zoom"]',
      'button[class*="zoom"]',
      'button[title*="zoom"]',
      'button[title*="Zoom"]',
      '.zoom-in-btn',
      '.zoom-out-btn',
      '.reset-zoom-btn',
      '.toggle-visibility-btn',
      '.chart-control-btn',
      '#ml-zoom-in',
      '#ml-zoom-out',
      '#ml-reset-zoom',
      '#ml-toggle-visibility',
      'button[onclick*="zoom"]',
      'button[onclick*="toggle"]'
    ];

    zoomSelectors.forEach(selector => {
      document.querySelectorAll(selector).forEach(button => {
        console.log('[CoreSystemOverhaul] Removing zoom button:', button.textContent || button.id || button.className);
        button.remove();
      });
    });

    // Remove zoom control containers
    document.querySelectorAll('.zoom-controls, .chart-controls, .ml-control-group, #ml-controls').forEach(container => {
      console.log('[CoreSystemOverhaul] Removing zoom container:', container.className || container.id);
      container.remove();
    });

    console.log('[CoreSystemOverhaul] ✅ All zoom buttons removed');
  }

  fixSignalLightsAlignment() {
    console.log('[CoreSystemOverhaul] ➡️ Fixing signal lights alignment to right...');

    // Find all signal light cells and align them to the right
    const signalCells = document.querySelectorAll('#momentum-table td:nth-child(n+3)');
    signalCells.forEach(cell => {
      cell.style.textAlign = 'right';
      cell.style.paddingRight = '10px';

      // Also align the signal circles within the cells
      const circles = cell.querySelectorAll('.signal-circle, .circle');
      circles.forEach(circle => {
        circle.style.marginLeft = 'auto';
        circle.style.marginRight = '0';
      });
    });

    console.log('[CoreSystemOverhaul] ✅ Signal lights aligned to right');
  }

  fixAIChartSizing() {
    console.log('[CoreSystemOverhaul] 📊 Fixing AI chart sizing and visibility...');

    // Find ML analysis container
    const mlContainer = document.querySelector('.ml-analysis-container');
    if (mlContainer) {
      // Fix container sizing
      mlContainer.style.cssText = `
        margin-top: 20px;
        padding: 15px;
        background: rgba(0, 20, 40, 0.8);
        border: 1px solid rgba(0, 255, 255, 0.3);
        border-radius: 8px;
        width: 100%;
        box-sizing: border-box;
      `;

      // Fix chart container
      const chartContainer = mlContainer.querySelector('.chart-container');
      if (chartContainer) {
        chartContainer.style.cssText = `
          position: relative;
          height: 300px;
          width: 100%;
          overflow: visible;
        `;

        // Fix canvas
        const canvas = chartContainer.querySelector('#mlAnalysisChart');
        if (canvas) {
          canvas.style.cssText = `
            width: 100% !important;
            height: 100% !important;
            display: block;
          `;
          canvas.width = chartContainer.offsetWidth;
          canvas.height = 300;
        }
      }
    }

    console.log('[CoreSystemOverhaul] ✅ AI chart sizing fixed');
  }

  getCurrentStrategyIndicators() {
    // Get indicators from current strategy or use defaults
    const currentStrategy = window.currentStrategy || 'momentum_blast';
    const strategyConfig = window.TRADING_STRATEGIES?.[currentStrategy];

    if (strategyConfig && strategyConfig.indicators) {
      return strategyConfig.indicators;
    }

    // Default indicators
    return ['rsi', 'stochRsi', 'macd', 'mfi', 'volume'];
  }

  fixCircleClickHandlers() {
    console.log('[CoreSystemOverhaul] 🎯 Fixing circle click handlers...');
    
    // Remove ALL existing click handlers by cloning elements
    document.querySelectorAll('.signal-circle').forEach(circle => {
      const newCircle = circle.cloneNode(true);
      circle.parentNode.replaceChild(newCircle, circle);
    });
    
    // Remove any existing document-level click handlers
    if (this.circleClickHandler) {
      document.removeEventListener('click', this.circleClickHandler);
    }
    
    // Add single, unified click handler
    this.circleClickHandler = this.handleCircleClick;
    document.addEventListener('click', this.circleClickHandler);
    
    console.log('[CoreSystemOverhaul] ✅ Circle click handlers fixed');
  }

  handleCircleClick(event) {
    const circle = event.target.closest('.signal-circle');
    if (!circle) return;
    
    event.stopPropagation();
    event.preventDefault();
    
    const indicator = circle.getAttribute('data-indicator');
    const timeframe = circle.getAttribute('data-timeframe');
    
    if (!indicator || !timeframe) {
      console.warn('[CoreSystemOverhaul] Circle missing data attributes:', circle);
      return;
    }
    
    console.log(`[CoreSystemOverhaul] Circle clicked: ${indicator} (${timeframe})`);
    
    // Get current data
    const indicatorData = this.getIndicatorData(indicator, timeframe);
    const thresholds = this.getCurrentThresholds(indicator);
    
    // Add to historical analysis
    this.addToHistoricalAnalysis({
      indicator,
      timeframe,
      value: indicatorData.value,
      color: indicatorData.color,
      thresholds,
      timestamp: new Date().toISOString(),
      strategy: window.currentStrategy || 'unknown'
    });
    
    // Show tooltip
    this.showActionableTooltip(circle, indicator, timeframe, indicatorData);
    
    // Dispatch custom event for other systems
    document.dispatchEvent(new CustomEvent('signalCircleClicked', {
      detail: { indicator, timeframe, element: circle, data: indicatorData }
    }));
  }

  getIndicatorData(indicator, timeframe) {
    // Get data from global state
    const tfData = window.indicatorsData?.[timeframe] || {};
    const data = tfData[indicator] || {};
    
    return {
      value: data.value || data.current || 0,
      color: data.color || '#808080',
      signal: data.signal || 'neutral',
      strength: data.strength || 0.5
    };
  }

  getCurrentThresholds(indicator) {
    const thresholds = window.thresholds || {};
    return thresholds[indicator] || {
      red: 80,
      orange: 70,
      blue: 30,
      green: 20
    };
  }

  fixMiniCharts() {
    console.log('[CoreSystemOverhaul] 📈 Fixing existing mini charts...');

    // Find all existing mini chart elements and fix their styling
    const existingCharts = document.querySelectorAll('[id*="Chart"], .mini-chart, canvas[id*="chart"]');

    existingCharts.forEach(chart => {
      // Fix height and container issues
      chart.style.height = '40px';
      chart.style.maxHeight = '40px';
      chart.style.minHeight = '40px';

      if (chart.tagName === 'CANVAS') {
        chart.height = 40;
      }

      // Fix parent container if it exists
      const parentCell = chart.closest('td');
      if (parentCell) {
        parentCell.style.height = '50px';
        parentCell.style.verticalAlign = 'middle';
        parentCell.style.padding = '5px';
        parentCell.style.position = 'relative';
        parentCell.style.overflow = 'hidden';
      }

      // Remove any data overlays that might be covering the chart
      const dataOverlays = chart.parentElement?.querySelectorAll('.data-overlay, .chart-data, .indicator-data');
      dataOverlays?.forEach(overlay => {
        if (overlay.style.position === 'absolute' || overlay.style.zIndex > 1) {
          overlay.style.display = 'none';
        }
      });
    });

    console.log('[CoreSystemOverhaul] ✅ Existing mini charts fixed');
  }



  fixThresholdSliders() {
    console.log('[CoreSystemOverhaul] 🎚️ Creating proper 5-color threshold sliders...');

    // Find threshold menu
    const thresholdMenu = document.getElementById('thresholdsMenu');
    if (!thresholdMenu) {
      console.warn('[CoreSystemOverhaul] Threshold menu not found');
      return;
    }

    // Clear and rebuild with proper 5-color system
    thresholdMenu.innerHTML = `
      <div class="threshold-header">
        <h3>🎚️ 5-Color Logic Thresholds</h3>
        <p>Adjust crossover points for current strategy</p>
      </div>
      <div class="threshold-sliders-container" id="thresholdSlidersContainer">
        <!-- 5-color sliders will be added here -->
      </div>
      <div class="threshold-actions">
        <button id="resetThresholds" class="threshold-button">Reset to Defaults</button>
        <button id="applyThresholds" class="threshold-button">Apply & Save</button>
      </div>
    `;

    // Create proper 5-color sliders for current strategy indicators
    const indicators = this.getCurrentStrategyIndicators();
    const container = document.getElementById('thresholdSlidersContainer');

    indicators.forEach(indicator => {
      this.createFiveColorSlider(container, indicator);
    });

    // Add event listeners
    document.getElementById('resetThresholds').addEventListener('click', () => {
      this.resetThresholds();
    });

    document.getElementById('applyThresholds').addEventListener('click', () => {
      this.applyThresholds();
    });

    console.log('[CoreSystemOverhaul] ✅ 5-color threshold sliders created');
  }

  createFiveColorSlider(container, indicator) {
    const thresholds = this.getCurrentThresholds(indicator);

    const sliderDiv = document.createElement('div');
    sliderDiv.className = 'five-color-slider';
    sliderDiv.innerHTML = `
      <div class="slider-header">
        <span class="indicator-label">${indicator.toUpperCase()}</span>
        <div class="threshold-values">
          <span class="value-display">
            G:${thresholds.green} | B:${thresholds.blue} | N | O:${thresholds.orange} | R:${thresholds.red}
          </span>
        </div>
      </div>
      <div class="five-color-track" data-indicator="${indicator}">
        <!-- 5 color segments: Green | Blue | Neutral | Orange | Red -->
        <div class="color-segment green-segment" style="width: ${thresholds.green}%"></div>
        <div class="color-segment blue-segment" style="width: ${thresholds.blue - thresholds.green}%"></div>
        <div class="color-segment neutral-segment" style="width: ${thresholds.orange - thresholds.blue}%"></div>
        <div class="color-segment orange-segment" style="width: ${thresholds.red - thresholds.orange}%"></div>
        <div class="color-segment red-segment" style="width: ${100 - thresholds.red}%"></div>

        <!-- 4 draggable thumbs at crossover points -->
        <div class="threshold-thumb green-thumb" data-type="green" data-indicator="${indicator}"
             style="left: ${thresholds.green}%" title="Green threshold: ${thresholds.green}%"></div>
        <div class="threshold-thumb blue-thumb" data-type="blue" data-indicator="${indicator}"
             style="left: ${thresholds.blue}%" title="Blue threshold: ${thresholds.blue}%"></div>
        <div class="threshold-thumb orange-thumb" data-type="orange" data-indicator="${indicator}"
             style="left: ${thresholds.orange}%" title="Orange threshold: ${thresholds.orange}%"></div>
        <div class="threshold-thumb red-thumb" data-type="red" data-indicator="${indicator}"
             style="left: ${thresholds.red}%" title="Red threshold: ${thresholds.red}%"></div>
      </div>
    `;

    container.appendChild(sliderDiv);

    // Make thumbs draggable with proper 5-color logic
    const thumbs = sliderDiv.querySelectorAll('.threshold-thumb');
    thumbs.forEach(thumb => {
      this.makeFiveColorThumbDraggable(thumb, indicator);
    });

    this.thresholdSliders.set(indicator, sliderDiv);
  }

  makeFiveColorThumbDraggable(thumb, indicator) {
    let isDragging = false;
    let startX = 0;
    let startLeft = 0;

    thumb.addEventListener('mousedown', (e) => {
      isDragging = true;
      startX = e.clientX;
      startLeft = parseFloat(thumb.style.left) || 0;
      thumb.classList.add('dragging');
      e.preventDefault();
    });

    document.addEventListener('mousemove', (e) => {
      if (!isDragging) return;

      const track = thumb.parentElement;
      const trackRect = track.getBoundingClientRect();
      const deltaX = e.clientX - startX;
      const deltaPercent = (deltaX / trackRect.width) * 100;

      let newLeft = startLeft + deltaPercent;

      // Constrain based on 5-color logic order: green < blue < orange < red
      const type = thumb.getAttribute('data-type');
      const currentThresholds = window.thresholds[indicator] || {};

      switch(type) {
        case 'green':
          newLeft = Math.max(0, Math.min(newLeft, (currentThresholds.blue || 30) - 1));
          break;
        case 'blue':
          newLeft = Math.max((currentThresholds.green || 20) + 1, Math.min(newLeft, (currentThresholds.orange || 70) - 1));
          break;
        case 'orange':
          newLeft = Math.max((currentThresholds.blue || 30) + 1, Math.min(newLeft, (currentThresholds.red || 80) - 1));
          break;
        case 'red':
          newLeft = Math.max((currentThresholds.orange || 70) + 1, Math.min(newLeft, 100));
          break;
      }

      thumb.style.left = `${newLeft}%`;
      thumb.title = `${type.charAt(0).toUpperCase() + type.slice(1)} threshold: ${Math.round(newLeft)}%`;

      // Update threshold value
      if (!window.thresholds) window.thresholds = {};
      if (!window.thresholds[indicator]) window.thresholds[indicator] = {};
      window.thresholds[indicator][type] = Math.round(newLeft);

      // Update 5-color segments
      this.updateFiveColorSegments(indicator);
    });

    document.addEventListener('mouseup', () => {
      if (isDragging) {
        isDragging = false;
        thumb.classList.remove('dragging');
      }
    });
  }

  updateFiveColorSegments(indicator) {
    const slider = this.thresholdSliders.get(indicator);
    if (!slider) return;

    const thresholds = window.thresholds[indicator];
    const segments = slider.querySelectorAll('.color-segment');
    const valueDisplay = slider.querySelector('.value-display');

    // Update segment widths based on threshold positions
    if (segments.length >= 5) {
      segments[0].style.width = `${thresholds.green}%`; // Green segment
      segments[1].style.width = `${thresholds.blue - thresholds.green}%`; // Blue segment
      segments[2].style.width = `${thresholds.orange - thresholds.blue}%`; // Neutral segment
      segments[3].style.width = `${thresholds.red - thresholds.orange}%`; // Orange segment
      segments[4].style.width = `${100 - thresholds.red}%`; // Red segment
    }

    // Update value display
    if (valueDisplay) {
      valueDisplay.textContent = `G:${thresholds.green} | B:${thresholds.blue} | N | O:${thresholds.orange} | R:${thresholds.red}`;
    }
  }

  wireMLAILinkage() {
    console.log('[CoreSystemOverhaul] 🤖 Wiring ML/AI linkage...');

    // Find ML chart and ensure it's properly connected
    const mlChart = document.querySelector('#mlAnalysisChart');
    if (mlChart) {
      // Connect to WebSocket data updates
      document.addEventListener('indicatorsUpdated', (e) => {
        this.updateMLChart(e.detail);
      });

      // Connect to historical data
      document.addEventListener('historicalDataReceived', (e) => {
        this.updateMLHistoricalData(e.detail);
      });

      // Connect to strategy changes
      document.addEventListener('strategyChanged', (e) => {
        this.updateMLStrategy(e.detail);
      });
    }

    // Wire info pane to show ML insights
    this.wireInfoPane();

    console.log('[CoreSystemOverhaul] ✅ ML/AI linkage wired');
  }

  wireInfoPane() {
    console.log('[CoreSystemOverhaul] 📋 Wiring info pane...');

    // Find or create info pane
    let infoPane = document.querySelector('#mlAnalysisInsights, .ml-insights');
    if (!infoPane) {
      const mlContainer = document.querySelector('.ml-analysis-container');
      if (mlContainer) {
        const insightsDiv = document.createElement('div');
        insightsDiv.className = 'ml-insights';
        insightsDiv.id = 'mlAnalysisInsights';
        insightsDiv.innerHTML = 'Waiting for ML analysis data...';
        mlContainer.appendChild(insightsDiv);
        infoPane = insightsDiv;
      }
    }

    if (infoPane) {
      // Connect to circle clicks to show analysis
      document.addEventListener('signalCircleClicked', (e) => {
        const { indicator, timeframe, data } = e.detail;
        this.updateInfoPane(infoPane, indicator, timeframe, data);
      });

      // Connect to threshold changes
      document.addEventListener('thresholdsUpdated', (e) => {
        this.updateInfoPaneThresholds(infoPane, e.detail);
      });
    }

    console.log('[CoreSystemOverhaul] ✅ Info pane wired');
  }

  updateMLChart(data) {
    // Update ML chart with new indicator data
    console.log('[CoreSystemOverhaul] 📊 Updating ML chart with new data');
    // Implementation would connect to existing ML chart system
  }

  updateMLHistoricalData(data) {
    // Update ML chart with historical data for backtesting
    console.log('[CoreSystemOverhaul] 📈 Updating ML historical data');
    // Implementation would connect to existing historical data system
  }

  updateMLStrategy(strategy) {
    // Update ML analysis based on strategy change
    console.log('[CoreSystemOverhaul] 🎯 Updating ML strategy:', strategy);
    // Implementation would update ML parameters based on strategy
  }

  updateInfoPane(infoPane, indicator, timeframe, data) {
    const thresholds = this.getCurrentThresholds(indicator);
    const analysis = this.generateAnalysis(indicator, timeframe, data, thresholds);

    infoPane.innerHTML = `
      <div class="analysis-entry">
        <strong>${indicator.toUpperCase()} (${timeframe})</strong><br>
        Value: ${this.formatValue(data.value)}<br>
        Signal: ${data.signal || 'neutral'}<br>
        Thresholds: G:${thresholds.green} B:${thresholds.blue} O:${thresholds.orange} R:${thresholds.red}<br>
        Analysis: ${analysis}
      </div>
    `;
  }

  updateInfoPaneThresholds(infoPane, thresholds) {
    const currentContent = infoPane.innerHTML;
    infoPane.innerHTML = currentContent + `<br><em>Thresholds updated for current strategy</em>`;
  }

  generateAnalysis(indicator, timeframe, data, thresholds) {
    const value = parseFloat(data.value) || 0;

    if (value <= thresholds.green) return "Strong buy signal - value in green zone";
    if (value <= thresholds.blue) return "Buy signal - value in blue zone";
    if (value <= thresholds.orange) return "Neutral - value in neutral zone";
    if (value <= thresholds.red) return "Sell signal - value in orange zone";
    return "Strong sell signal - value in red zone";
  }

  resetThresholds() {
    console.log('[CoreSystemOverhaul] Resetting thresholds...');
    
    this.getCurrentStrategyIndicators().forEach(indicator => {
      const defaults = {
        red: 80,
        orange: 70,
        blue: 30,
        green: 20
      };
      
      if (!window.thresholds) window.thresholds = {};
      window.thresholds[indicator] = { ...defaults };
      
      // Update slider positions
      const slider = this.thresholdSliders.get(indicator);
      if (slider) {
        const thumbs = slider.querySelectorAll('.slider-thumb');
        thumbs.forEach(thumb => {
          const type = thumb.getAttribute('data-type');
          thumb.style.left = `${defaults[type]}%`;
        });
        this.updateThresholdDisplay(indicator);
      }
    });
  }

  applyThresholds() {
    console.log('[CoreSystemOverhaul] Applying thresholds...');
    
    // Save to localStorage
    localStorage.setItem('starCryptThresholds', JSON.stringify(window.thresholds));
    
    // Dispatch event
    document.dispatchEvent(new CustomEvent('thresholdsUpdated', {
      detail: window.thresholds
    }));
    
    // Force signal light update
    if (typeof window.forceUpdateSignalLights === 'function') {
      window.forceUpdateSignalLights();
    }
  }

  wireHistoricalAnalysis() {
    console.log('[CoreSystemOverhaul] 🔗 Wiring historical analysis...');

    // Find or create historical analysis container
    let historyContainer = document.querySelector('#mlHistoricalAnalysis, .ml-historical-analysis');

    if (!historyContainer) {
      // Create container if it doesn't exist
      const mlContainer = document.querySelector('#ml-visualization-container, .ml-visualization-container');
      if (mlContainer) {
        historyContainer = document.createElement('div');
        historyContainer.id = 'mlHistoricalAnalysis';
        historyContainer.className = 'ml-historical-analysis';
        historyContainer.innerHTML = `
          <div class="history-header">
            <h4>📊 Historical Analysis</h4>
            <button id="clearHistory" class="clear-button">Clear</button>
          </div>
          <div class="history-content" id="historyContent">
            <p class="no-data">Click signal circles to add analysis data</p>
          </div>
        `;
        mlContainer.appendChild(historyContainer);

        // Add clear button functionality
        document.getElementById('clearHistory').addEventListener('click', () => {
          this.clearHistoricalAnalysis();
        });
      }
    }

    console.log('[CoreSystemOverhaul] ✅ Historical analysis wired');
  }

  addToHistoricalAnalysis(data) {
    console.log('[CoreSystemOverhaul] 📊 Adding to historical analysis:', data);

    const historyContent = document.getElementById('historyContent');
    if (!historyContent) {
      console.warn('[CoreSystemOverhaul] History content container not found');
      return;
    }

    // Remove "no data" message
    const noDataMsg = historyContent.querySelector('.no-data');
    if (noDataMsg) {
      noDataMsg.remove();
    }

    // Create analysis entry
    const entry = document.createElement('div');
    entry.className = 'history-entry';
    entry.innerHTML = `
      <div class="entry-header">
        <span class="indicator-badge">${data.indicator.toUpperCase()}</span>
        <span class="timeframe-badge">${data.timeframe}</span>
        <span class="timestamp">${new Date(data.timestamp).toLocaleTimeString()}</span>
      </div>
      <div class="entry-data">
        <div class="value-info">
          <span class="label">Value:</span>
          <span class="value">${this.formatValue(data.value)}</span>
        </div>
        <div class="threshold-info">
          <span class="label">Thresholds:</span>
          <span class="thresholds">
            R:${data.thresholds.red} O:${data.thresholds.orange}
            B:${data.thresholds.blue} G:${data.thresholds.green}
          </span>
        </div>
        <div class="strategy-info">
          <span class="label">Strategy:</span>
          <span class="strategy">${data.strategy}</span>
        </div>
      </div>
    `;

    // Add to top of list
    historyContent.insertBefore(entry, historyContent.firstChild);

    // Limit to 20 entries
    const entries = historyContent.querySelectorAll('.history-entry');
    if (entries.length > 20) {
      entries[entries.length - 1].remove();
    }
  }

  formatValue(value) {
    if (typeof value === 'number') {
      return value.toFixed(2);
    }
    return value || 'N/A';
  }

  clearHistoricalAnalysis() {
    const historyContent = document.getElementById('historyContent');
    if (historyContent) {
      historyContent.innerHTML = '<p class="no-data">Click signal circles to add analysis data</p>';
    }
  }

  showActionableTooltip(circle, indicator, timeframe, data) {
    // Remove existing tooltips
    document.querySelectorAll('.actionable-tooltip').forEach(tooltip => {
      tooltip.remove();
    });

    // Create tooltip
    const tooltip = document.createElement('div');
    tooltip.className = 'actionable-tooltip';
    tooltip.innerHTML = `
      <div class="tooltip-header">
        <strong>${indicator.toUpperCase()} - ${timeframe}</strong>
      </div>
      <div class="tooltip-value">
        Value: ${this.formatValue(data.value)}
      </div>
      <div class="tooltip-signal">
        Signal: ${data.signal || 'neutral'}
      </div>
      <div class="tooltip-advice">
        💡 ${this.getActionableAdvice(indicator, data)}
      </div>
    `;

    // Position tooltip
    const rect = circle.getBoundingClientRect();
    tooltip.style.cssText = `
      position: fixed;
      left: ${rect.right + 10}px;
      top: ${rect.top}px;
      background: rgba(0, 20, 40, 0.95);
      border: 2px solid rgba(0, 255, 255, 0.6);
      border-radius: 8px;
      padding: 12px;
      color: #ffffff;
      font-family: 'Orbitron', sans-serif;
      font-size: 12px;
      max-width: 250px;
      z-index: 999999;
      box-shadow: 0 8px 32px rgba(0, 255, 255, 0.3);
    `;

    document.body.appendChild(tooltip);

    // Remove after 3 seconds
    setTimeout(() => {
      tooltip.remove();
    }, 3000);
  }

  getActionableAdvice(indicator, data) {
    const adviceMap = {
      'rsi': {
        'strong_sell': 'RSI overbought - consider taking profits',
        'sell': 'RSI elevated - watch for reversal',
        'neutral': 'RSI neutral - wait for clear signal',
        'buy': 'RSI oversold - potential entry opportunity',
        'strong_buy': 'RSI deeply oversold - strong buy signal'
      },
      'macd': {
        'strong_sell': 'MACD bearish momentum - reduce exposure',
        'sell': 'MACD weakening - trend may reverse',
        'neutral': 'MACD neutral - no clear direction',
        'buy': 'MACD building momentum - prepare entry',
        'strong_buy': 'MACD strong bullish - good entry signal'
      }
    };

    const signal = data.signal || 'neutral';
    return adviceMap[indicator]?.[signal] || `Monitor ${indicator.toUpperCase()} for trend changes`;
  }

  applyProfessionalStyling() {
    console.log('[CoreSystemOverhaul] 🎨 Applying professional styling...');

    const style = document.createElement('style');
    style.id = 'core-system-overhaul-css';
    style.textContent = `
      /* Enhanced Matrix Structure - Work with existing table */
      #momentum-table.enhanced-matrix-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
      }

      /* Hide any unnecessary headers that were added */
      #momentum-table.enhanced-matrix-table th {
        display: none !important;
      }

      /* Fix existing table rows */
      #momentum-table tr {
        height: 50px !important;
        border-bottom: 1px solid rgba(0, 255, 255, 0.1);
        transition: background 0.3s ease;
      }

      #momentum-table tr:hover {
        background: rgba(0, 255, 255, 0.05);
      }

      #momentum-table td {
        padding: 5px !important;
        vertical-align: middle !important;
        height: 50px !important;
        box-sizing: border-box;
      }

      /* Fix indicator name column */
      #momentum-table td:first-child {
        text-align: left !important;
        padding-left: 15px !important;
        width: 120px;
        min-width: 120px;
      }

      /* Fix mini chart column */
      #momentum-table td:nth-child(2) {
        width: 120px;
        min-width: 120px;
        padding: 5px !important;
        text-align: center;
      }

      /* Fix signal light columns - align to RIGHT */
      #momentum-table td:nth-child(n+3) {
        width: 40px !important;
        min-width: 40px !important;
        max-width: 40px !important;
        text-align: right !important;
        padding: 2px 10px 2px 2px !important;
        position: relative !important;
      }

      /* Fix existing mini charts */
      #momentum-table [id*="Chart"],
      #momentum-table .mini-chart,
      #momentum-table canvas[id*="chart"] {
        height: 40px !important;
        max-height: 40px !important;
        min-height: 40px !important;
        width: 100% !important;
        display: block !important;
        border: 1px solid rgba(0, 255, 255, 0.2);
        border-radius: 4px;
        background: rgba(0, 20, 40, 0.3);
      }

      /* Hide any data overlays covering charts */
      #momentum-table .data-overlay,
      #momentum-table .chart-data,
      #momentum-table .indicator-data {
        display: none !important;
      }

      /* Fix existing signal circles - ALIGN RIGHT */
      #momentum-table .signal-circle,
      #momentum-table .circle {
        width: 20px !important;
        height: 20px !important;
        border-radius: 50% !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        margin-left: auto !important;
        margin-right: 0 !important;
        display: block !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
        float: right !important;
      }

      #momentum-table .signal-circle:hover,
      #momentum-table .circle:hover {
        transform: scale(1.1) !important;
        box-shadow: 0 0 10px rgba(0, 255, 255, 0.5) !important;
      }

      /* Add timeframe labels to signal circles */
      #momentum-table .signal-circle::after,
      #momentum-table .circle::after {
        content: attr(data-timeframe);
        position: absolute;
        bottom: -15px;
        right: 0;
        font-size: 8px;
        color: #888;
        font-family: 'Orbitron', sans-serif;
        white-space: nowrap;
        pointer-events: none;
      }

      /* 5-Color Threshold Sliders */
      .threshold-header {
        text-align: center;
        margin-bottom: 20px;
        color: #00ffff;
      }

      .five-color-slider {
        margin-bottom: 20px;
        padding: 15px;
        background: rgba(0, 20, 40, 0.3);
        border: 1px solid rgba(0, 255, 255, 0.2);
        border-radius: 8px;
      }

      .slider-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
      }

      .indicator-label {
        color: #00ccff;
        font-weight: bold;
        font-family: 'Orbitron', sans-serif;
        font-size: 1rem;
      }

      .value-display {
        font-size: 0.8rem;
        color: #ffffff;
        font-family: 'Orbitron', sans-serif;
      }

      .five-color-track {
        position: relative;
        height: 40px;
        border: 2px solid rgba(0, 255, 255, 0.3);
        border-radius: 20px;
        margin: 10px 0;
        display: flex;
        overflow: hidden;
      }

      .color-segment {
        height: 100%;
        transition: width 0.3s ease;
      }

      .green-segment { background: #44ff44; }
      .blue-segment { background: #4488ff; }
      .neutral-segment { background: #888888; }
      .orange-segment { background: #ff8800; }
      .red-segment { background: #ff4444; }

      .threshold-thumb {
        position: absolute;
        width: 24px;
        height: 24px;
        background: #ffffff;
        border: 3px solid #000000;
        border-radius: 50%;
        top: 8px;
        cursor: grab;
        transition: transform 0.2s ease;
        margin-left: -12px;
        z-index: 10;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      }

      .threshold-thumb:hover {
        transform: scale(1.2);
        box-shadow: 0 4px 12px rgba(0, 255, 255, 0.5);
      }

      .threshold-thumb.dragging {
        cursor: grabbing;
        transform: scale(1.3);
        z-index: 20;
        box-shadow: 0 6px 16px rgba(0, 255, 255, 0.8);
      }

      .green-thumb { border-color: #44ff44; }
      .blue-thumb { border-color: #4488ff; }
      .orange-thumb { border-color: #ff8800; }
      .red-thumb { border-color: #ff4444; }

      .threshold-actions {
        display: flex;
        gap: 10px;
        justify-content: center;
        margin-top: 20px;
      }

      .threshold-button {
        padding: 10px 20px;
        background: linear-gradient(135deg, rgba(0, 100, 200, 0.8), rgba(0, 60, 120, 0.9));
        border: 1px solid rgba(0, 255, 255, 0.4);
        border-radius: 5px;
        color: #ffffff;
        font-family: 'Orbitron', sans-serif;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .threshold-button:hover {
        background: linear-gradient(135deg, rgba(0, 150, 255, 0.8), rgba(0, 100, 180, 0.9));
        box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
      }

      /* Historical Analysis */
      .ml-historical-analysis {
        margin-top: 20px;
        background: rgba(0, 20, 40, 0.8);
        border: 1px solid rgba(0, 255, 255, 0.3);
        border-radius: 8px;
        padding: 15px;
      }

      .history-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        color: #00ffff;
      }

      .clear-button {
        padding: 5px 10px;
        background: rgba(255, 100, 100, 0.8);
        border: 1px solid rgba(255, 0, 0, 0.4);
        border-radius: 4px;
        color: white;
        font-size: 0.8rem;
        cursor: pointer;
      }

      .history-entry {
        margin-bottom: 10px;
        padding: 10px;
        background: rgba(0, 30, 60, 0.3);
        border: 1px solid rgba(0, 255, 255, 0.2);
        border-radius: 6px;
      }

      .entry-header {
        display: flex;
        gap: 10px;
        margin-bottom: 5px;
      }

      .indicator-badge, .timeframe-badge {
        padding: 2px 6px;
        background: rgba(0, 255, 255, 0.2);
        border-radius: 3px;
        font-size: 0.8rem;
        color: #00ffff;
      }

      .timestamp {
        margin-left: auto;
        font-size: 0.7rem;
        color: #888;
      }

      .entry-data {
        font-size: 0.8rem;
        color: #ccc;
      }

      .entry-data .label {
        color: #00ccff;
        font-weight: bold;
      }

      /* Actionable Tooltip */
      .actionable-tooltip {
        font-family: 'Orbitron', sans-serif;
      }

      .tooltip-header {
        color: #00ffff;
        margin-bottom: 5px;
      }

      .tooltip-value, .tooltip-signal {
        margin-bottom: 3px;
      }

      .tooltip-advice {
        margin-top: 8px;
        padding-top: 8px;
        border-top: 1px solid rgba(0, 255, 255, 0.3);
        color: #ffff88;
      }
    `;

    // Remove existing style if present
    const existingStyle = document.getElementById('core-system-overhaul-css');
    if (existingStyle) existingStyle.remove();

    document.head.appendChild(style);

    console.log('[CoreSystemOverhaul] ✅ Professional styling applied');
  }
}

// Initialize the core system overhaul
document.addEventListener('DOMContentLoaded', () => {
  // Disable all the broken modular fixes
  window.comprehensiveSystemFix = null;
  window.enhancedMLChartSystem = null;
  window.miniChartsFix = null;
  window.volumeSpikefix = null;
  window.finalCleanupFixes = null;
  window.matrixAlignmentFix = null;
  window.thresholdSliderFix = null;

  // Initialize the working system
  window.coreSystemOverhaul = new CoreSystemOverhaul();
});

// Also initialize if DOM is already loaded
if (document.readyState !== 'loading') {
  window.coreSystemOverhaul = new CoreSystemOverhaul();
}
