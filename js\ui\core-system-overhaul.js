/**
 * Core System Overhaul - Complete fix for all fundamental issues
 * This replaces all the broken modular fixes with a single, working system
 */

class CoreSystemOverhaul {
  constructor() {
    this.isInitialized = false;
    this.circleClickHandler = null;
    this.miniCharts = new Map();
    this.thresholdSliders = new Map();
    this.matrixContainer = null;
    
    // Bind methods
    this.handleCircleClick = this.handleCircleClick.bind(this);
    
    this.init();
  }

  init() {
    console.log('[CoreSystemOverhaul] 🚀 Starting complete system overhaul...');
    
    // Wait for DOM and WebSocket
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.executeOverhaul());
    } else {
      this.executeOverhaul();
    }
  }

  executeOverhaul() {
    if (this.isInitialized) return;

    console.log('[CoreSystemOverhaul] 🔧 Executing complete system overhaul...');

    try {
      // 1. Remove all zoom buttons permanently
      this.removeAllZoomButtons();

      // 2. Fix matrix structure and alignment
      this.fixMatrixStructure();

      // 3. Fix signal lights alignment to right
      this.fixSignalLightsAlignment();

      // 4. Fix AI chart sizing and visibility
      this.fixAIChartSizing();

      // 5. Fix circle click handlers (remove all conflicts)
      this.fixCircleClickHandlers();

      // 6. Fix mini charts completely
      this.fixMiniCharts();

      // 7. Fix threshold sliders completely (proper 5-color system)
      this.fixThresholdSliders();

      // 8. Enhance Signal Logic and Light Logic menus
      this.enhanceLogicMenus();

      // 9. Wire ML/AI linkage properly
      this.wireMLAILinkage();

      // 10. Wire historical analysis properly
      this.wireHistoricalAnalysis();

      // 11. Apply professional styling
      this.applyProfessionalStyling();

      this.isInitialized = true;
      console.log('[CoreSystemOverhaul] ✅ Complete system overhaul successful');

    } catch (error) {
      console.error('[CoreSystemOverhaul] ❌ System overhaul failed:', error);
    }
  }

  fixMatrixStructure() {
    console.log('[CoreSystemOverhaul] 📊 Working with existing matrix structure...');

    // Find the momentum table - DON'T rebuild it, just enhance it
    this.matrixContainer = document.querySelector('#momentum-indicators');
    if (!this.matrixContainer) {
      console.error('[CoreSystemOverhaul] Momentum indicators container not found');
      return;
    }

    // The table already exists and is managed by the main system
    // We just need to ensure proper styling and functionality
    const table = this.matrixContainer.querySelector('#momentum-table');
    if (table) {
      // Add our enhancement class without disrupting existing structure
      table.classList.add('enhanced-matrix-table');

      // Remove any unnecessary headers that might have been added
      const headers = table.querySelectorAll('th');
      headers.forEach(header => {
        if (header.textContent.includes('Indicator') ||
            header.textContent.includes('Chart') ||
            header.textContent.includes('1m') ||
            header.textContent.includes('5m')) {
          header.parentElement?.remove();
        }
      });
    }

    console.log('[CoreSystemOverhaul] ✅ Matrix structure enhanced (not rebuilt)');
  }

  removeAllZoomButtons() {
    console.log('[CoreSystemOverhaul] 🗑️ Removing ALL zoom buttons permanently...');

    // Find all possible zoom button selectors
    const zoomSelectors = [
      'button[id*="zoom"]',
      'button[class*="zoom"]',
      'button[title*="zoom"]',
      'button[title*="Zoom"]',
      '.zoom-in-btn',
      '.zoom-out-btn',
      '.reset-zoom-btn',
      '.toggle-visibility-btn',
      '.chart-control-btn',
      '#ml-zoom-in',
      '#ml-zoom-out',
      '#ml-reset-zoom',
      '#ml-toggle-visibility',
      'button[onclick*="zoom"]',
      'button[onclick*="toggle"]'
    ];

    zoomSelectors.forEach(selector => {
      document.querySelectorAll(selector).forEach(button => {
        console.log('[CoreSystemOverhaul] Removing zoom button:', button.textContent || button.id || button.className);
        button.remove();
      });
    });

    // Remove zoom control containers
    document.querySelectorAll('.zoom-controls, .chart-controls, .ml-control-group, #ml-controls').forEach(container => {
      console.log('[CoreSystemOverhaul] Removing zoom container:', container.className || container.id);
      container.remove();
    });

    console.log('[CoreSystemOverhaul] ✅ All zoom buttons removed');
  }

  fixSignalLightsAlignment() {
    console.log('[CoreSystemOverhaul] ➡️ Fixing signal lights alignment to right...');

    // Find all signal light cells and align them to the right
    const signalCells = document.querySelectorAll('#momentum-table td:nth-child(n+3)');
    signalCells.forEach(cell => {
      cell.style.textAlign = 'right';
      cell.style.paddingRight = '10px';

      // Also align the signal circles within the cells
      const circles = cell.querySelectorAll('.signal-circle, .circle');
      circles.forEach(circle => {
        circle.style.marginLeft = 'auto';
        circle.style.marginRight = '0';
      });
    });

    console.log('[CoreSystemOverhaul] ✅ Signal lights aligned to right');
  }

  fixAIChartSizing() {
    console.log('[CoreSystemOverhaul] 📊 Fixing AI chart sizing and visibility...');

    // Find ML analysis container
    const mlContainer = document.querySelector('.ml-analysis-container');
    if (mlContainer) {
      // Fix container sizing
      mlContainer.style.cssText = `
        margin-top: 20px;
        padding: 15px;
        background: rgba(0, 20, 40, 0.8);
        border: 1px solid rgba(0, 255, 255, 0.3);
        border-radius: 8px;
        width: 100%;
        box-sizing: border-box;
      `;

      // Fix chart container
      const chartContainer = mlContainer.querySelector('.chart-container');
      if (chartContainer) {
        chartContainer.style.cssText = `
          position: relative;
          height: 300px;
          width: 100%;
          overflow: visible;
        `;

        // Fix canvas
        const canvas = chartContainer.querySelector('#mlAnalysisChart');
        if (canvas) {
          canvas.style.cssText = `
            width: 100% !important;
            height: 100% !important;
            display: block;
          `;
          canvas.width = chartContainer.offsetWidth;
          canvas.height = 300;
        }
      }
    }

    console.log('[CoreSystemOverhaul] ✅ AI chart sizing fixed');
  }

  fixMenuPositioning() {
    console.log('[CoreSystemOverhaul] 📍 Fixing menu positioning to universal position...');

    // Fix Signal Logic and Light Logic menus to use universal positioning
    const menusToFix = ['#logicMenu', '#lightLogicMenu'];

    menusToFix.forEach(selector => {
      const menu = document.querySelector(selector);
      if (menu) {
        // Apply universal menu positioning
        menu.style.cssText = `
          position: fixed !important;
          top: 50% !important;
          left: calc(50% + 200px) !important;
          transform: translate(-50%, -50%) !important;
          width: 400px !important;
          max-width: 90vw !important;
          max-height: 80vh !important;
          overflow-y: auto !important;
          z-index: 1000 !important;
          background: linear-gradient(135deg, rgba(0, 20, 40, 0.95), rgba(0, 40, 80, 0.95)) !important;
          border: 2px solid rgba(0, 255, 255, 0.6) !important;
          border-radius: 12px !important;
          backdrop-filter: blur(10px) !important;
          box-shadow: 0 0 30px rgba(0, 255, 255, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
          display: none !important;
        `;
      }
    });

    console.log('[CoreSystemOverhaul] ✅ Menu positioning fixed');
  }

  enhanceLogicMenus() {
    console.log('[CoreSystemOverhaul] 🎛️ Enhancing Signal Logic and Light Logic menus...');

    // Enhance Signal Logic Menu
    this.enhanceSignalLogicMenu();

    // Enhance Light Logic Menu
    this.enhanceLightLogicMenu();

    console.log('[CoreSystemOverhaul] ✅ Logic menus enhanced');
  }

  enhanceSignalLogicMenu() {
    const logicMenu = document.getElementById('logicMenu');
    if (logicMenu) {
      logicMenu.innerHTML = `
        <div class="menu-header">
          <h3>🎯 Signal Logic Control</h3>
          <p>Advanced signal processing and convergence settings</p>
        </div>
        <div class="logic-controls">
          <div class="control-group">
            <label>Signal Convergence Mode:</label>
            <select id="convergenceMode" class="logic-select">
              <option value="strict">Strict Convergence</option>
              <option value="moderate">Moderate Convergence</option>
              <option value="loose">Loose Convergence</option>
              <option value="adaptive">Adaptive Convergence</option>
            </select>
          </div>

          <div class="control-group">
            <label>Signal Strength Multiplier:</label>
            <input type="range" id="strengthMultiplier" min="0.5" max="3.0" step="0.1" value="1.0">
            <span id="strengthValue">1.0x</span>
          </div>

          <div class="control-group">
            <label>Noise Filtering:</label>
            <select id="noiseFilter" class="logic-select">
              <option value="none">No Filtering</option>
              <option value="light">Light Filtering</option>
              <option value="medium">Medium Filtering</option>
              <option value="heavy">Heavy Filtering</option>
            </select>
          </div>

          <div class="control-group">
            <label>Signal Persistence:</label>
            <input type="range" id="signalPersistence" min="1" max="10" step="1" value="3">
            <span id="persistenceValue">3 bars</span>
          </div>

          <div class="control-group">
            <label>Cross-Timeframe Weighting:</label>
            <input type="checkbox" id="crossTimeframeWeight" checked>
            <span>Enable higher timeframe bias</span>
          </div>
        </div>
      `;

      // Add event listeners
      this.setupSignalLogicEventListeners();
    }
  }

  enhanceLightLogicMenu() {
    const lightLogicMenu = document.getElementById('lightLogicMenu');
    if (lightLogicMenu) {
      lightLogicMenu.innerHTML = `
        <div class="menu-header">
          <h3>💡 Light Logic Styles</h3>
          <p>Visual effects and animation settings</p>
        </div>
        <div class="light-controls">
          <div class="control-group">
            <label>Visual Style:</label>
            <select id="lightStyle" class="logic-select">
              <option value="classic">Classic</option>
              <option value="neon">Neon Glow</option>
              <option value="cosmic">Cosmic</option>
              <option value="matrix">Matrix</option>
              <option value="vibeflow">Vibe Flow</option>
              <option value="quantum">Quantum</option>
              <option value="cyberpunk">Cyberpunk</option>
            </select>
          </div>

          <div class="control-group">
            <label>Animation Intensity:</label>
            <input type="range" id="animationIntensity" min="1" max="5" step="1" value="3">
            <span id="intensityValue">3</span>
          </div>

          <div class="control-group">
            <label>Pulse Effects:</label>
            <input type="checkbox" id="enablePulse" checked>
            <span>Enable signal pulse effects</span>
          </div>

          <div class="control-group">
            <label>Color Saturation:</label>
            <input type="range" id="colorSaturation" min="50" max="150" step="10" value="100">
            <span id="saturationValue">100%</span>
          </div>

          <div class="control-group">
            <label>Glow Radius:</label>
            <input type="range" id="glowRadius" min="0" max="20" step="2" value="8">
            <span id="glowValue">8px</span>
          </div>

          <div class="control-group">
            <label>Transition Speed:</label>
            <select id="transitionSpeed" class="logic-select">
              <option value="slow">Slow (1s)</option>
              <option value="medium">Medium (0.5s)</option>
              <option value="fast">Fast (0.3s)</option>
              <option value="instant">Instant</option>
            </select>
          </div>
        </div>
      `;

      // Add event listeners
      this.setupLightLogicEventListeners();
    }
  }

  setupSignalLogicEventListeners() {
    // Convergence mode
    const convergenceMode = document.getElementById('convergenceMode');
    if (convergenceMode) {
      convergenceMode.addEventListener('change', (e) => {
        window.signalLogicSettings = window.signalLogicSettings || {};
        window.signalLogicSettings.convergenceMode = e.target.value;
        console.log('[CoreSystemOverhaul] Convergence mode changed:', e.target.value);
      });
    }

    // Strength multiplier
    const strengthMultiplier = document.getElementById('strengthMultiplier');
    const strengthValue = document.getElementById('strengthValue');
    if (strengthMultiplier && strengthValue) {
      strengthMultiplier.addEventListener('input', (e) => {
        const value = parseFloat(e.target.value);
        strengthValue.textContent = `${value}x`;
        window.signalLogicSettings = window.signalLogicSettings || {};
        window.signalLogicSettings.strengthMultiplier = value;
      });
    }

    // Signal persistence
    const signalPersistence = document.getElementById('signalPersistence');
    const persistenceValue = document.getElementById('persistenceValue');
    if (signalPersistence && persistenceValue) {
      signalPersistence.addEventListener('input', (e) => {
        const value = parseInt(e.target.value);
        persistenceValue.textContent = `${value} bars`;
        window.signalLogicSettings = window.signalLogicSettings || {};
        window.signalLogicSettings.persistence = value;
      });
    }
  }

  setupLightLogicEventListeners() {
    // Animation intensity
    const animationIntensity = document.getElementById('animationIntensity');
    const intensityValue = document.getElementById('intensityValue');
    if (animationIntensity && intensityValue) {
      animationIntensity.addEventListener('input', (e) => {
        const value = parseInt(e.target.value);
        intensityValue.textContent = value;
        window.lightLogicSettings = window.lightLogicSettings || {};
        window.lightLogicSettings.intensity = value;
      });
    }

    // Color saturation
    const colorSaturation = document.getElementById('colorSaturation');
    const saturationValue = document.getElementById('saturationValue');
    if (colorSaturation && saturationValue) {
      colorSaturation.addEventListener('input', (e) => {
        const value = parseInt(e.target.value);
        saturationValue.textContent = `${value}%`;
        window.lightLogicSettings = window.lightLogicSettings || {};
        window.lightLogicSettings.saturation = value;
      });
    }

    // Glow radius
    const glowRadius = document.getElementById('glowRadius');
    const glowValue = document.getElementById('glowValue');
    if (glowRadius && glowValue) {
      glowRadius.addEventListener('input', (e) => {
        const value = parseInt(e.target.value);
        glowValue.textContent = `${value}px`;
        window.lightLogicSettings = window.lightLogicSettings || {};
        window.lightLogicSettings.glowRadius = value;
      });
    }
  }

  fixSignalLightsAlignment() {
    console.log('[CoreSystemOverhaul] ➡️ Fixing signal lights alignment and sizing...');

    // Wait for table to be rendered, then fix alignment
    setTimeout(() => {
      // Find all signal light cells
      const signalCells = document.querySelectorAll('#momentum-table td:nth-child(n+3)');

      signalCells.forEach((cell, index) => {
        // Fix cell alignment and spacing
        cell.style.cssText = `
          text-align: right !important;
          padding: 8px 15px 8px 8px !important;
          vertical-align: middle !important;
          width: 50px !important;
          min-width: 50px !important;
          position: relative !important;
        `;

        // Find signal circles in this cell
        const circles = cell.querySelectorAll('.signal-circle, .circle');
        circles.forEach(circle => {
          // Make circles 40px with timeframe text centered
          circle.style.cssText = `
            width: 40px !important;
            height: 40px !important;
            border-radius: 50% !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            margin: 0 !important;
            float: right !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            border: 2px solid rgba(255, 255, 255, 0.3) !important;
            font-size: 10px !important;
            font-weight: bold !important;
            color: #ffffff !important;
            text-shadow: 0 0 3px rgba(0, 0, 0, 0.8) !important;
            font-family: 'Orbitron', sans-serif !important;
          `;

          // Add timeframe text centered in the circle
          const timeframe = circle.getAttribute('data-timeframe');
          if (timeframe && !circle.textContent) {
            circle.textContent = timeframe;
          }
        });
      });

      // Fix row spacing to match light spacing
      const rows = document.querySelectorAll('#momentum-table tr');
      rows.forEach(row => {
        row.style.height = '60px';
        row.style.marginBottom = '8px';
      });

    }, 1000);

    console.log('[CoreSystemOverhaul] ✅ Signal lights alignment and sizing fixed');
  }

  getCurrentStrategyIndicators() {
    // Get indicators from current strategy or use defaults
    const currentStrategy = window.currentStrategy || 'momentum_blast';
    const strategyConfig = window.TRADING_STRATEGIES?.[currentStrategy];

    if (strategyConfig && strategyConfig.indicators) {
      return strategyConfig.indicators;
    }

    // Default indicators
    return ['rsi', 'stochRsi', 'macd', 'mfi', 'volume'];
  }

  fixCircleClickHandlers() {
    console.log('[CoreSystemOverhaul] 🎯 Fixing circle click handlers...');
    
    // Remove ALL existing click handlers by cloning elements
    document.querySelectorAll('.signal-circle').forEach(circle => {
      const newCircle = circle.cloneNode(true);
      circle.parentNode.replaceChild(newCircle, circle);
    });
    
    // Remove any existing document-level click handlers
    if (this.circleClickHandler) {
      document.removeEventListener('click', this.circleClickHandler);
    }
    
    // Add single, unified click handler
    this.circleClickHandler = this.handleCircleClick;
    document.addEventListener('click', this.circleClickHandler);
    
    console.log('[CoreSystemOverhaul] ✅ Circle click handlers fixed');
  }

  handleCircleClick(event) {
    // Try multiple selectors to catch all circle types
    const circle = event.target.closest('.signal-circle') ||
                   event.target.closest('.circle') ||
                   event.target.closest('[data-indicator]');

    if (!circle) return;

    event.stopPropagation();
    event.preventDefault();

    const indicator = circle.getAttribute('data-indicator') || circle.getAttribute('data-ind');
    const timeframe = circle.getAttribute('data-timeframe') || circle.getAttribute('data-tf');

    if (!indicator || !timeframe) {
      console.warn('[CoreSystemOverhaul] Circle missing data attributes:', circle);
      return;
    }

    console.log(`[CoreSystemOverhaul] ✅ Circle clicked: ${indicator} (${timeframe})`);

    // Get current data from global state
    const indicatorData = this.getIndicatorData(indicator, timeframe);
    const thresholds = this.getCurrentThresholds(indicator);

    // Add to historical analysis with visual feedback
    this.addToHistoricalAnalysis({
      indicator,
      timeframe,
      value: indicatorData.value,
      color: indicatorData.color,
      signal: indicatorData.signal,
      thresholds,
      timestamp: new Date().toISOString(),
      strategy: window.currentStrategy || 'unknown'
    });

    // Show actionable tooltip
    this.showActionableTooltip(circle, indicator, timeframe, indicatorData);

    // Update info pane immediately
    const infoPane = document.querySelector('#mlAnalysisInsights, .ml-insights');
    if (infoPane) {
      this.updateInfoPane(infoPane, indicator, timeframe, indicatorData);
    }

    // Visual feedback - flash the circle
    circle.style.boxShadow = '0 0 20px rgba(0, 255, 255, 1)';
    setTimeout(() => {
      circle.style.boxShadow = '';
    }, 500);

    // Dispatch custom event for other systems
    document.dispatchEvent(new CustomEvent('signalCircleClicked', {
      detail: { indicator, timeframe, element: circle, data: indicatorData }
    }));
  }

  getIndicatorData(indicator, timeframe) {
    // Get data from global state
    const tfData = window.indicatorsData?.[timeframe] || {};
    const data = tfData[indicator] || {};
    
    return {
      value: data.value || data.current || 0,
      color: data.color || '#808080',
      signal: data.signal || 'neutral',
      strength: data.strength || 0.5
    };
  }

  getCurrentThresholds(indicator) {
    const thresholds = window.thresholds || {};
    return thresholds[indicator] || {
      red: 80,
      orange: 70,
      blue: 30,
      green: 20
    };
  }

  fixMiniCharts() {
    console.log('[CoreSystemOverhaul] 📈 Fixing existing mini charts...');

    // Find all existing mini chart elements and fix their styling
    const existingCharts = document.querySelectorAll('[id*="Chart"], .mini-chart, canvas[id*="chart"]');

    existingCharts.forEach(chart => {
      // Fix height and container issues
      chart.style.height = '40px';
      chart.style.maxHeight = '40px';
      chart.style.minHeight = '40px';

      if (chart.tagName === 'CANVAS') {
        chart.height = 40;
      }

      // Fix parent container if it exists
      const parentCell = chart.closest('td');
      if (parentCell) {
        parentCell.style.height = '50px';
        parentCell.style.verticalAlign = 'middle';
        parentCell.style.padding = '5px';
        parentCell.style.position = 'relative';
        parentCell.style.overflow = 'hidden';
      }

      // Remove any data overlays that might be covering the chart
      const dataOverlays = chart.parentElement?.querySelectorAll('.data-overlay, .chart-data, .indicator-data');
      dataOverlays?.forEach(overlay => {
        if (overlay.style.position === 'absolute' || overlay.style.zIndex > 1) {
          overlay.style.display = 'none';
        }
      });
    });

    console.log('[CoreSystemOverhaul] ✅ Existing mini charts fixed');
  }



  fixThresholdSliders() {
    console.log('[CoreSystemOverhaul] 🎚️ Creating proper 5-color threshold sliders...');

    // Find threshold menu
    const thresholdMenu = document.getElementById('thresholdsMenu');
    if (!thresholdMenu) {
      console.warn('[CoreSystemOverhaul] Threshold menu not found');
      return;
    }

    // Clear and rebuild with proper 5-color system
    thresholdMenu.innerHTML = `
      <div class="threshold-header">
        <h3>🎚️ 5-Color Logic Thresholds</h3>
        <p>Adjust crossover points for current strategy</p>
      </div>
      <div class="threshold-sliders-container" id="thresholdSlidersContainer">
        <!-- 5-color sliders will be added here -->
      </div>
      <div class="threshold-actions">
        <button id="resetThresholds" class="threshold-button">Reset to Defaults</button>
        <button id="applyThresholds" class="threshold-button">Apply & Save</button>
      </div>
    `;

    // Create proper 5-color sliders for current strategy indicators
    const indicators = this.getCurrentStrategyIndicators();
    const container = document.getElementById('thresholdSlidersContainer');

    indicators.forEach(indicator => {
      this.createFiveColorSlider(container, indicator);
    });

    // Add event listeners
    document.getElementById('resetThresholds').addEventListener('click', () => {
      this.resetThresholds();
    });

    document.getElementById('applyThresholds').addEventListener('click', () => {
      this.applyThresholds();
    });

    // Fix menu positioning to universal position
    this.fixMenuPositioning();

    console.log('[CoreSystemOverhaul] ✅ 5-color threshold sliders created');
  }

  createFiveColorSlider(container, indicator) {
    const thresholds = this.getCurrentThresholds(indicator);

    const sliderDiv = document.createElement('div');
    sliderDiv.className = 'five-color-slider';
    sliderDiv.innerHTML = `
      <div class="slider-header">
        <span class="indicator-label">${indicator.toUpperCase()}</span>
        <div class="threshold-values">
          <span class="value-display">
            G:${thresholds.green} | B:${thresholds.blue} | N | O:${thresholds.orange} | R:${thresholds.red}
          </span>
        </div>
      </div>
      <div class="five-color-track" data-indicator="${indicator}">
        <!-- 5 color segments background -->
        <div class="color-segments-container">
          <div class="color-segment green-segment" style="width: ${thresholds.green}%"></div>
          <div class="color-segment blue-segment" style="width: ${thresholds.blue - thresholds.green}%"></div>
          <div class="color-segment neutral-segment" style="width: ${thresholds.orange - thresholds.blue}%"></div>
          <div class="color-segment orange-segment" style="width: ${thresholds.red - thresholds.orange}%"></div>
          <div class="color-segment red-segment" style="width: ${100 - thresholds.red}%"></div>
        </div>

        <!-- 4 draggable thumbs at crossover points -->
        <div class="threshold-thumb green-thumb" data-type="green" data-indicator="${indicator}"
             style="left: ${thresholds.green}%" title="Green threshold: ${thresholds.green}%">
          <div class="thumb-handle"></div>
        </div>
        <div class="threshold-thumb blue-thumb" data-type="blue" data-indicator="${indicator}"
             style="left: ${thresholds.blue}%" title="Blue threshold: ${thresholds.blue}%">
          <div class="thumb-handle"></div>
        </div>
        <div class="threshold-thumb orange-thumb" data-type="orange" data-indicator="${indicator}"
             style="left: ${thresholds.orange}%" title="Orange threshold: ${thresholds.orange}%">
          <div class="thumb-handle"></div>
        </div>
        <div class="threshold-thumb red-thumb" data-type="red" data-indicator="${indicator}"
             style="left: ${thresholds.red}%" title="Red threshold: ${thresholds.red}%">
          <div class="thumb-handle"></div>
        </div>
      </div>
    `;

    container.appendChild(sliderDiv);

    // Make thumbs draggable with proper 5-color logic
    const thumbs = sliderDiv.querySelectorAll('.threshold-thumb');
    thumbs.forEach(thumb => {
      this.makeFiveColorThumbDraggable(thumb, indicator);
    });

    this.thresholdSliders.set(indicator, sliderDiv);
  }

  makeFiveColorThumbDraggable(thumb, indicator) {
    let isDragging = false;
    let startX = 0;
    let startLeft = 0;

    thumb.addEventListener('mousedown', (e) => {
      isDragging = true;
      startX = e.clientX;
      startLeft = parseFloat(thumb.style.left) || 0;
      thumb.classList.add('dragging');
      e.preventDefault();
    });

    document.addEventListener('mousemove', (e) => {
      if (!isDragging) return;

      const track = thumb.parentElement;
      const trackRect = track.getBoundingClientRect();
      const deltaX = e.clientX - startX;
      const deltaPercent = (deltaX / trackRect.width) * 100;

      let newLeft = startLeft + deltaPercent;

      // Constrain based on 5-color logic order: green < blue < orange < red
      const type = thumb.getAttribute('data-type');
      const currentThresholds = window.thresholds[indicator] || {};

      switch(type) {
        case 'green':
          newLeft = Math.max(0, Math.min(newLeft, (currentThresholds.blue || 30) - 1));
          break;
        case 'blue':
          newLeft = Math.max((currentThresholds.green || 20) + 1, Math.min(newLeft, (currentThresholds.orange || 70) - 1));
          break;
        case 'orange':
          newLeft = Math.max((currentThresholds.blue || 30) + 1, Math.min(newLeft, (currentThresholds.red || 80) - 1));
          break;
        case 'red':
          newLeft = Math.max((currentThresholds.orange || 70) + 1, Math.min(newLeft, 100));
          break;
      }

      thumb.style.left = `${newLeft}%`;
      thumb.title = `${type.charAt(0).toUpperCase() + type.slice(1)} threshold: ${Math.round(newLeft)}%`;

      // Update threshold value
      if (!window.thresholds) window.thresholds = {};
      if (!window.thresholds[indicator]) window.thresholds[indicator] = {};
      window.thresholds[indicator][type] = Math.round(newLeft);

      // Update 5-color segments
      this.updateFiveColorSegments(indicator);
    });

    document.addEventListener('mouseup', () => {
      if (isDragging) {
        isDragging = false;
        thumb.classList.remove('dragging');
      }
    });
  }

  updateFiveColorSegments(indicator) {
    const slider = this.thresholdSliders.get(indicator);
    if (!slider) return;

    const thresholds = window.thresholds[indicator];
    const segments = slider.querySelectorAll('.color-segment');
    const valueDisplay = slider.querySelector('.value-display');

    // Update segment widths based on threshold positions
    if (segments.length >= 5) {
      segments[0].style.width = `${thresholds.green}%`; // Green segment
      segments[1].style.width = `${thresholds.blue - thresholds.green}%`; // Blue segment
      segments[2].style.width = `${thresholds.orange - thresholds.blue}%`; // Neutral segment
      segments[3].style.width = `${thresholds.red - thresholds.orange}%`; // Orange segment
      segments[4].style.width = `${100 - thresholds.red}%`; // Red segment
    }

    // Update value display
    if (valueDisplay) {
      valueDisplay.textContent = `G:${thresholds.green} | B:${thresholds.blue} | N | O:${thresholds.orange} | R:${thresholds.red}`;
    }
  }

  wireMLAILinkage() {
    console.log('[CoreSystemOverhaul] 🤖 Wiring ML/AI linkage...');

    // Find ML chart and ensure it's properly connected
    const mlChart = document.querySelector('#mlAnalysisChart');
    if (mlChart) {
      // Connect to WebSocket data updates
      document.addEventListener('indicatorsUpdated', (e) => {
        this.updateMLChart(e.detail);
      });

      // Connect to historical data
      document.addEventListener('historicalDataReceived', (e) => {
        this.updateMLHistoricalData(e.detail);
      });

      // Connect to strategy changes
      document.addEventListener('strategyChanged', (e) => {
        this.updateMLStrategy(e.detail);
      });
    }

    // Wire info pane to show ML insights
    this.wireInfoPane();

    console.log('[CoreSystemOverhaul] ✅ ML/AI linkage wired');
  }

  wireInfoPane() {
    console.log('[CoreSystemOverhaul] 📋 Wiring info pane...');

    // Find or create info pane
    let infoPane = document.querySelector('#mlAnalysisInsights, .ml-insights');
    if (!infoPane) {
      const mlContainer = document.querySelector('.ml-analysis-container');
      if (mlContainer) {
        const insightsDiv = document.createElement('div');
        insightsDiv.className = 'ml-insights';
        insightsDiv.id = 'mlAnalysisInsights';
        insightsDiv.innerHTML = 'Waiting for ML analysis data...';
        mlContainer.appendChild(insightsDiv);
        infoPane = insightsDiv;
      }
    }

    if (infoPane) {
      // Connect to circle clicks to show analysis
      document.addEventListener('signalCircleClicked', (e) => {
        const { indicator, timeframe, data } = e.detail;
        this.updateInfoPane(infoPane, indicator, timeframe, data);
      });

      // Connect to threshold changes
      document.addEventListener('thresholdsUpdated', (e) => {
        this.updateInfoPaneThresholds(infoPane, e.detail);
      });
    }

    console.log('[CoreSystemOverhaul] ✅ Info pane wired');
  }

  updateMLChart(data) {
    // Update ML chart with new indicator data
    console.log('[CoreSystemOverhaul] 📊 Updating ML chart with new data');
    // Implementation would connect to existing ML chart system
  }

  updateMLHistoricalData(data) {
    // Update ML chart with historical data for backtesting
    console.log('[CoreSystemOverhaul] 📈 Updating ML historical data');
    // Implementation would connect to existing historical data system
  }

  updateMLStrategy(strategy) {
    // Update ML analysis based on strategy change
    console.log('[CoreSystemOverhaul] 🎯 Updating ML strategy:', strategy);
    // Implementation would update ML parameters based on strategy
  }

  updateInfoPane(infoPane, indicator, timeframe, data) {
    const thresholds = this.getCurrentThresholds(indicator);
    const analysis = this.generateAnalysis(indicator, timeframe, data, thresholds);

    infoPane.innerHTML = `
      <div class="analysis-entry">
        <strong>${indicator.toUpperCase()} (${timeframe})</strong><br>
        Value: ${this.formatValue(data.value)}<br>
        Signal: ${data.signal || 'neutral'}<br>
        Thresholds: G:${thresholds.green} B:${thresholds.blue} O:${thresholds.orange} R:${thresholds.red}<br>
        Analysis: ${analysis}
      </div>
    `;
  }

  updateInfoPaneThresholds(infoPane, thresholds) {
    const currentContent = infoPane.innerHTML;
    infoPane.innerHTML = currentContent + `<br><em>Thresholds updated for current strategy</em>`;
  }

  generateAnalysis(indicator, timeframe, data, thresholds) {
    const value = parseFloat(data.value) || 0;

    if (value <= thresholds.green) return "Strong buy signal - value in green zone";
    if (value <= thresholds.blue) return "Buy signal - value in blue zone";
    if (value <= thresholds.orange) return "Neutral - value in neutral zone";
    if (value <= thresholds.red) return "Sell signal - value in orange zone";
    return "Strong sell signal - value in red zone";
  }

  resetThresholds() {
    console.log('[CoreSystemOverhaul] Resetting thresholds...');
    
    this.getCurrentStrategyIndicators().forEach(indicator => {
      const defaults = {
        red: 80,
        orange: 70,
        blue: 30,
        green: 20
      };
      
      if (!window.thresholds) window.thresholds = {};
      window.thresholds[indicator] = { ...defaults };
      
      // Update slider positions
      const slider = this.thresholdSliders.get(indicator);
      if (slider) {
        const thumbs = slider.querySelectorAll('.slider-thumb');
        thumbs.forEach(thumb => {
          const type = thumb.getAttribute('data-type');
          thumb.style.left = `${defaults[type]}%`;
        });
        this.updateThresholdDisplay(indicator);
      }
    });
  }

  applyThresholds() {
    console.log('[CoreSystemOverhaul] Applying thresholds...');
    
    // Save to localStorage
    localStorage.setItem('starCryptThresholds', JSON.stringify(window.thresholds));
    
    // Dispatch event
    document.dispatchEvent(new CustomEvent('thresholdsUpdated', {
      detail: window.thresholds
    }));
    
    // Force signal light update
    if (typeof window.forceUpdateSignalLights === 'function') {
      window.forceUpdateSignalLights();
    }
  }

  wireHistoricalAnalysis() {
    console.log('[CoreSystemOverhaul] 🔗 Wiring historical analysis...');

    // Find or create historical analysis container
    let historyContainer = document.querySelector('#mlHistoricalAnalysis, .ml-historical-analysis');

    if (!historyContainer) {
      // Create container if it doesn't exist
      const mlContainer = document.querySelector('#ml-visualization-container, .ml-visualization-container');
      if (mlContainer) {
        historyContainer = document.createElement('div');
        historyContainer.id = 'mlHistoricalAnalysis';
        historyContainer.className = 'ml-historical-analysis';
        historyContainer.innerHTML = `
          <div class="history-header">
            <h4>📊 Historical Analysis</h4>
            <button id="clearHistory" class="clear-button">Clear</button>
          </div>
          <div class="history-content" id="historyContent">
            <p class="no-data">Click signal circles to add analysis data</p>
          </div>
        `;
        mlContainer.appendChild(historyContainer);

        // Add clear button functionality
        document.getElementById('clearHistory').addEventListener('click', () => {
          this.clearHistoricalAnalysis();
        });
      }
    }

    console.log('[CoreSystemOverhaul] ✅ Historical analysis wired');
  }

  addToHistoricalAnalysis(data) {
    console.log('[CoreSystemOverhaul] 📊 Adding to historical analysis:', data);

    const historyContent = document.getElementById('historyContent');
    if (!historyContent) {
      console.warn('[CoreSystemOverhaul] History content container not found');
      return;
    }

    // Remove "no data" message
    const noDataMsg = historyContent.querySelector('.no-data');
    if (noDataMsg) {
      noDataMsg.remove();
    }

    // Create analysis entry
    const entry = document.createElement('div');
    entry.className = 'history-entry';
    entry.innerHTML = `
      <div class="entry-header">
        <span class="indicator-badge">${data.indicator.toUpperCase()}</span>
        <span class="timeframe-badge">${data.timeframe}</span>
        <span class="timestamp">${new Date(data.timestamp).toLocaleTimeString()}</span>
      </div>
      <div class="entry-data">
        <div class="value-info">
          <span class="label">Value:</span>
          <span class="value">${this.formatValue(data.value)}</span>
        </div>
        <div class="threshold-info">
          <span class="label">Thresholds:</span>
          <span class="thresholds">
            R:${data.thresholds.red} O:${data.thresholds.orange}
            B:${data.thresholds.blue} G:${data.thresholds.green}
          </span>
        </div>
        <div class="strategy-info">
          <span class="label">Strategy:</span>
          <span class="strategy">${data.strategy}</span>
        </div>
      </div>
    `;

    // Add to top of list
    historyContent.insertBefore(entry, historyContent.firstChild);

    // Limit to 20 entries
    const entries = historyContent.querySelectorAll('.history-entry');
    if (entries.length > 20) {
      entries[entries.length - 1].remove();
    }
  }

  formatValue(value) {
    if (typeof value === 'number') {
      return value.toFixed(2);
    }
    return value || 'N/A';
  }

  clearHistoricalAnalysis() {
    const historyContent = document.getElementById('historyContent');
    if (historyContent) {
      historyContent.innerHTML = '<p class="no-data">Click signal circles to add analysis data</p>';
    }
  }

  showActionableTooltip(circle, indicator, timeframe, data) {
    // Remove existing tooltips
    document.querySelectorAll('.actionable-tooltip').forEach(tooltip => {
      tooltip.remove();
    });

    // Create tooltip
    const tooltip = document.createElement('div');
    tooltip.className = 'actionable-tooltip';
    tooltip.innerHTML = `
      <div class="tooltip-header">
        <strong>${indicator.toUpperCase()} - ${timeframe}</strong>
      </div>
      <div class="tooltip-value">
        Value: ${this.formatValue(data.value)}
      </div>
      <div class="tooltip-signal">
        Signal: ${data.signal || 'neutral'}
      </div>
      <div class="tooltip-advice">
        💡 ${this.getActionableAdvice(indicator, data)}
      </div>
    `;

    // Position tooltip
    const rect = circle.getBoundingClientRect();
    tooltip.style.cssText = `
      position: fixed;
      left: ${rect.right + 10}px;
      top: ${rect.top}px;
      background: rgba(0, 20, 40, 0.95);
      border: 2px solid rgba(0, 255, 255, 0.6);
      border-radius: 8px;
      padding: 12px;
      color: #ffffff;
      font-family: 'Orbitron', sans-serif;
      font-size: 12px;
      max-width: 250px;
      z-index: 999999;
      box-shadow: 0 8px 32px rgba(0, 255, 255, 0.3);
    `;

    document.body.appendChild(tooltip);

    // Remove after 3 seconds
    setTimeout(() => {
      tooltip.remove();
    }, 3000);
  }

  getActionableAdvice(indicator, data) {
    const adviceMap = {
      'rsi': {
        'strong_sell': 'RSI overbought - consider taking profits',
        'sell': 'RSI elevated - watch for reversal',
        'neutral': 'RSI neutral - wait for clear signal',
        'buy': 'RSI oversold - potential entry opportunity',
        'strong_buy': 'RSI deeply oversold - strong buy signal'
      },
      'macd': {
        'strong_sell': 'MACD bearish momentum - reduce exposure',
        'sell': 'MACD weakening - trend may reverse',
        'neutral': 'MACD neutral - no clear direction',
        'buy': 'MACD building momentum - prepare entry',
        'strong_buy': 'MACD strong bullish - good entry signal'
      }
    };

    const signal = data.signal || 'neutral';
    return adviceMap[indicator]?.[signal] || `Monitor ${indicator.toUpperCase()} for trend changes`;
  }

  applyProfessionalStyling() {
    console.log('[CoreSystemOverhaul] 🎨 Applying professional styling...');

    const style = document.createElement('style');
    style.id = 'core-system-overhaul-css';
    style.textContent = `
      /* Enhanced Matrix Structure - Work with existing table */
      #momentum-table.enhanced-matrix-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
      }

      /* Hide any unnecessary headers that were added */
      #momentum-table.enhanced-matrix-table th {
        display: none !important;
      }

      /* Fix existing table rows */
      #momentum-table tr {
        height: 50px !important;
        border-bottom: 1px solid rgba(0, 255, 255, 0.1);
        transition: background 0.3s ease;
      }

      #momentum-table tr:hover {
        background: rgba(0, 255, 255, 0.05);
      }

      #momentum-table td {
        padding: 5px !important;
        vertical-align: middle !important;
        height: 50px !important;
        box-sizing: border-box;
      }

      /* Fix indicator name column */
      #momentum-table td:first-child {
        text-align: left !important;
        padding-left: 15px !important;
        width: 120px;
        min-width: 120px;
      }

      /* Fix mini chart column */
      #momentum-table td:nth-child(2) {
        width: 120px;
        min-width: 120px;
        padding: 5px !important;
        text-align: center;
      }

      /* Fix signal light columns - align to RIGHT with proper spacing */
      #momentum-table td:nth-child(n+3) {
        width: 50px !important;
        min-width: 50px !important;
        max-width: 50px !important;
        text-align: right !important;
        padding: 8px 15px 8px 8px !important;
        vertical-align: middle !important;
        position: relative !important;
        height: 60px !important;
      }

      /* Fix existing mini charts */
      #momentum-table [id*="Chart"],
      #momentum-table .mini-chart,
      #momentum-table canvas[id*="chart"] {
        height: 40px !important;
        max-height: 40px !important;
        min-height: 40px !important;
        width: 100% !important;
        display: block !important;
        border: 1px solid rgba(0, 255, 255, 0.2);
        border-radius: 4px;
        background: rgba(0, 20, 40, 0.3);
      }

      /* Hide any data overlays covering charts */
      #momentum-table .data-overlay,
      #momentum-table .chart-data,
      #momentum-table .indicator-data {
        display: none !important;
      }

      /* Fix existing signal circles - 40px with centered timeframe text */
      #momentum-table .signal-circle,
      #momentum-table .circle {
        width: 40px !important;
        height: 40px !important;
        border-radius: 50% !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        margin: 0 !important;
        float: right !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        border: 2px solid rgba(255, 255, 255, 0.3) !important;
        font-size: 10px !important;
        font-weight: bold !important;
        color: #ffffff !important;
        text-shadow: 0 0 3px rgba(0, 0, 0, 0.8) !important;
        font-family: 'Orbitron', sans-serif !important;
        box-sizing: border-box !important;
      }

      #momentum-table .signal-circle:hover,
      #momentum-table .circle:hover {
        transform: scale(1.1) !important;
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.7) !important;
        border-color: rgba(0, 255, 255, 0.8) !important;
      }

      /* Remove the ::after pseudo-element since text is now inside the circle */
      #momentum-table .signal-circle::after,
      #momentum-table .circle::after {
        display: none !important;
      }

      /* 5-Color Threshold Sliders */
      .threshold-header {
        text-align: center;
        margin-bottom: 20px;
        color: #00ffff;
      }

      .five-color-slider {
        margin-bottom: 20px;
        padding: 15px;
        background: rgba(0, 20, 40, 0.3);
        border: 1px solid rgba(0, 255, 255, 0.2);
        border-radius: 8px;
      }

      .slider-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
      }

      .indicator-label {
        color: #00ccff;
        font-weight: bold;
        font-family: 'Orbitron', sans-serif;
        font-size: 1rem;
      }

      .value-display {
        font-size: 0.8rem;
        color: #ffffff;
        font-family: 'Orbitron', sans-serif;
      }

      .five-color-track {
        position: relative;
        height: 30px;
        width: 100%;
        max-width: 300px;
        border: 2px solid rgba(0, 255, 255, 0.3);
        border-radius: 15px;
        margin: 10px 0;
        background: #000;
        overflow: visible;
      }

      .color-segments-container {
        display: flex;
        height: 100%;
        width: 100%;
        border-radius: 13px;
        overflow: hidden;
      }

      .color-segment {
        height: 100%;
        transition: width 0.3s ease;
        border: none;
      }

      .green-segment { background: #44ff44; }
      .blue-segment { background: #4488ff; }
      .neutral-segment { background: #888888; }
      .orange-segment { background: #ff8800; }
      .red-segment { background: #ff4444; }

      .threshold-thumb {
        position: absolute;
        width: 20px;
        height: 20px;
        top: 5px;
        cursor: grab;
        transition: transform 0.2s ease;
        margin-left: -10px;
        z-index: 15;
        pointer-events: all;
      }

      .thumb-handle {
        width: 100%;
        height: 100%;
        background: #ffffff;
        border: 3px solid #000000;
        border-radius: 50%;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
        transition: all 0.2s ease;
      }

      .threshold-thumb:hover .thumb-handle {
        transform: scale(1.2);
        box-shadow: 0 4px 12px rgba(0, 255, 255, 0.7);
      }

      .threshold-thumb.dragging .thumb-handle {
        cursor: grabbing;
        transform: scale(1.3);
        box-shadow: 0 6px 16px rgba(0, 255, 255, 0.9);
      }

      .green-thumb .thumb-handle { border-color: #44ff44; }
      .blue-thumb .thumb-handle { border-color: #4488ff; }
      .orange-thumb .thumb-handle { border-color: #ff8800; }
      .red-thumb .thumb-handle { border-color: #ff4444; }

      .threshold-actions {
        display: flex;
        gap: 10px;
        justify-content: center;
        margin-top: 20px;
      }

      .threshold-button {
        padding: 10px 20px;
        background: linear-gradient(135deg, rgba(0, 100, 200, 0.8), rgba(0, 60, 120, 0.9));
        border: 1px solid rgba(0, 255, 255, 0.4);
        border-radius: 5px;
        color: #ffffff;
        font-family: 'Orbitron', sans-serif;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .threshold-button:hover {
        background: linear-gradient(135deg, rgba(0, 150, 255, 0.8), rgba(0, 100, 180, 0.9));
        box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
      }

      /* Historical Analysis */
      .ml-historical-analysis {
        margin-top: 20px;
        background: rgba(0, 20, 40, 0.8);
        border: 1px solid rgba(0, 255, 255, 0.3);
        border-radius: 8px;
        padding: 15px;
      }

      .history-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        color: #00ffff;
      }

      .clear-button {
        padding: 5px 10px;
        background: rgba(255, 100, 100, 0.8);
        border: 1px solid rgba(255, 0, 0, 0.4);
        border-radius: 4px;
        color: white;
        font-size: 0.8rem;
        cursor: pointer;
      }

      .history-entry {
        margin-bottom: 10px;
        padding: 10px;
        background: rgba(0, 30, 60, 0.3);
        border: 1px solid rgba(0, 255, 255, 0.2);
        border-radius: 6px;
      }

      .entry-header {
        display: flex;
        gap: 10px;
        margin-bottom: 5px;
      }

      .indicator-badge, .timeframe-badge {
        padding: 2px 6px;
        background: rgba(0, 255, 255, 0.2);
        border-radius: 3px;
        font-size: 0.8rem;
        color: #00ffff;
      }

      .timestamp {
        margin-left: auto;
        font-size: 0.7rem;
        color: #888;
      }

      .entry-data {
        font-size: 0.8rem;
        color: #ccc;
      }

      .entry-data .label {
        color: #00ccff;
        font-weight: bold;
      }

      /* Actionable Tooltip */
      .actionable-tooltip {
        font-family: 'Orbitron', sans-serif;
      }

      .tooltip-header {
        color: #00ffff;
        margin-bottom: 5px;
      }

      .tooltip-value, .tooltip-signal {
        margin-bottom: 3px;
      }

      .tooltip-advice {
        margin-top: 8px;
        padding-top: 8px;
        border-top: 1px solid rgba(0, 255, 255, 0.3);
        color: #ffff88;
      }
    `;

    // Remove existing style if present
    const existingStyle = document.getElementById('core-system-overhaul-css');
    if (existingStyle) existingStyle.remove();

    document.head.appendChild(style);

    console.log('[CoreSystemOverhaul] ✅ Professional styling applied');
  }
}

// Initialize the core system overhaul
document.addEventListener('DOMContentLoaded', () => {
  // Disable all the broken modular fixes
  window.comprehensiveSystemFix = null;
  window.enhancedMLChartSystem = null;
  window.miniChartsFix = null;
  window.volumeSpikefix = null;
  window.finalCleanupFixes = null;
  window.matrixAlignmentFix = null;
  window.thresholdSliderFix = null;

  // Initialize the working system
  window.coreSystemOverhaul = new CoreSystemOverhaul();
});

// Also initialize if DOM is already loaded
if (document.readyState !== 'loading') {
  window.coreSystemOverhaul = new CoreSystemOverhaul();
}
