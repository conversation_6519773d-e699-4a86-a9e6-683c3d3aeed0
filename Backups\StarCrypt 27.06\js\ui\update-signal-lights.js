(function() {
  'use strict';

  // Configuration
  const UPDATE_THROTTLE_MS = 100; // Throttle updates to max once every 100ms
  
  // Track states to prevent recursive calls
  let isUpdating = false;
  let lastUpdateTime = 0;
  let updatePending = false; // Track if an update is pending
  const pendingUpdates = new Map(); // Track pending updates by indicator+timeframe
  
  // Add debug mode (disabled by default)
  window.DEBUG_SIGNALS = false;
  
  // Performance optimization: Cache DOM elements
  const signalElementsCache = new Map();
  
  // Debounce function to prevent rapid calls
  function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
    
    // Debounced version of the update function
    const debouncedUpdate = debounce(updateSignalLightsNonRecursive, UPDATE_THROTTLE_MS);
    
    /**
     * Public function to update all signal lights - optimized for performance
     */
    function updateAllSignalLights() {
      const now = Date.now();
    
      const UPDATE_THROTTLE_MS = window.signalSystem.config.updateThrottleMs; // Throttle updates to max once every 100ms

      // If we're already in the middle of an update, just return
      // The debounced function will handle scheduling the next update
      if (window.signalSystem && window.signalSystem.getSignalUpdateInProgress()) {
        return;
      }
    
      // Throttle updates to prevent excessive rendering
      if (now - lastUpdateTime < UPDATE_THROTTLE_MS) {
        return;
      }
    
      // Update the last update time
      lastUpdateTime = now;
    
      try {
        if (window.DEBUG_SIGNALS) console.log('Scheduling signal light update...');
    
        // Check if we have indicator data from the server
        if (!window.indicatorsData || Object.keys(window.indicatorsData).length === 0) {
          if (window.DEBUG_SIGNALS) console.log('No indicator data available');
          return;
        }
    
        // Set the flag to prevent multiple updates
        if (window.signalSystem) {
          window.signalSystem.setSignalUpdateInProgress(true);
        }
    
        // Use requestAnimationFrame for the next frame
        requestAnimationFrame(() => {
          try {
            // Perform the actual update using the debounced function
            debouncedUpdate();
          } catch (err) {
            console.error('Error in signal light update:', err);
          } finally {
            // Clear the in-progress flag after a small delay
            // to ensure we don't get into a tight loop
            setTimeout(() => {
              if (window.signalSystem) {
                window.signalSystem.setSignalUpdateInProgress(false);
              }
            }, 10);
          }
        });
      } catch (err) {
        console.error('Unexpected error in updateAllSignalLights:', err);
        if (window.signalSystem) {
          window.signalSystem.setSignalUpdateInProgress(false);
        }
      }
    }
    
    /**
     * Non-recursive implementation of signal light updates
     */
    function updateSignalLightsNonRecursive() {
      // Skip if no data available
      if (!window.indicatorsData) {
        console.warn('No indicator data available');
        return;
      }
    
      // Define indicator groups (moved from global scope to local)
      const INDICATORS = {
        momentum: ['rsi', 'stochRsi', 'macd', 'williamsR', 'mfi', 'ultimateOscillator'],
        trend: ['adx', 'bollingerBands', 'atr', 'vwap', 'fractal'],
        volume: ['volume', 'obv', 'cmf'],
        advanced: ['ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'],
      };
    
      // Define timeframes (moved from global scope to local)
      const TIMEFRAMES = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];
    
      // Batching to improve performance
      batchUpdateSignalLights(INDICATORS, TIMEFRAMES);
    }
    
    /**
     * Batch updates all signal lights with optimized rendering
     */
    function batchUpdateSignalLights(INDICATORS, TIMEFRAMES) {
      if (!INDICATORS || !TIMEFRAMES) {
        console.error('Invalid indicators or timeframes provided');
        return;
      }
    
      // Process updates in chunks to avoid blocking the main thread
      const CHUNK_SIZE = 10;
      const updates = [];
    
      // Prepare all updates
      for (const ind of INDICATORS) {
        for (const tf of TIMEFRAMES) {
          updates.push({ ind, tf });
        }
      }
    
      // Process chunks with requestIdleCallback for better performance
      const processChunk = (startIdx) => {
        const endIdx = Math.min(startIdx + CHUNK_SIZE, updates.length);
    
        // Process current chunk
        for (let i = startIdx; i < endIdx; i++) {
          const { ind, tf } = updates[i];
          updateSingleSignalLight(ind, tf);
        }
    
        // Schedule next chunk if there are more updates
        if (endIdx < updates.length) {
          requestIdleCallback(() => processChunk(endIdx), { timeout: 50 });
        } else {
          // All updates complete
          if (window.DEBUG_SIGNALS) console.log('All signal lights updated');
          isUpdating = false;
    
          // Check if there are pending updates
          if (updatePending) {
            updatePending = false;
            updateAllSignalLights();
          }
        }
      };
    
      // Start processing chunks
      processChunk(0);
    }
    
    // Track reported missing indicators to avoid console spam
    const reportedMissingIndicators = new Set();
    
    // The createSignalLight function was removed. The signal-matrix.js module is the single
    // source of truth for creating, adding, and removing signal circle elements from the DOM.
    
    /**
     * Updates a single signal light with performance optimizations
     */
    function updateSingleSignalLight(ind, tf) {
      try {
        const signalId = `signal-${ind}-${tf}`;
        const signalElement = document.getElementById(signalId);

        // If the signal element doesn't exist, it's because the current strategy
        // doesn't use this indicator, or the matrix has not been rendered yet.
        // We should not create it here. This module's job is to UPDATE, not create.
        if (!signalElement) {
          if (window.DEBUG_SIGNALS && !reportedMissingIndicators.has(signalId)) {
            // This warning is useful for debugging timing issues.
            console.warn(`[update-signal-lights] Signal element not found, cannot update: ${signalId}`);
            reportedMissingIndicators.add(signalId); // Report only once to prevent spam
          }
          return; // Exit gracefully.
        }
    
        // Get indicator data for this timeframe
        const indicatorData = window.indicatorsData?.[tf]?.[ind];
        if (!indicatorData) {
          if (window.DEBUG_SIGNALS) console.log(`No data for ${ind} on ${tf}`);
          signalElement.className = 'signal neutral';
          signalElement.title = `${ind.toUpperCase()} (${tf}): No data`;
          signalElement.style.backgroundColor = '#808080'; // Neutral gray
          return;
        }
    
        // Determine signal state based on indicator data
        let signalState = 'neutral';
        let tooltip = `${ind.toUpperCase()} (${tf}): `;
        let color = '#808080'; // Default neutral color (gray)
    
        // Custom logic for each indicator type
        try {
          switch (ind) {
            case 'rsi':
              if (indicatorData.value > 70) {
                signalState = 'bearish';
                color = '#FF0000'; // Red for overbought
              } else if (indicatorData.value < 30) {
                signalState = 'bullish';
                color = '#00FF00'; // Green for oversold
              }
              tooltip += `RSI: ${typeof indicatorData?.value === 'number' ? indicatorData.value.toFixed(2) : 'N/A'}`;
              break;
    
            case 'macd':
              if (indicatorData.histogram > 0) {
                signalState = 'bullish';
                color = '#00FF00'; // Green for bullish
              } else if (indicatorData.histogram < 0) {
                signalState = 'bearish';
                color = '#FF0000'; // Red for bearish
              }
              tooltip += `MACD: ${typeof indicatorData?.histogram === 'number' ? indicatorData.histogram.toFixed(4) : 'N/A'}`;
              break;
    
            case 'bollingerBands':
              if (indicatorData.upper && indicatorData.middle && indicatorData.lower) {
                const price = indicatorData.price || 0;
                const upper = indicatorData.upper;
                const lower = indicatorData.lower;
                const middle = indicatorData.middle;
    
                if (price > upper) {
                  signalState = 'overbought';
                  color = '#FF0000'; // Red for above upper band
                } else if (price < lower) {
                  signalState = 'oversold';
                  color = '#00FF00'; // Green for below lower band
                } else if (price > middle) {
                  signalState = 'bullish';
                  color = '#90EE90'; // Light green for upper half
                } else {
                  signalState = 'bearish';
                  color = '#FFA07A'; // Light red for lower half
                }
                tooltip += `BB: ${typeof price === 'number' ? price.toFixed(2) : 'N/A'} (${typeof lower === 'number' ? lower.toFixed(2) : 'N/A'}-${typeof upper === 'number' ? upper.toFixed(2) : 'N/A'})`;
              }
              break;
    
            // Add more indicator cases as needed
            default:
              // Default logic for other indicators
              if (indicatorData.value !== undefined) {
                if (indicatorData.value > 0.5) {
                  signalState = 'bullish';
                  color = '#00FF00';
                } else if (indicatorData.value < -0.5) {
                  signalState = 'bearish';
                  color = '#FF0000';
                }
                tooltip += `${ind}: ${typeof indicatorData?.value === 'number' ? indicatorData.value.toFixed(2) : 'N/A'}`;
              } else {
                tooltip += `${ind}: N/A`;
              }
          }
        } catch (e) {
          console.error(`Error processing ${ind} indicator:`, e);
          signalState = 'error';
          color = '#FF00FF'; // Magenta for errors
          tooltip += 'Error processing indicator';
        }

        // Add timestamp to the tooltip for data freshness
        const lastUpdate = indicatorData.timestamp ? new Date(indicatorData.timestamp).toLocaleString() : 'N/A';
        tooltip += `\nLast Update: ${lastUpdate}`;
    
        // Get the current color and tooltip for comparison
        const prevColor = signalElement.dataset.currentColor || '';
        const prevTooltip = signalElement.dataset.currentTooltip || '';
    
        // Only update if the color has changed
        if (prevColor !== color || prevTooltip !== tooltip) {
          // First remove all possible color classes
          signalElement.classList.remove('green-light', 'blue-light', 'orange-light', 'red-light', 'grey-light');
    
          // Remove all possible signal classes
          signalElement.classList.remove('degen-buy', 'mild-buy', 'neutral', 'mild-sell', 'degen-sell');
    
          // Add the appropriate color and signal classes
          signalElement.classList.add(signalState);
          signalElement.style.backgroundColor = color;
    
          // Set data attributes for reference
          signalElement.dataset.currentColor = color;
          signalElement.dataset.signalClass = signalState;
    
          // Update tooltip
          signalElement.title = tooltip;
          signalElement.dataset.currentTooltip = tooltip;
    
          // Log for debugging
          if (window.DEBUG_SIGNALS) {
            console.log(`Updated signal light ${ind}:${tf} to ${signalState} (${color})`);
          }
        }
    
        return true;
      } catch (error) {
        console.error(`Error updating signal light for ${ind} (${tf}):`, error);
        const signalId = `signal-${ind}-${tf}`;
        const signalElement = document.getElementById(signalId);
        if (signalElement) {
          signalElement.className = 'signal error';
          signalElement.style.backgroundColor = '#FF00FF'; // Magenta for errors
          signalElement.title = `Error: ${error.message || 'Unknown error'}`;
        }
        return false;
      }
    }
    
    /**
     * Updates the convergence indicators (like overall strategy strength)
     * Completely separate from the main signal light update to avoid recursion
     */
    function updateConvergenceIndicators() {
      try {
        // Update the strategy strength display if it exists
        const strengthElement = document.getElementById('strategyStrength');
        if (strengthElement) {
          // Calculate strength based on signal convergence
          const strength = calculateStrategyStrength();
          strengthElement.textContent = `${strength}%`;
    
          // Update color based on strength
          if (strength >= 80) {
            strengthElement.className = 'green-value';
          } else if (strength >= 60) {
            strengthElement.className = 'blue-value';
          } else if (strength >= 40) {
            strengthElement.className = 'neutral-value';
          } else if (strength >= 20) {
            strengthElement.className = 'orange-value';
          } else {
            strengthElement.className = 'red-value';
          }
        }
    
        // Other convergence indicators can be updated here
      } catch (err) {
        console.error('Error updating convergence indicators:', err);
      }
    }
    
    /**
     * Calculate the strategy strength based on signal convergence
     * Returns a percentage from 0-100
     */
    function calculateStrategyStrength() {
      // Default to 50% if we can't calculate
      let strength = 50;
    
      try {
        // Count green vs red signals as a very simple metric
        let green = 0;
        let red = 0;
        let total = 0;
    
        // Get all signal lights
        const signals = document.querySelectorAll('.signal-circle');
    
        signals.forEach(signal => {
          if (signal.classList.contains('green-light') || signal.classList.contains('degen-buy')) {
            green++;
          } else if (signal.classList.contains('blue-light') || signal.classList.contains('mild-buy')) {
            green += 0.5;
          } else if (signal.classList.contains('red-light') || signal.classList.contains('degen-sell')) {
            red++;
          } else if (signal.classList.contains('orange-light') || signal.classList.contains('mild-sell')) {
            red += 0.5;
          }
          total++;
        });
    
        if (total > 0) {
          // Balance between buy and sell signals
          const buyRatio = green / total;
          const sellRatio = red / total
    
          // More sophisticated strength calculation could be added here
          // For now, use a simple weighted average favoring buys
          strength = Math.max(0, Math.min(100, 50 + (buyRatio * 50) - (sellRatio * 50)));
        }
      } catch (err) {
        console.error('Error calculating strategy strength:', err);
        // Fallback to 50% if there's an error
        strength = 50;
      }
    
      return Math.round(strength);
    }



  // Make the functions available globally
  window.updateAllSignalLights = updateAllSignalLights;
  
  // Initialize the module
  console.log('Signal lights module initialized');
})();

// This is a fallback in case the module pattern fails
if (typeof window.updateSignalLightsScriptLoaded === 'undefined') {
  window.updateSignalLightsScriptLoaded = true;
  console.warn('Signal lights module loaded in compatibility mode');
}
