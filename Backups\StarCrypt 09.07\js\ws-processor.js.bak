/**
 * WebSocket Processor - Handles WebSocket message processing with WebSocketManager
 */
class WebSocketProcessor {
    constructor() {
        if (!window.WebSocketManager) {
            console.error('[WebSocket] WebSocketManager not found');
            return;
        }

        this.ws = window.WebSocketManager;
        this.messageHandlers = new Map();
        this.processedMessages = new Set();
        this.messageTypeTimestamps = new Map();
        this.lastMessageId = 0;
        this.isProcessing = false;
        
        // Configuration
        this.messageThrottle = 100; // ms between same message types
        this.maxProcessedMessages = 1000;
        this.batchSize = 5;
        this.batchDelay = 20;
        this.messageDelay = 10;
        
        // Bind methods
        this.handleIncomingMessage = this.handleIncomingMessage.bind(this);
        this.processQueue = this.processQueue.bind(this);
        this.handleError = this.handleError.bind(this);
        
        // Initialize
        this.initialize();
    }
    
    /**
     * Initialize the processor
     */
    initialize() {
        // Register message handler
        this.ws.onMessageType('message', this.handleIncomingMessage);
        
        // Register error handler
        document.addEventListener('websocket:error', (event) => {
            this.handleError(event.detail);
        });
        
        // Register connection status handlers
        document.addEventListener('websocket:connected', () => {
            console.log('[WebSocket] Connected to server');
            this.processQueue();
        });
        
        document.addEventListener('websocket:disconnected', (event) => {
            console.warn('[WebSocket] Disconnected from server:', event.detail);
        });
    }
    
    /**
     * Handle incoming message from WebSocketManager
     * @param {Object} message - The message to process
     */
    handleIncomingMessage(message) {
        if (!message || typeof message !== 'object') {
            console.warn('[WebSocket] Invalid message format:', message);
            return;
        }
        
        const now = Date.now();
        const messageType = message.type || 'unknown';
        const messageId = message._id || `msg_${now}_${++this.lastMessageId}`;
        
        // Skip if we've already processed this message
        if (this.processedMessages.has(messageId)) {
            console.debug(`[WebSocket] Skipping duplicate message: ${messageId}`);
            return;
        }
        
        // Check message type throttling
        const lastMessageTime = this.messageTypeTimestamps.get(messageType) || 0;
        if (now - lastMessageTime < this.messageThrottle) {
            console.debug(`[WebSocket] Throttling message type: ${messageType}`);
            return;
        }
        this.messageTypeTimestamps.set(messageType, now);
        
        // Add to processed messages with cleanup
        this.processedMessages.add(messageId);
        if (this.processedMessages.size > this.maxProcessedMessages) {
            // Keep the set size manageable
            const firstId = Array.from(this.processedMessages)[0];
            this.processedMessages.delete(firstId);
        }
        
        // Process the message
        this.processMessage(message);
    }
    
    /**
     * Process a single message
     * @param {Object} message - The message to process
     */
    async processMessage(message) {
        const { type, data, _id } = message;
        const startTime = performance.now();
        
        try {
            console.debug(`[WebSocket] Processing message ${_id} (${type})`);
            
            // Call registered handlers for this message type
            if (this.messageHandlers.has(type)) {
                const handlers = this.messageHandlers.get(type);
                for (const handler of handlers) {
                    try {
                        await handler(data);
                    } catch (error) {
                        console.error(`[WebSocket] Error in handler for ${type}:`, error);
                    }
                }
            }
            
            // Dispatch global event
            const event = new CustomEvent(`websocket:${type}`, { detail: data });
            document.dispatchEvent(event);
            
            const processTime = performance.now() - startTime;
            if (processTime > 100) {
                console.warn(`[WebSocket] Slow processing (${processTime.toFixed(2)}ms):`, type, _id);
            }
        } catch (error) {
            console.error(`[WebSocket] Error processing message ${_id} (${type}):`, error);
            this.handleError(error);
        }
    }
    
    /**
     * Register a message handler
     * @param {String} type - Message type to handle
     * @param {Function} handler - Handler function
     * @returns {Function} Unsubscribe function
     */
    onMessageType(type, handler) {
        if (typeof handler !== 'function') {
            throw new Error('Handler must be a function');
        }
        
        if (!this.messageHandlers.has(type)) {
            this.messageHandlers.set(type, new Set());
        }
        
        const handlers = this.messageHandlers.get(type);
        handlers.add(handler);
        
        // Return unsubscribe function
        return () => {
            if (this.messageHandlers.has(type)) {
                const handlers = this.messageHandlers.get(type);
                handlers.delete(handler);
                if (handlers.size === 0) {
                    this.messageHandlers.delete(type);
                }
            }
        };
    }
    
    /**
     * Process messages from the queue in batches
     */
    async processQueue() {
        if (this.isProcessing) {
            return;
        }
        
        this.isProcessing = true;
        
        try {
            // Process any queued messages
            while (this.messageQueue && this.messageQueue.length > 0) {
                const batch = this.messageQueue.splice(0, this.batchSize);
                
                // Process each message in the batch
                for (const message of batch) {
                    try {
                        await this.processMessage(message);
                        
                        // Small delay between messages
                        if (batch.length > 1) {
                            await new Promise(resolve => setTimeout(resolve, this.messageDelay));
                        }
                    } catch (error) {
                        console.error('[WebSocket] Error processing message:', error, message);
                        this.handleError(error);
                    }
                }
                
                // Small delay between batches
                if (this.messageQueue.length > 0) {
                    await new Promise(resolve => setTimeout(resolve, this.batchDelay));
                }
            }
        } catch (error) {
            console.error('[WebSocket] Error in processQueue:', error);
            this.handleError(error);
        } finally {
            this.isProcessing = false;
        }
    }
    
    /**
     * Handle errors
     * @param {Error} error - The error to handle
     */
    handleError(error) {
        console.error('[WebSocket] Error:', error);
        
        // Dispatch error event
        const errorEvent = new CustomEvent('websocket:error', {
            detail: {
                message: error.message,
                error: error,
                timestamp: Date.now()
            }
        });
        document.dispatchEvent(errorEvent);
    }
    
    /**
     * Send a message through the WebSocket
     * @param {Object} message - The message to send
     */
    send(message) {
        if (!this.ws) {
            console.error('[WebSocket] Not connected');
            return false;
        }
        
        try {
            return this.ws.send(message);
        } catch (error) {
            console.error('[WebSocket] Error sending message:', error);
            this.handleError(error);
            return false;
        }
    }
    
    /**
     * Clean up resources
     */
    cleanup() {
        // Remove all message handlers
        this.messageHandlers.clear();
        
        // Clear processed messages
        this.processedMessages.clear();
        
        // Clear timestamps
        this.messageTypeTimestamps.clear();
        
        // Reset state
        this.isProcessing = false;
    }
}

// Create and initialize singleton instance
if (!window.WebSocketProcessor) {
    window.WebSocketProcessor = new WebSocketProcessor();
    
    // Clean up on page unload
    window.addEventListener('beforeunload', () => {
        if (window.WebSocketProcessor) {
            window.WebSocketProcessor.cleanup();
        }
    });
}
