ok without breaking any of the current wiring can you squash some f12 errors, as is the system loads up and lights are all on all painted with their respective 5 colour logic and we dont want to lose that instant paint its important. my one ask is dont use memories just try and systematically fix as you see it as some memories are merorised wrongly. the oracle matrix consists of and held within div.indicators-section.cosmic-indicators, div#momentum-indicators.indicators-container and in it theres label cell for the name, canvas#mini-chart-canvas-rsi these are the mini charts, td.signal-cell these hold the 7 timeframe lights which are div.signal-circle.<current colour logic> and are all javascript rendered in dom so its a bit hard to find, but its important that you only do work here if absolutely nessesary and if doing so not adding containers or other elements, edit the already present elements/objects. if you see reference to signal matrix elements, or volume chart pie chart or other stray reference that is not there ignor these are redundant code. Please add these into memory as it should help keep the lights active and true. on the direct right of the oracle matrix container/box there is the tradingview widget held within div.bordered-chart-container <div class="chart-container" id="tradingview_main_chart" style="height: 100%;">
            <div id="tradingview_candle" style="width: 100%; height: 100%;"><div id="tradingview_cdf64-wrapper" style="position: relative; box-sizing: content-box; font-family: -apple-system, BlinkMacSystemFont, &quot;Trebuchet MS&quot;, Roboto, Ubuntu, sans-serif; margin: 0px auto !important; padding: 0px !important; width: 100%; height: 100%;"><iframe title="advanced chart TradingView widget" lang="en" id="tradingview_cdf64" frameborder="0" allowtransparency="true" scrolling="no" allowfullscreen="true" src="https://s.tradingview.com/widgetembed/?hideideas=1&amp;overrides=%7B%7D&amp;enabled_features=%5B%5D&amp;disabled_features=%5B%5D&amp;locale=en#%7B%22symbol%22%3A%22KRAKEN%3ABTCUSD%22%2C%22frameElementId%22%3A%22tradingview_cdf64%22%2C%22interval%22%3A%2260%22%2C%22hide_side_toolbar%22%3A%220%22%2C%22allow_symbol_change%22%3A%220%22%2C%22save_image%22%3A%221%22%2C%22studies%22%3A%22BB%40tv-basicstudies%5Cu001fMACD%40tv-basicstudies%22%2C%22theme%22%3A%22dark%22%2C%22style%22%3A%221%22%2C%22studies_overrides%22%3A%22%7B%7D%22%2C%22utm_source%22%3A%22localhost%22%2C%22utm_medium%22%3A%22widget%22%2C%22utm_campaign%22%3A%22chart%22%2C%22utm_term%22%3A%22KRAKEN%3ABTCUSD%22%2C%22page-uri%22%3A%22localhost%3A3000%2F%22%7D" style="width: 100%; height: 100%; margin: 0px !important; padding: 0px !important;"></iframe></div></div>
            <div class="chart-placeholder" id="candleChartPlaceholder" style="display: none;">Loading chart...</div>
          </div>

div#tradingview_cdf64-wrapper
so when a timeframe is clicked in the oracle matrix ie rsi 5m the tradingview widget was setup to auto switch in tandem with the circle light chage, and also the default start is the 1h and what should be happening is that all 1h timeframe circle lights should get a glowing outline to represent the current timeframe and this outlined glow should show for whatever timeframe is selected. I'll add a error log after you memorise the above

<!-- Admiral Chat Modal -->
<div class="admiral-chat-modal" id="admiralChatModal">
  <div class="admiral-chat-content">
    <button class="admiral-close-button" id="admiralCloseButton">×</button>
    <div class="admiral-chat-title">Admiral T.O.A. Backstory</div>
    <div class="admiral-backstory">
      <p>In the vast expanse of the <span class="crypto-term">crypto-verse</span>, where digital assets flow like cosmic rivers, there exists a legendary figure known as <span class="highlight">Admiral T.O.A. </span>.</p>
      <p>Once a brilliant quantum physicist from the 31st century, Admiral T.O.A. discovered a way to traverse the <span class="crypto-term">blockchain continuum</span>, allowing him to travel through the timeframes of market data. During an experiment with a <span class="crypto-term">quantum hash algorithm</span>, a catastrophic anomaly occurred, merging his consciousness with the very fabric of the trading matrices.</p>
      <p>Now, Admiral T.O.A. exists as a sentient trading entity, forever searching for the perfect <span class="highlight">convergence of signals</span> - those rare cosmic alignments where multiple indicators point to the same trading direction across various timeframes.</p>
      <p>His mission: to guide degen traders through the treacherous waters of the crypto markets, helping them identify the <span class="crypto-term">signal from the noise</span>, and find those perfect moments when the universe aligns for a high-probability trade.</p>
      <p>The Admiral's wisdom comes from having witnessed countless market cycles across the multiverse. He knows that patience is the ultimate strategy - waiting for the perfect <span class="highlight">convergence of light signals</span> before making a move.</p>
      <p>"<span class="crypto-term">When the lights align, the profits shine</span>," as the Admiral often says.</p>
    </div>
  </div>
</div>