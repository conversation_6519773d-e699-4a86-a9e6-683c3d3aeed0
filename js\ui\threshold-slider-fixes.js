/**
 * Threshold Slider Fixes for StarCrypt
 * Implements proper 4-thumb sliders with 5-color quadrant sections
 */

class ThresholdSliderFixes {
  constructor() {
    this.sliders = new Map();
    this.isDragging = false;
    this.activeThumb = null;
    this.dragStartX = 0;
    this.dragStartLeft = 0;
    this.liveUpdate = true;
    
    this.init();
  }

  init() {
    console.log('[ThresholdSliderFixes] Initializing proper 4-thumb sliders...');
    
    try {
      this.ensureDefaultThresholds();
      this.fixExistingSliders();
      this.addSliderStyles();
      this.bindGlobalEvents();
      
      console.log('[ThresholdSliderFixes] Threshold slider fixes applied successfully');
    } catch (error) {
      console.error('[ThresholdSliderFixes] Error applying threshold slider fixes:', error);
    }
  }

  ensureDefaultThresholds() {
    if (!window.defaultThresholds) {
      window.defaultThresholds = {
        rsi: { green: 20, blue: 40, orange: 60, red: 80 },
        stochRsi: { green: 15, blue: 35, orange: 65, red: 85 },
        williamsR: { green: 15, blue: 35, orange: 65, red: 85 },
        ultimateOscillator: { green: 20, blue: 40, orange: 60, red: 80 },
        mfi: { green: 15, blue: 35, orange: 65, red: 85 },
        adx: { green: 15, blue: 25, orange: 40, red: 60 },
        bollingerBands: { green: 2, blue: 10, orange: 90, red: 98 },
        macd: { green: 20, blue: 40, orange: 60, red: 80 },
        volume: { green: 15, blue: 35, orange: 65, red: 85 },
        sentiment: { green: 15, blue: 35, orange: 65, red: 85 },
        entropy: { green: 15, blue: 35, orange: 65, red: 85 },
        correlation: { green: 15, blue: 35, orange: 65, red: 85 },
        time_anomaly: { green: 15, blue: 35, orange: 65, red: 85 }
      };
    }

    if (!window.thresholds) {
      window.thresholds = JSON.parse(JSON.stringify(window.defaultThresholds));
    }
  }

  fixExistingSliders() {
    // Find all threshold slider containers
    const containers = [
      document.getElementById('threshold-sliders'),
      document.querySelector('.threshold-sliders'),
      document.querySelector('.enhanced-threshold-sliders'),
      document.querySelector('#thresholdsMenu .menu-content')
    ].filter(Boolean);

    containers.forEach(container => {
      this.enhanceSliderContainer(container);
    });

    // Monitor for new slider containers
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            if (node.id === 'threshold-sliders' || node.classList?.contains('threshold-sliders')) {
              this.enhanceSliderContainer(node);
            }
            
            const nestedSliders = node.querySelectorAll?.('#threshold-sliders, .threshold-sliders, .enhanced-threshold-sliders');
            nestedSliders?.forEach(slider => this.enhanceSliderContainer(slider));
          }
        });
      });
    });

    observer.observe(document.body, { childList: true, subtree: true });
  }

  enhanceSliderContainer(container) {
    if (!container || container.dataset.enhanced === 'true') return;
    
    container.dataset.enhanced = 'true';
    
    // Get current strategy indicators
    const currentStrategy = window.currentStrategy || 'admiral_toa';
    const strategyData = window.TRADING_STRATEGIES?.[currentStrategy];
    const indicators = strategyData?.indicators || Object.keys(window.defaultThresholds);

    // Clear and rebuild container
    container.innerHTML = this.generateSliderHTML(indicators);
    
    // Bind events for this container
    this.bindSliderEvents(container);
    
    console.log(`[ThresholdSliderFixes] Enhanced slider container with ${indicators.length} indicators`);
  }

  generateSliderHTML(indicators) {
    const header = `
      <div class="threshold-header">
        <h3>🎚️ Threshold Control</h3>
        <div class="threshold-controls">
          <button class="live-toggle ${this.liveUpdate ? 'active' : ''}" id="liveThresholdToggle">
            <span class="toggle-icon">⚡</span>
            Live Update
          </button>
          <button class="reset-all-btn" id="resetAllThresholds">
            <span class="reset-icon">🔄</span>
            Reset All
          </button>
        </div>
      </div>
    `;

    const sliders = indicators.map(indicator => this.generateSingleSliderHTML(indicator)).join('');
    
    return header + '<div class="sliders-container">' + sliders + '</div>';
  }

  generateSingleSliderHTML(indicator) {
    const thresholds = window.thresholds[indicator] || window.defaultThresholds[indicator];
    if (!thresholds) return '';

    const displayName = this.formatIndicatorName(indicator);
    
    return `
      <div class="threshold-slider-item" data-indicator="${indicator}">
        <div class="slider-header">
          <h4 class="indicator-name">${displayName}</h4>
          <div class="threshold-values" id="${indicator}-values">
            <span class="value-display green">G: ${thresholds.green}%</span>
            <span class="value-display blue">B: ${thresholds.blue}%</span>
            <span class="value-display orange">O: ${thresholds.orange}%</span>
            <span class="value-display red">R: ${thresholds.red}%</span>
          </div>
        </div>
        
        <div class="slider-container">
          <div class="slider-track" data-indicator="${indicator}">
            <!-- 5 Color Segments -->
            <div class="color-segment green-segment" style="left: 0%; width: ${thresholds.green}%"></div>
            <div class="color-segment blue-segment" style="left: ${thresholds.green}%; width: ${thresholds.blue - thresholds.green}%"></div>
            <div class="color-segment grey-segment" style="left: ${thresholds.blue}%; width: ${thresholds.orange - thresholds.blue}%"></div>
            <div class="color-segment orange-segment" style="left: ${thresholds.orange}%; width: ${thresholds.red - thresholds.orange}%"></div>
            <div class="color-segment red-segment" style="left: ${thresholds.red}%; width: ${100 - thresholds.red}%"></div>
            
            <!-- 4 Draggable Thumbs -->
            <div class="slider-thumb green-thumb" 
                 data-indicator="${indicator}" 
                 data-type="green" 
                 data-value="${thresholds.green}"
                 style="left: ${thresholds.green}%"
                 title="Green Threshold: ${thresholds.green}%">
              <span class="thumb-label">G</span>
            </div>
            <div class="slider-thumb blue-thumb" 
                 data-indicator="${indicator}" 
                 data-type="blue" 
                 data-value="${thresholds.blue}"
                 style="left: ${thresholds.blue}%"
                 title="Blue Threshold: ${thresholds.blue}%">
              <span class="thumb-label">B</span>
            </div>
            <div class="slider-thumb orange-thumb" 
                 data-indicator="${indicator}" 
                 data-type="orange" 
                 data-value="${thresholds.orange}"
                 style="left: ${thresholds.orange}%"
                 title="Orange Threshold: ${thresholds.orange}%">
              <span class="thumb-label">O</span>
            </div>
            <div class="slider-thumb red-thumb" 
                 data-indicator="${indicator}" 
                 data-type="red" 
                 data-value="${thresholds.red}"
                 style="left: ${thresholds.red}%"
                 title="Red Threshold: ${thresholds.red}%">
              <span class="thumb-label">R</span>
            </div>
          </div>
          
          <div class="slider-scale">
            <span class="scale-mark" style="left: 0%">0</span>
            <span class="scale-mark" style="left: 25%">25</span>
            <span class="scale-mark" style="left: 50%">50</span>
            <span class="scale-mark" style="left: 75%">75</span>
            <span class="scale-mark" style="left: 100%">100</span>
          </div>
        </div>
        
        <div class="slider-actions">
          <button class="reset-indicator-btn" data-indicator="${indicator}">Reset</button>
          <button class="preset-btn" data-indicator="${indicator}">Preset</button>
        </div>
      </div>
    `;
  }

  formatIndicatorName(indicator) {
    const names = {
      rsi: 'RSI',
      stochRsi: 'Stoch RSI',
      williamsR: 'Williams %R',
      ultimateOscillator: 'Ultimate Oscillator',
      mfi: 'Money Flow Index',
      adx: 'ADX',
      bollingerBands: 'Bollinger Bands',
      macd: 'MACD',
      volume: 'Volume',
      sentiment: 'Sentiment',
      entropy: 'Entropy',
      correlation: 'Correlation',
      time_anomaly: 'Time Anomaly'
    };
    return names[indicator] || indicator.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
  }

  bindSliderEvents(container) {
    // Bind thumb drag events
    const thumbs = container.querySelectorAll('.slider-thumb');
    thumbs.forEach(thumb => {
      thumb.addEventListener('mousedown', (e) => this.startDrag(e, thumb));
      thumb.addEventListener('touchstart', (e) => this.startDrag(e, thumb), { passive: false });
    });

    // Bind control buttons
    const liveToggle = container.querySelector('#liveThresholdToggle');
    if (liveToggle) {
      liveToggle.addEventListener('click', () => this.toggleLiveUpdate());
    }

    const resetAllBtn = container.querySelector('#resetAllThresholds');
    if (resetAllBtn) {
      resetAllBtn.addEventListener('click', () => this.resetAllThresholds());
    }

    // Bind individual reset buttons
    const resetBtns = container.querySelectorAll('.reset-indicator-btn');
    resetBtns.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const indicator = e.target.dataset.indicator;
        this.resetIndicatorThreshold(indicator);
      });
    });

    // Bind preset buttons
    const presetBtns = container.querySelectorAll('.preset-btn');
    presetBtns.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const indicator = e.target.dataset.indicator;
        this.showPresetMenu(indicator);
      });
    });
  }

  bindGlobalEvents() {
    // Global mouse events for dragging
    document.addEventListener('mousemove', (e) => this.handleDrag(e));
    document.addEventListener('mouseup', () => this.endDrag());
    document.addEventListener('touchmove', (e) => this.handleDrag(e), { passive: false });
    document.addEventListener('touchend', () => this.endDrag());
  }

  startDrag(e, thumb) {
    e.preventDefault();
    e.stopPropagation();
    
    this.isDragging = true;
    this.activeThumb = thumb;
    
    const clientX = e.clientX || e.touches?.[0]?.clientX;
    this.dragStartX = clientX;
    this.dragStartLeft = parseFloat(thumb.style.left) || 0;
    
    thumb.classList.add('dragging');
    document.body.style.userSelect = 'none';
    
    console.log(`[ThresholdSliderFixes] Started dragging ${thumb.dataset.type} thumb for ${thumb.dataset.indicator}`);
  }

  handleDrag(e) {
    if (!this.isDragging || !this.activeThumb) return;
    
    e.preventDefault();
    
    const clientX = e.clientX || e.touches?.[0]?.clientX;
    const thumb = this.activeThumb;
    const track = thumb.closest('.slider-track');
    const trackRect = track.getBoundingClientRect();
    
    // Calculate new position
    const deltaX = clientX - this.dragStartX;
    const trackWidth = trackRect.width;
    const deltaPercent = (deltaX / trackWidth) * 100;
    let newPosition = this.dragStartLeft + deltaPercent;
    
    // Constrain within bounds
    newPosition = Math.max(0, Math.min(100, newPosition));
    
    // Apply ordering constraints
    newPosition = this.constrainThumbPosition(thumb, newPosition);
    
    // Update thumb position
    thumb.style.left = `${newPosition}%`;
    thumb.dataset.value = Math.round(newPosition);
    thumb.title = `${thumb.dataset.type.charAt(0).toUpperCase() + thumb.dataset.type.slice(1)} Threshold: ${Math.round(newPosition)}%`;
    
    // Update threshold data
    const indicator = thumb.dataset.indicator;
    const type = thumb.dataset.type;
    window.thresholds[indicator][type] = Math.round(newPosition);
    
    // Update visual display
    this.updateSliderVisuals(indicator);
    
    // Live update if enabled
    if (this.liveUpdate) {
      this.applyThresholds();
    }
  }

  constrainThumbPosition(thumb, newPosition) {
    const indicator = thumb.dataset.indicator;
    const type = thumb.dataset.type;
    const track = thumb.closest('.slider-track');
    const allThumbs = track.querySelectorAll('.slider-thumb');
    
    // Get current positions of other thumbs
    const positions = {};
    allThumbs.forEach(t => {
      if (t !== thumb) {
        positions[t.dataset.type] = parseFloat(t.style.left) || 0;
      }
    });
    
    // Apply ordering constraints: green < blue < orange < red
    switch (type) {
      case 'green':
        if (positions.blue !== undefined) newPosition = Math.min(newPosition, positions.blue - 1);
        break;
      case 'blue':
        if (positions.green !== undefined) newPosition = Math.max(newPosition, positions.green + 1);
        if (positions.orange !== undefined) newPosition = Math.min(newPosition, positions.orange - 1);
        break;
      case 'orange':
        if (positions.blue !== undefined) newPosition = Math.max(newPosition, positions.blue + 1);
        if (positions.red !== undefined) newPosition = Math.min(newPosition, positions.red - 1);
        break;
      case 'red':
        if (positions.orange !== undefined) newPosition = Math.max(newPosition, positions.orange + 1);
        break;
    }
    
    return newPosition;
  }

  endDrag() {
    if (!this.isDragging) return;
    
    this.isDragging = false;
    
    if (this.activeThumb) {
      this.activeThumb.classList.remove('dragging');
      this.activeThumb = null;
    }
    
    document.body.style.userSelect = '';
    
    // Save thresholds
    this.saveThresholds();
    
    console.log('[ThresholdSliderFixes] Ended drag operation');
  }

  updateSliderVisuals(indicator) {
    const track = document.querySelector(`[data-indicator="${indicator}"] .slider-track`);
    if (!track) return;
    
    const thresholds = window.thresholds[indicator];
    const segments = track.querySelectorAll('.color-segment');
    const valueDisplays = document.querySelectorAll(`#${indicator}-values .value-display`);
    
    // Update segments
    if (segments.length >= 5) {
      segments[0].style.width = `${thresholds.green}%`;
      segments[0].style.left = '0%';
      
      segments[1].style.width = `${thresholds.blue - thresholds.green}%`;
      segments[1].style.left = `${thresholds.green}%`;
      
      segments[2].style.width = `${thresholds.orange - thresholds.blue}%`;
      segments[2].style.left = `${thresholds.blue}%`;
      
      segments[3].style.width = `${thresholds.red - thresholds.orange}%`;
      segments[3].style.left = `${thresholds.orange}%`;
      
      segments[4].style.width = `${100 - thresholds.red}%`;
      segments[4].style.left = `${thresholds.red}%`;
    }
    
    // Update value displays
    valueDisplays.forEach(display => {
      if (display.classList.contains('green')) display.textContent = `G: ${thresholds.green}%`;
      if (display.classList.contains('blue')) display.textContent = `B: ${thresholds.blue}%`;
      if (display.classList.contains('orange')) display.textContent = `O: ${thresholds.orange}%`;
      if (display.classList.contains('red')) display.textContent = `R: ${thresholds.red}%`;
    });
  }

  toggleLiveUpdate() {
    this.liveUpdate = !this.liveUpdate;
    
    const toggle = document.querySelector('#liveThresholdToggle');
    if (toggle) {
      toggle.classList.toggle('active', this.liveUpdate);
    }
    
    this.showNotification(`Live update ${this.liveUpdate ? 'enabled' : 'disabled'}`, 'info');
  }

  resetAllThresholds() {
    Object.keys(window.defaultThresholds).forEach(indicator => {
      window.thresholds[indicator] = { ...window.defaultThresholds[indicator] };
    });
    
    // Update all sliders
    document.querySelectorAll('.threshold-slider-item').forEach(item => {
      const indicator = item.dataset.indicator;
      this.updateSliderFromData(indicator);
    });
    
    this.saveThresholds();
    this.applyThresholds();
    this.showNotification('All thresholds reset to defaults', 'success');
  }

  resetIndicatorThreshold(indicator) {
    if (window.defaultThresholds[indicator]) {
      window.thresholds[indicator] = { ...window.defaultThresholds[indicator] };
      this.updateSliderFromData(indicator);
      this.saveThresholds();
      this.applyThresholds();
      this.showNotification(`${this.formatIndicatorName(indicator)} threshold reset`, 'success');
    }
  }

  updateSliderFromData(indicator) {
    const thresholds = window.thresholds[indicator];
    if (!thresholds) return;
    
    const track = document.querySelector(`[data-indicator="${indicator}"] .slider-track`);
    if (!track) return;
    
    // Update thumb positions
    const thumbs = track.querySelectorAll('.slider-thumb');
    thumbs.forEach(thumb => {
      const type = thumb.dataset.type;
      const value = thresholds[type];
      thumb.style.left = `${value}%`;
      thumb.dataset.value = value;
      thumb.title = `${type.charAt(0).toUpperCase() + type.slice(1)} Threshold: ${value}%`;
    });
    
    // Update visuals
    this.updateSliderVisuals(indicator);
  }

  saveThresholds() {
    localStorage.setItem('userThresholds', JSON.stringify(window.thresholds));
  }

  applyThresholds() {
    // Trigger threshold update events
    window.dispatchEvent(new CustomEvent('thresholdsUpdated', { 
      detail: { thresholds: window.thresholds } 
    }));
    
    // Update signal lights if function exists
    if (typeof window.updateAllSignalLights === 'function') {
      window.updateAllSignalLights();
    }
    
    // Update signal matrix if function exists
    if (typeof window.updateSignalMatrix === 'function') {
      window.updateSignalMatrix();
    }
  }

  showPresetMenu(indicator) {
    // Simple preset options
    const presets = {
      conservative: { green: 10, blue: 30, orange: 70, red: 90 },
      moderate: { green: 20, blue: 40, orange: 60, red: 80 },
      aggressive: { green: 30, blue: 45, orange: 55, red: 70 }
    };
    
    const preset = prompt(`Choose preset for ${this.formatIndicatorName(indicator)}:\n1. Conservative (10,30,70,90)\n2. Moderate (20,40,60,80)\n3. Aggressive (30,45,55,70)\n\nEnter 1, 2, or 3:`);
    
    if (preset === '1') {
      window.thresholds[indicator] = { ...presets.conservative };
    } else if (preset === '2') {
      window.thresholds[indicator] = { ...presets.moderate };
    } else if (preset === '3') {
      window.thresholds[indicator] = { ...presets.aggressive };
    } else {
      return;
    }
    
    this.updateSliderFromData(indicator);
    this.saveThresholds();
    this.applyThresholds();
    this.showNotification(`Applied ${preset === '1' ? 'conservative' : preset === '2' ? 'moderate' : 'aggressive'} preset`, 'success');
  }

  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `threshold-notification ${type}`;
    notification.textContent = message;
    
    Object.assign(notification.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      padding: '12px 20px',
      borderRadius: '6px',
      color: '#ffffff',
      fontWeight: 'bold',
      zIndex: '10000',
      opacity: '0',
      transform: 'translateX(100%)',
      transition: 'all 0.3s ease',
      backgroundColor: type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'
    });
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.style.opacity = '1';
      notification.style.transform = 'translateX(0)';
    }, 10);
    
    setTimeout(() => {
      notification.style.opacity = '0';
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }

  addSliderStyles() {
    const style = document.createElement('style');
    style.textContent = `
      .threshold-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #303045;
      }
      
      .threshold-header h3 {
        margin: 0;
        color: #00ffff;
        font-size: 18px;
      }
      
      .threshold-controls {
        display: flex;
        gap: 10px;
      }
      
      .live-toggle, .reset-all-btn {
        background: linear-gradient(135deg, #4ECDC4, #44A08D);
        border: 1px solid #4ECDC4;
        color: white;
        padding: 6px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 11px;
        font-weight: bold;
        display: flex;
        align-items: center;
        gap: 4px;
        transition: all 0.3s ease;
      }
      
      .live-toggle.active {
        background: linear-gradient(135deg, #00ff00, #32cd32);
        border-color: #00ff00;
      }
      
      .live-toggle:hover, .reset-all-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(78, 205, 196, 0.3);
      }
      
      .threshold-slider-item {
        margin-bottom: 25px;
        padding: 15px;
        background: rgba(26, 26, 46, 0.8);
        border: 1px solid #303045;
        border-radius: 8px;
      }
      
      .slider-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
      }
      
      .indicator-name {
        margin: 0;
        color: #ffffff;
        font-size: 14px;
      }
      
      .threshold-values {
        display: flex;
        gap: 10px;
        font-size: 11px;
      }
      
      .value-display {
        padding: 2px 6px;
        border-radius: 3px;
        font-weight: bold;
      }
      
      .value-display.green { background: #4CAF50; color: white; }
      .value-display.blue { background: #2196F3; color: white; }
      .value-display.orange { background: #FF9800; color: white; }
      .value-display.red { background: #f44336; color: white; }
      
      .slider-container {
        position: relative;
        margin-bottom: 15px;
      }
      
      .slider-track {
        position: relative;
        height: 30px;
        background: #1a1a2e;
        border: 1px solid #303045;
        border-radius: 15px;
        overflow: hidden;
      }
      
      .color-segment {
        position: absolute;
        height: 100%;
        transition: all 0.2s ease;
      }
      
      .green-segment { background: linear-gradient(90deg, #4CAF50, #66BB6A); }
      .blue-segment { background: linear-gradient(90deg, #2196F3, #42A5F5); }
      .grey-segment { background: linear-gradient(90deg, #9E9E9E, #BDBDBD); }
      .orange-segment { background: linear-gradient(90deg, #FF9800, #FFB74D); }
      .red-segment { background: linear-gradient(90deg, #f44336, #EF5350); }
      
      .slider-thumb {
        position: absolute;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 24px;
        height: 24px;
        border-radius: 50%;
        border: 2px solid #ffffff;
        cursor: grab;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        font-weight: bold;
        color: white;
        transition: all 0.2s ease;
        z-index: 10;
      }
      
      .slider-thumb:hover {
        transform: translate(-50%, -50%) scale(1.1);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      }
      
      .slider-thumb.dragging {
        cursor: grabbing;
        transform: translate(-50%, -50%) scale(1.2);
        z-index: 20;
      }
      
      .green-thumb { background: #4CAF50; }
      .blue-thumb { background: #2196F3; }
      .orange-thumb { background: #FF9800; }
      .red-thumb { background: #f44336; }
      
      .slider-scale {
        display: flex;
        justify-content: space-between;
        margin-top: 5px;
        font-size: 10px;
        color: #888;
      }
      
      .slider-actions {
        display: flex;
        gap: 8px;
        justify-content: flex-end;
      }
      
      .reset-indicator-btn, .preset-btn {
        background: linear-gradient(135deg, #6c5ce7, #a29bfe);
        border: 1px solid #6c5ce7;
        color: white;
        padding: 4px 8px;
        border-radius: 3px;
        cursor: pointer;
        font-size: 10px;
        transition: all 0.3s ease;
      }
      
      .reset-indicator-btn:hover, .preset-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(108, 92, 231, 0.3);
      }
    `;
    document.head.appendChild(style);
  }
}

// Initialize threshold slider fixes
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    window.thresholdSliderFixes = new ThresholdSliderFixes();
  }, 2000);
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ThresholdSliderFixes;
}
