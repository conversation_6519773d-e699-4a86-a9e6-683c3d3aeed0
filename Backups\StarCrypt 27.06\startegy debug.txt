<!-- Commenting out strategy-related CSS -->
<!-- <link rel="stylesheet" href="css/strategy-styles.css"> --> done
<!-- <link rel="stylesheet" href="css/strategy-animations.css"> --> not there in index

<!-- Commenting out strategy-related JS -->
<!-- <script src="js/ui/strategy-logic.js"></script> --> done
<!-- <script src="js/ui/strategy-selector.js"></script> --> done
<!-- <script src="js/ui/strategy-fix.js"></script> --> done
<!-- <script src="js/ui/strategy-comprehensive-fix.js"></script> --> not there
<!-- <script src="js/ui/strategy-persistence.js"></script> --> not there
<!-- <script src="js/ui/enhanced-strategy-animations.js"></script> --> done
<!-- <script src="js/ui/menu-strategy-fix.js"></script> --> done


<script>
  // Comment out strategy initialization
  /*
  document.addEventListener('DOMContentLoaded', function() {
    if (typeof initializeStrategySelectorModule === 'function') {
      initializeStrategySelectorModule();
    }
  });
  */
</script> not there


