/**
 * Unified Signal System - Combines signal management and rendering
 */
class SignalSystem {
  constructor() {
    // State
    this.isInitialized = false;
    this.isUpdating = false;
    this.lastUpdateTime = 0;
    this.updateTimer = null;
    this.signalState = new Map();
    this.pendingUpdates = new Map();
    this.elementCache = new Map();
    this.tooltipCache = new Map();
    this.updateQueue = [];
    this.isProcessingQueue = false;
    this.errorCount = 0;
    this.maxErrors = 5;
    this.updateInProgress = new Set(); // Tracks individual signal elements being updated
    this.currentTimeframe = '1h';
    this.isSignalUpdateInProgress = false; // Overall flag for update-signal-lights.js

    this.activeTimeframeGlowApplied = false;
    this.tradingViewChart = null;
    this.matrixContainer = null;

    // Configuration
    this.config = {
      updateDebounce: 50,
      maxUpdateTime: 500,
      maxBatchSize: 5,
      updateCooldown: 500,
      updateThrottleMs: 100, // Added for update-signal-lights.js
      signalColors: {
        'strong-buy': '#00FF00',
        'mild-buy': '#00AAFF',
        neutral: '#808080',
        'mild-sell': '#FFA500',
        'strong-sell': '#FF0000',
        error: '#FF00FF',
      },
      defaultTimeframes: ['1m', '5m', '15m', '1h', '4h', '1d', '1w'],
      batchSizes: {
        '1m': 3,
        '5m': 3,
        '15m': 2,
        '1h': 2,
        '4h': 1,
        '1d': 1,
        '1w': 1,
      },
      allowUpdatesBeforeInit: false,
      glowClass: 'golden-pulse-glow',
      debug: false, // Disabled by default in production
    };
  }

  /**
   * Set the TradingView chart instance and set up chart ready callback
   * @param {Object} chartInstance - The TradingView widget instance
   */
  setSignalUpdateInProgress(status) {
    this.isSignalUpdateInProgress = status;
  }

  getSignalUpdateInProgress() {
    return this.isSignalUpdateInProgress;
  }

  setTradingViewChart(chartInstance) {
    if (!chartInstance) {
      console.warn('[SignalSystem] Invalid TradingView chart instance provided');
      return;
    }

    this.tradingViewChart = chartInstance;
    if (this.config.debug) {
      console.log('[SignalSystem] TradingView chart instance linked');
    }

    if (typeof chartInstance.onChartReady === 'function') {
      chartInstance.onChartReady(() => {
        if (this.config.debug) {
          console.log('[SignalSystem] TradingView chart ready, syncing timeframe:', this.currentTimeframe);
        }
        this.syncTradingViewTimeframe(this.currentTimeframe);
      });
    } else {
      setTimeout(() => this.syncTradingViewTimeframe(this.currentTimeframe), 1000);
    }

    this.testTradingViewConnection();
  }

  /**
   * Update the glow effect for the selected timeframe
   * @param {string} timeframe - The selected timeframe
   */
  updateSelectedTimeframeGlow(timeframe) {
    if (!timeframe) {
      if (this.config.debug) console.warn('[SignalSystem] No timeframe provided for glow update');
      return false;
    }

    const normalizedTimeframe = timeframe.toLowerCase();
    if (this.config.debug) {
      console.log(`[SignalSystem] Updating timeframe glow to: ${normalizedTimeframe}`);
    }

    // Optimized selector to reduce DOM queries
    const selector = `.signal-circle[data-timeframe="${normalizedTimeframe}"]`;
    const circles = document.querySelectorAll(selector);
    document.querySelectorAll(`.${this.config.glowClass}`).forEach(el => {
      el.classList.remove(this.config.glowClass);
    });

    try {
      circles.forEach(circle => {
        circle.classList.add(this.config.glowClass);
        circle.style.display = 'block';
        circle.style.visibility = 'visible';
      });

      if (this.currentTimeframe !== normalizedTimeframe) {
        const previousTimeframe = this.currentTimeframe;
        this.currentTimeframe = normalizedTimeframe;
        if (this.config.debug) {
          console.log(`[SignalSystem] Timeframe changed from ${previousTimeframe || 'none'} to ${normalizedTimeframe}`);
        }
        this.syncTradingViewTimeframe(normalizedTimeframe);
        this.dispatchEvent('timeframeChanged', {
          previousTimeframe,
          newTimeframe: normalizedTimeframe,
        });
      }

      if (this.config.debug) {
        console.log(`[SignalSystem] Applied glow to ${circles.length} signal circles`);
      }
      return circles.length > 0;
    } catch (error) {
      console.error('[SignalSystem] Error updating timeframe glow:', error);
      this.handleError(error);
      return false;
    }
  }

  /**
   * Setup timeframe click listeners
   */
  setupTimeframeClickListeners() {
    if (!this.matrixContainer) {
      console.warn('[SignalSystem] Matrix container not available for click listeners');
      return;
    }

    this.matrixContainer.addEventListener('click', (event) => {
      const signalCircle = event.target.closest('.signal-circle');
      if (!signalCircle) return;

      const timeframe = signalCircle.dataset.timeframe;
      const indicator = signalCircle.dataset.indicator;

      if (timeframe) {
        this.handleTimeframeSelect(timeframe, indicator, signalCircle);
      } else {
        if (this.config.debug) {
          console.warn('[SignalSystem] Clicked signal circle missing data-timeframe attribute');
        }
      }
    });
  }

  /**
   * Handle timeframe selection
   * @param {string} timeframe - Selected timeframe
   * @param {string} indicator - Selected indicator
   * @param {Element} clickedCellElement - Clicked element
   */
  handleTimeframeSelect(timeframe, indicator, clickedCellElement) {
    if (this.currentTimeframe !== timeframe) {
      if (this.config.debug) {
        console.log(`[SignalSystem] Timeframe selected: ${timeframe}, indicator: ${indicator || 'N/A'}`);
      }
      this.currentTimeframe = timeframe;
      this.updateSelectedTimeframeGlow(timeframe);
      this.syncTradingViewTimeframe(timeframe);
      this.dispatchEvent('timeframeChanged', { timeframe, indicator });
    } else {
      if (this.config.debug) {
        console.log(`[SignalSystem] Timeframe ${timeframe} re-selected`);
      }
      this.updateSelectedTimeframeGlow(timeframe);
    }

    if (indicator && clickedCellElement) {
      const signalCircle = clickedCellElement.querySelector('.signal-circle') || clickedCellElement;
      const indicatorName = signalCircle.dataset.indicator;
      if (indicatorName) {
        this.dispatchEvent('signalClick', { indicator: indicatorName, timeframe });
      }
    }
  }

  /**
   * Sync the TradingView chart with the selected timeframe
   * @param {string} timeframe - The selected timeframe
   */
  syncTradingViewTimeframe(timeframe) {
    if (!timeframe) {
      console.warn('[SignalSystem] No timeframe provided for sync');
      return;
    }

    const widget = this.findTradingViewWidget();
    if (!widget) {
      console.warn('[SignalSystem] TradingView widget not available for timeframe sync');
      return;
    }

    const tvTimeframeMap = {
      '1m': '1',
      '5m': '5',
      '15m': '15',
      '1h': '60',
      '4h': '240',
      '1d': '1D',
      '1w': '1W',
    };

    const tvInterval = tvTimeframeMap[timeframe.toLowerCase()];
    if (!tvInterval) {
      console.warn(`[SignalSystem] Unknown timeframe for TradingView sync: ${timeframe}`);
      return;
    }

    try {
      if (typeof widget.onChartReady === 'function') {
        widget.onChartReady(() => this.updateTradingViewTimeframe(widget, tvInterval, timeframe));
      } else {
        this.updateTradingViewTimeframe(widget, tvInterval, timeframe);
      }
    } catch (error) {
      console.error('[SignalSystem] Error syncing TradingView timeframe:', error);
      this.handleError(error);
    }
  }

  /**
   * Update TradingView timeframe
   * @param {Object} widget - TradingView widget
   * @param {string} tvInterval - TradingView interval
   * @param {string} originalTimeframe - Original timeframe
   */
  updateTradingViewTimeframe(widget, tvInterval, originalTimeframe) {
    try {
      if (this.clickTradingViewTimeframeButton(widget, tvInterval)) {
        if (this.config.debug) {
          console.log(`[SignalSystem] Successfully clicked timeframe button for ${tvInterval}`);
        }
        return true;
      }

      console.log(`[SignalSystem] Button click failed, recreating widget with interval ${tvInterval}`);
      this.recreateWidgetWithInterval(tvInterval);
      return true;
    } catch (error) {
      console.error('[SignalSystem] Error in updateTradingViewTimeframe:', error);
      this.handleError(error);
      return false;
    }
  }

  /**
   * Click TradingView timeframe button
   * @param {Object} widget - TradingView widget
   * @param {string} tvInterval - TradingView interval
   */
  clickTradingViewTimeframeButton(widget, tvInterval) {
    try {
      if (!widget.iframe || !widget.iframe.contentDocument) {
        if (this.config.debug) {
          console.warn('[SignalSystem] Cannot access iframe content (cross-origin restriction)');
        }
        return false;
      }

      const iframeDoc = widget.iframe.contentDocument;
      const selectors = [
        `div[data-value='${tvInterval}']`,
        `div[data-name='interval-dialog-item'][data-value='${tvInterval}']`,
        `div[data-id='interval-dialog-item'][data-value='${tvInterval}']`,
        `div[data-interval='${tvInterval}']`,
        `button[data-value='${tvInterval}']`,
        `span[data-value='${tvInterval}']`,
        `[data-value="${tvInterval}"]`, // Original selector
        `[data-interval="${tvInterval}"]`, // Original selector
        `[aria-label*="${tvInterval}"]`, // Original selector
      ];

      for (const selector of selectors) {
        const button = iframeDoc.querySelector(selector);
        if (button) {
          if (this.config.debug) {
            console.log(`[SignalSystem] Found timeframe button with selector: ${selector}`);
          }
          button.click();
          return true;
        }
      }

      if (this.config.debug) {
        console.warn(`[SignalSystem] No timeframe button found for ${tvInterval}`);
      }
      return false;
    } catch (error) {
      if (this.config.debug) {
        console.warn('[SignalSystem] Error clicking timeframe button:', error.message);
      }
      return false;
    }
  }

  /**
   * Recreate TradingView widget with new interval
   * @param {string} interval - TradingView interval
   */
  recreateWidgetWithInterval(interval) {
    try {
      if (this.tradingViewChart && typeof this.tradingViewChart.remove === 'function') {
        this.tradingViewChart.remove();
      }

      const container = document.getElementById('tradingview_candle');
      if (container) {
        container.innerHTML = '';
      }

      if (typeof TradingView !== 'undefined') {
        const newWidget = new TradingView.widget({
          container_id: 'tradingview_candle',
          symbol: 'KRAKEN:BTCUSD',
          interval,
          width: '100%',
          height: '100%',
          locale: 'en',
          theme: 'dark',
          style: '1',
          toolbar_bg: '#1a1a2a',
          enable_publishing: false,
          allow_symbol_change: false,
          hide_side_toolbar: false,
          studies: ['BB@tv-basicstudies', 'MACD@tv-basicstudies'],
          autosize: true,
          disabled_features: ['header_widget', 'left_toolbar'],
          enabled_features: ['hide_left_toolbar_by_default'],
        });

        this.tradingViewChart = newWidget;
        window.tradingViewWidget = newWidget;
        if (window.StarCrypt && window.StarCrypt.tradingViewManager) {
          window.StarCrypt.tradingViewManager.chart = newWidget;
        }

        if (this.config.debug) {
          console.log(`[SignalSystem] Successfully recreated widget with interval ${interval}`);
        }
        return true;
      }
    } catch (error) {
      console.error('[SignalSystem] Error recreating widget:', error);
      this.handleError(error);
      return false;
    }
  }

  /**
   * Initialize the signal system
   */
  init() {
    if (this.isInitialized) return;
    if (this.config.debug) console.log('[SignalSystem] Initializing...');

    this.matrixContainer = document.querySelector('.indicators-section.cosmic-indicators');
    if (!this.matrixContainer) {
      console.error('[SignalSystem] CRITICAL: Matrix container not found');
      return;
    }

    // Get all signal elements and filter out invalid ones
    const signalsInMatrix = Array.from(this.matrixContainer.querySelectorAll('.signal-circle'));
    const validSignals = signalsInMatrix.filter(el => {
      const hasIndicator = el.hasAttribute('data-indicator');
      const hasTimeframe = el.hasAttribute('data-timeframe');
      return hasIndicator && hasTimeframe;
    });

    // Log summary of found signals
    if (signalsInMatrix.length === 0) {
      console.warn('[SignalSystem] No .signal-circle elements found in matrix container');
    } else {
      const invalidCount = signalsInMatrix.length - validSignals.length;
      if (invalidCount > 0) {
        console.warn(`[SignalSystem] Found ${invalidCount} invalid signal elements missing required attributes`);
      }
      if (this.config.debug) {
        console.log(`[SignalSystem] Found ${validSignals.length} valid signal circles`);
      }
    }

    // Initialize only valid signals
    validSignals.forEach(signalElement => {
      const indicator = signalElement.dataset.indicator;
      const timeframe = signalElement.dataset.timeframe;
      const key = `${indicator}:${timeframe}`;
      
      this.signalState.set(key, {
        value: null,
        strength: null,
        status: 'waiting',
        element: signalElement,
        lastUpdate: 0,
      });
      this.updateSignalElement(key, this.signalState.get(key));
    });

    this.setupTimeframeClickListeners();
    this.setupEventListeners();
    this.attemptTradingViewConnection();
    this.updateSelectedTimeframeGlow(this.currentTimeframe);

    this.isInitialized = true; // Moved to end to ensure full initialization
    if (this.config.debug) console.log('[SignalSystem] Initialized');

    this.dispatchEvent('ready', { signalSystem: this });
  }
  attemptTradingViewConnection() {
    if (this.tradingViewChart) {
      if (this.config.debug) console.log('[SignalSystem] TradingView chart already connected');
      return;
    }

    const widget = this.findTradingViewWidget();
    if (widget) {
      this.setTradingViewChart(widget);
    } else {
      let attempts = 0;
      const maxAttempts = 30;
      const checkInterval = setInterval(() => {
        attempts++;
        if (this.findTradingViewWidget() || attempts >= maxAttempts) {
          clearInterval(checkInterval);
          if (this.tradingViewChart) {
            this.setTradingViewChart(this.tradingViewChart);
          } else {
            console.warn('[SignalSystem] TradingView widget not found after 30 seconds');
          }
        }
      }, 1000);
    }
  }

  /**
   * Find TradingView widget
   */
  findTradingViewWidget() {
    if (window.StarCrypt?.tradingViewManager?.chart) {
      return window.StarCrypt.tradingViewManager.chart;
    }
    if (window.tradingViewWidget) {
      return window.tradingViewWidget;
    }
    const tvContainer = document.getElementById('tradingview_candle');
    if (tvContainer && tvContainer._tradingViewWidget) {
      return tvContainer._tradingViewWidget;
    }
    const iframe = document.querySelector('#tradingview_candle iframe');
    if (iframe && iframe.contentWindow) {
      return this.createTradingViewBridge(iframe);
    }
    return null;
  }

  /**
   * Create a bridge to communicate with TradingView iframe
   */
  createTradingViewBridge(iframe) {
    const bridge = {
      iframe,
      setSymbol: (symbol, interval, callback) => {
        try {
          const url = new URL(iframe.src);
          const hashData = decodeURIComponent(url.hash.substring(1));
          const widgetData = JSON.parse(hashData);
          widgetData.interval = interval;
          widgetData.symbol = symbol;
          url.hash = encodeURIComponent(JSON.stringify(widgetData));
          iframe.src = url.toString();
          if (callback) setTimeout(callback, 1000);
          return true;
        } catch (error) {
          console.warn('[TradingViewBridge] Error updating iframe URL:', error);
          return false;
        }
      },
      chart: () => ({
        setResolution: (interval, callback) => {
          return bridge.setSymbol(bridge.getCurrentSymbol(), interval, callback);
        },
      }),
      getCurrentSymbol: () => {
        try {
          const url = new URL(iframe.src);
          const hashData = decodeURIComponent(url.hash.substring(1));
          const widgetData = JSON.parse(hashData);
          return widgetData.symbol || 'KRAKEN:BTCUSD';
        } catch (error) {
          console.warn('[TradingViewBridge] Error getting current symbol:', error);
          return 'KRAKEN:BTCUSD';
        }
      },
      onChartReady: (callback) => {
        setTimeout(callback, 500);
      },
    };
    return bridge;
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    window.addEventListener('resize', this.debounce(this.handleWindowResize.bind(this), 100));

    if (this.matrixContainer) {
      this.matrixContainer.addEventListener('mouseover', (event) => {
        const signalCircle = event.target.closest('.signal-circle');
        if (signalCircle) this.handleSignalHover({ target: signalCircle });
      });
      this.matrixContainer.addEventListener('mouseout', (event) => {
        const signalCircle = event.target.closest('.signal-circle');
        if (signalCircle) this.handleSignalLeave({ target: signalCircle });
      });
    } else {
      console.warn('[SignalSystem] Matrix container not available for hover events');
      document.querySelectorAll('.signal-circle').forEach(signal => {
        signal.addEventListener('mouseenter', this.handleSignalHover.bind(this));
        signal.addEventListener('mouseleave', this.handleSignalLeave.bind(this));
      });
    }
  }

  /**
   * Update all signal lights
   * @param {boolean} force - Bypass rate limiting
   */
  updateAllSignalLights(force = false) {
    if (this.errorCount >= this.maxErrors) {
      console.warn('[SignalSystem] Updates disabled due to errors');
      return;
    }

    if (!force && !this.canUpdate()) return;

    this.isUpdating = true;
    try {
      const signals = this.matrixContainer?.querySelectorAll('.signal-circle') || [];
      signals.forEach(signal => {
        const indicator = signal.dataset.indicator;
        const timeframe = signal.dataset.timeframe;
        if (indicator && timeframe) {
          const key = `${indicator}:${timeframe}`;
          const data = this.signalState.get(key);
          if (data) this.updateSignalElement(key, data);
        }
      });
    } catch (error) {
      console.error('[SignalSystem] Error updating signals:', error);
      this.handleError(error);
    } finally {
      this.isUpdating = false;
    }
  }

  /**
   * Update a single signal
   * @param {string} indicator - Indicator name
   * @param {string} timeframe - Timeframe
   * @param {Object} data - Signal data
   */
  updateSignal(indicator, timeframe, data) {
    const key = `${indicator}:${timeframe}`;
    this.pendingUpdates.set(key, data);
    if (!this.isProcessingQueue) this.processUpdates();
  }

  /**
   * Process pending updates
   */
  processUpdates() {
    if (this.isProcessingQueue || this.pendingUpdates.size === 0) return;

    this.isProcessingQueue = true;
    try {
      const batch = Array.from(this.pendingUpdates.entries());
      for (const [key, data] of batch) {
        if (this.updateInProgress.has(key)) continue;
        const currentSignalInfo = this.signalState.get(key);
        if (currentSignalInfo) {
          currentSignalInfo.status = data.status ?? currentSignalInfo.status;
          currentSignalInfo.value = data.value ?? currentSignalInfo.value;
          currentSignalInfo.strength = data.strength ?? currentSignalInfo.strength;
          this.updateSignalElement(key, currentSignalInfo);
        }
      }
    } catch (error) {
      console.error('[SignalSystem] Error processing updates:', error);
      this.handleError(error);
    } finally {
      this.isProcessingQueue = false;
      this.pendingUpdates.clear();
    }
  }

  /**
   * Update a single signal element
   * @param {string} key - Signal key
   * @param {Object} signalData - Signal data
   */
  updateSignalElement(key, signalData) {
    if (!this.isInitialized && !this.config.allowUpdatesBeforeInit) {
      console.warn(`[SignalSystem] Update for ${key} ignored: system not initialized`);
      return;
    }

    const currentSignalInfo = this.signalState.get(key);
    if (!currentSignalInfo || !currentSignalInfo.element) {
      console.warn(`[SignalSystem] Signal element for ${key} not found`);
      this.handleError(new Error(`Missing signal element for ${key}`));
      return;
    }

    const signalElement = currentSignalInfo.element;
    if (signalData !== currentSignalInfo) {
      currentSignalInfo.status = signalData.status ?? currentSignalInfo.status;
      currentSignalInfo.value = signalData.value ?? currentSignalInfo.value;
      currentSignalInfo.strength = signalData.strength ?? currentSignalInfo.strength;
    }
    currentSignalInfo.lastUpdate = Date.now();

    signalElement.dataset.signalValue = currentSignalInfo.value ?? '';
    signalElement.dataset.signalStrength = currentSignalInfo.strength ?? '';
    signalElement.dataset.signalStatus = currentSignalInfo.status || 'neutral';

    let newClassName = 'signal-circle';
    const specificSignalClass = this.getSignalClass(currentSignalInfo.status, currentSignalInfo.strength);
    if (specificSignalClass) newClassName += ` ${specificSignalClass}`;
    else newClassName += ' neutral-light';

    if (currentSignalInfo.status === 'waiting') newClassName += ' signal-waiting';
    else if (currentSignalInfo.status === 'error') newClassName += ' signal-error';
    else if (currentSignalInfo.status && currentSignalInfo.status !== 'neutral') newClassName += ' signal-active';
    else newClassName += ' signal-inactive';

    signalElement.className = newClassName;
    if (signalElement.dataset.timeframe === this.currentTimeframe) {
      signalElement.classList.add(this.config.glowClass);
    } else {
      signalElement.classList.remove(this.config.glowClass);
    }
  }

  /**
   * Get signal class
   * @param {string} status - Signal status
   * @param {number} strength - Signal strength
   * @returns {string} - Signal class
   */
  getSignalClass(status, strength = 0.5) {
    return `${status.toLowerCase().replace(/[^a-z]/g, '-')}-light`;
  }

  /**
   * Handle errors with reset mechanism
   * @param {Error} error - Error object
   */
  handleError(error) {
    this.errorCount++;
    if (this.errorCount >= this.maxErrors) {
      console.warn('[SignalSystem] Too many errors, pausing updates');
      this.pauseUpdates();
      setTimeout(() => {
        this.errorCount = 0; // Reset error count after 10 seconds
        if (this.config.debug) console.log('[SignalSystem] Error count reset');
      }, 10000);
    }
  }

  /**
   * Pause signal updates
   */
  pauseUpdates() {
    this.isUpdating = false;
    this.isProcessingQueue = false;
    this.pendingUpdates.clear();
    this.updateInProgress.clear();
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
      this.updateTimer = null;
    }
  }

  /**
   * Check if updates are allowed
   * @returns {boolean}
   */
  canUpdate() {
    const now = Date.now();
    return (
      !this.isUpdating &&
      !this.isProcessingQueue &&
      now - this.lastUpdateTime >= this.config.updateCooldown
    );
  }

  /**
   * Debounce function
   * @param {Function} func - Function to debounce
   * @param {number} wait - Wait time in ms
   * @returns {Function}
   */
  debounce(func, wait) {
    let timeout;
    return (...args) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  }

  /**
   * Handle signal click
   * @param {Event} event - Click event
   */
  handleSignalClick(event) {
    const target = event.target.closest('.signal-circle');
    if (!target) return;

    const indicator = target.dataset.indicator;
    const timeframe = target.dataset.timeframe;

    if (!timeframe) {
      if (this.config.debug) console.warn('[SignalSystem] Clicked signal circle missing data-timeframe');
      return;
    }

    if (this.config.debug) {
      console.log(`[SignalSystem] Signal clicked: ${indicator}, ${timeframe}`);
    }

    this.updateSelectedTimeframeGlow(timeframe);
    this.dispatchEvent('signalClick', {
      indicator,
      timeframe,
      element: target,
    });
  }

  /**
   * Handle signal hover
   * @param {Event} event - Hover event
   */
  handleSignalHover(event) {
    const target = event.target.closest('.signal-circle');
    if (!target) return;

    const tooltip = target.querySelector('.signal-tooltip');
    if (tooltip && tooltip.offsetHeight) {
      tooltip.style.display = 'block';
      this.positionTooltip(target, tooltip);
    }
  }

  /**
   * Handle signal leave
   * @param {Event} event - Leave event
   */
  handleSignalLeave(event) {
    const target = event.target.closest('.signal-circle');
    if (!target) return;

    const tooltip = target.querySelector('.signal-tooltip');
    if (tooltip) tooltip.style.display = 'none';
  }

  /**
   * Position tooltip
   * @param {Element} signal - Signal element
   * @param {Element} tooltip - Tooltip element
   */
  positionTooltip(signal, tooltip) {
    const rect = signal.getBoundingClientRect();
    const tooltipRect = tooltip.getBoundingClientRect();
    tooltip.style.position = 'fixed';
    tooltip.style.left = `${rect.left + rect.width / 2 - tooltipRect.width / 2}px`;
    tooltip.style.top = `${rect.top - tooltipRect.height - 5}px`;
  }

  /**
   * Handle indicator toggle
   * @param {Event} event - Change event
   */
  handleIndicatorToggle(event) {
    const target = event.target.closest('[data-indicator]');
    if (!target) return;

    const indicator = target.dataset.indicator;
    const timeframe = target.dataset.timeframe || this.currentTimeframe;
    if (indicator && timeframe) {
      this.dispatchEvent('indicatorToggle', {
        indicator,
        timeframe,
        value: event.target.checked,
      });
    }
  }

  /**
   * Handle window resize
   */
  handleWindowResize() {
    this.updateAllSignalLights(true);
  }

  /**
   * Dispatch custom event
   * @param {string} type - Event type
   * @param {Object} detail - Event detail
   */
  dispatchEvent(type, detail) {
    document.dispatchEvent(new CustomEvent(`signalSystem:${type}`, { detail }));
  }

  /**
   * Test TradingView connection
   */
  testTradingViewConnection() {
    if (this.config.debug) {
      console.log('=== TradingView Connection Test ===');
      console.log('Current timeframe:', this.currentTimeframe);
      console.log('TradingView chart:', this.tradingViewChart);
      console.log('Widget sources:', {
        StarCrypt: !!window.StarCrypt?.tradingViewManager?.chart,
        Global: !!window.tradingViewWidget,
        DOM: !!document.getElementById('tradingview_candle'),
        Iframe: !!document.querySelector('#tradingview_candle iframe'),
      });

      const widget = this.findTradingViewWidget();
      console.log('Found widget:', widget);

      if (widget) {
        console.log('Widget methods:', {
          setSymbol: typeof widget.setSymbol,
          chart: typeof widget.chart,
          onChartReady: typeof widget.onChartReady,
        });
        this.syncTradingViewTimeframe('5m');
      } else {
        console.log('Attempting manual connection...');
        this.attemptTradingViewConnection();
      }
      console.log('=== End Test ===');
    }
  }
}

// Export
(function () {
  if (typeof module !== 'undefined' && module.exports) {
    module.exports = SignalSystem;
  } else if (typeof window !== 'undefined') {
    window.StarCrypt = window.StarCrypt || {};
    const initSignalSystem = () => {
      const signalSystem = new SignalSystem();
      window.StarCrypt.signalSystem = signalSystem;
      window.signalSystem = signalSystem;
      signalSystem.init();
    };
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initSignalSystem);
    } else {
      initSignalSystem();
    }
  }
})();