/**
 * Initialize and manage the global SignalLightsManager instance
 * This script ensures the signal manager is properly initialized and available globally
 */

// Create a global namespace if it doesn't exist
window.StarCrypt = window.StarCrypt || {}

// Initialize signal manager when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', () => {
  try {
    // Check if SignalManager already exists
    if (window.StarCrypt.SignalManager) {
      console.log('[SignalManager] Already initialized')
      return
    }

    // Create and expose the signal manager instance
    const signalManager = window.StarCrypt.SignalManager || {}

    // If SignalManager is not initialized, create it
    if (!signalManager.isInitialized) {
      // Initialize the SignalManager
      signalManager.isInitialized = true

      // Add update method
      signalManager.updateSignal = function (indicator, timeframe, data) {
        try {
          // Prevent immediate recursive updates
          if (signalManager.isUpdating) {
            console.log('[SignalManager] Already updating, queueing update')
            signalManager.updateQueue = signalManager.updateQueue || []
            signalManager.updateQueue.push({ indicator, timeframe, data })
            return
          }

          signalManager.isUpdating = true

          // Process updates with delay to prevent recursion
          setTimeout(() => {
            try {
              // Process any queued updates
              if (signalManager.updateQueue && signalManager.updateQueue.length > 0) {
                const queue = signalManager.updateQueue
                signalManager.updateQueue = []
                for (const update of queue) {
                  this.updateSignal(update.indicator, update.timeframe, update.data)
                }
              }

              // Process current update
              if (window.SignalLightsManager) {
                window.SignalLightsManager.updateSignal(indicator, timeframe, data)
              }

              // Update signal matrix
              if (window.updateSignalMatrix) {
                window.updateSignalMatrix()
              }
            } catch (error) {
              console.error('[SignalManager] Error processing update:', error)
            } finally {
              signalManager.isUpdating = false
            }
          }, 10)
        } catch (error) {
          console.error('[SignalManager] Error in updateSignal:', error)
        }
      }

      // Add event dispatching with proper state management
      signalManager.addEventListener = function (event, callback) {
        if (!signalManager._eventListeners) {
          signalManager._eventListeners = new Map()
        }
        const listeners = signalManager._eventListeners.get(event) || []

        // Check if callback already exists to prevent duplicates
        if (!listeners.some(cb => cb === callback)) {
          listeners.push(callback)
          signalManager._eventListeners.set(event, listeners)
        }
      }

      // Dispatch update events with proper error handling
      signalManager.dispatchEvent = function (event, data) {
        if (signalManager._eventListeners) {
          const listeners = signalManager._eventListeners.get(event)
          if (listeners) {
            // Process each listener with a small delay to prevent recursion
            listeners.forEach((callback, index) => {
              setTimeout(() => {
                try {
                  // Only proceed if not already updating
                  if (!signalManager.isUpdating) {
                    callback(data)
                  }
                } catch (error) {
                  console.error(`[SignalManager] Error in ${event} callback:`, error)
                  // Track errors but don't stop processing
                  signalManager.errorCount++
                  if (signalManager.errorCount >= signalManager.maxErrors) {
                    console.warn(`[SignalManager] Too many errors (${signalManager.errorCount}/${signalManager.maxErrors}), entering cooldown`)
                    // Clear update flag to allow recovery
                    signalManager.isUpdating = false
                  }
                }
              }, index * 10) // Small delay between callbacks
            })
          }
        }
      }

      // Add method to update timeframe
      signalManager.setTimeframe = function (timeframe) {
        try {
          if (!timeframe || typeof timeframe !== 'string') {
            console.warn('[SignalManager] Invalid timeframe:', timeframe)
            return false
          }

          // Only update if timeframe has changed
          if (this.currentTimeframe === timeframe) {
            console.log(`[SignalManager] Timeframe already set to ${timeframe}`)
            return true
          }

          console.log(`[SignalManager] Updating timeframe to ${timeframe}`)
          const previousTimeframe = this.currentTimeframe
          this.currentTimeframe = timeframe

          // Update any timeframe-dependent data
          this.dispatchEvent('timeframeChanged', {
            timeframe,
            previousTimeframe,
          })

          return true
        } catch (error) {
          console.error('[SignalManager] Error updating timeframe:', error)
          this.dispatchEvent('error', {
            type: 'timeframeUpdateError',
            error: error.message,
            stack: error.stack,
          })
          return false
        }
      }

      // Add method to get current timeframe
      signalManager.getCurrentTimeframe = function () {
        return this.currentTimeframe || '1h' // Default to 1h if not set
      }

      // Initialize with default timeframe
      signalManager.currentTimeframe = '1h'

      // Expose the manager to the global scope
      window.StarCrypt.SignalManager = signalManager

      // Initial update
      if (window.SignalLightsManager) {
        window.SignalLightsManager.updateAllSignalLights()
      }

      console.log('[SignalManager] Initialized with timeframe support')
    } else {
      console.log('[SignalManager] Already initialized, skipping setup')
    }

    console.log('[SignalManager] Initialized successfully')

    // Dispatch custom event when signal manager is ready
    const event = new CustomEvent('signalManager:ready', {
      detail: { signalManager },
    })
    document.dispatchEvent(event)
  } catch (error) {
    console.error('[SignalManager] Failed to initialize:', error)

    // Dispatch error event
    const errorEvent = new CustomEvent('signalManager:error', {
      detail: { error },
    })
    document.dispatchEvent(errorEvent)
  }
})

// Add error event listener for unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
  if (event.reason && event.reason.message &&
        (event.reason.message.includes('signal') ||
         event.reason.message.includes('SignalManager'))) {
    console.error('[SignalManager] Unhandled promise rejection:', event.reason)
    event.preventDefault() // Prevent browser's default error handling
  }
})

// Add error event listener for global errors
window.addEventListener('error', (event) => {
  if (event.message &&
        (event.message.includes('signal') ||
         event.message.includes('SignalManager'))) {
    console.error('[SignalManager] Global error:', event)
    event.preventDefault() // Prevent browser's default error handling
  }
})
