// Initialize the AI Dashboard component
import { AIDashboard } from './components/AIDashboard.js'

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', async () => {
  console.log('Initializing AI Dashboard...')

  try {
    // Create a container for the AI Dashboard if it doesn't exist
    let dashboardContainer = document.getElementById('ai-dashboard-container')
    if (!dashboardContainer) {
      dashboardContainer = document.createElement('div')
      dashboardContainer.id = 'ai-dashboard-container'
      dashboardContainer.className = 'ai-dashboard-container'
      document.body.appendChild(dashboardContainer)
    }

    // Initialize the dashboard
    const aiDashboard = new AIDashboard('ai-dashboard-container')
    await aiDashboard.initialize()
    console.log('AI Dashboard initialized successfully')
  } catch (error) {
    console.error('Failed to initialize AI Dashboard:', error)

    // Show error to the user
    const errorDiv = document.createElement('div')
    errorDiv.className = 'ai-dashboard-error'
    errorDiv.style.color = 'red'
    errorDiv.style.padding = '10px'
    errorDiv.style.margin = '10px'
    errorDiv.style.border = '1px solid red'
    errorDiv.style.borderRadius = '4px'
    errorDiv.textContent = `Failed to initialize AI Dashboard: ${error.message}`

    // Add to the container or body if container doesn't exist
    const container = document.getElementById('ai-dashboard-container') || document.body
    container.appendChild(errorDiv)
  }
})
