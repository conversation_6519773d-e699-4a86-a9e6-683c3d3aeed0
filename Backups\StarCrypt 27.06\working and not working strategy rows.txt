the main starting page now shows rows and elements for all indicators but there is a different structure being use for some can you make your edits at the source in index.html strictly direct in the html to see if that would work. any way go through every strategy. here is the strategy list with not working indicator rows from different strategies at the end of the read out, you will see that some of the strategies unfilled indicator rows are ones that we know are working start page so there is a different structure being used for them unfilled ones I want them removed and rebuilt into the actual script source that is filling and populating the start page can you try please, heres the list:

admiral toa convergence: all 17 are present and work
momentum blast: all 5 present all work
tight convergence: all 9 present, 6 fully working 3 not working, bollinger bands/atr (volitiliy)/vwap guardian
top/bottom feeder: all 7 present, 5 fully working 2 not working, williams %r/ultimate oscillator
scalping sniper:  all 5 present, 4 fully working 1 not working, bollinger bands
trend rider: all 7 present, 5 fully working 2 not working, vwap guardian/atr (volitility)
fractal surge: all 8 presnt, 5 fully working 3 not working, atr (volitility)/volume analysis/fractal pattern
x sentiment blaster: all 8 present, 5 fully working 3 not working, volume analysis/williams %r/sentiment analysis
quantum entropy: all 6 present, 5 fully working 1 not working, volume analysis
cross-asset nebula: cant switch doesnt respond maybe a missing strategy
time warp scalper: all 6 present, 5 fully working 1 not working, volume analysis
ai neural network navigator: all 8 present, all 8 fully working
ai deep learning diver: all 8 present, 7 fully working 1 not working, market entropy
ai pattern prophet: all 8 present, 7 fully working 1 not working, volume analysis
ai ml momentum master: all 7 present, all 7 fully working
ai sentiment analysis surfer: all 7 present, all 7 full working