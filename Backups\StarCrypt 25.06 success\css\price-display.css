/* Price Display Styles */
.price-display {
  font-family: 'Orbitron', 'Roboto', sans-serif;
  font-weight: bold;
  font-size: 1.5rem;
  text-align: center;
  margin: 0.5rem 0;
  letter-spacing: 1px;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.price-up {
  color: #00FF00;
  animation: pricePulseGreen 2s infinite;
}

.price-down {
  color: #FF3B30;
  animation: pricePulseRed 2s infinite;
}

.price-unchanged {
  color: #CCCCCC;
}

.price-change {
  font-size: 1rem;
  margin-left: 0.5rem;
  vertical-align: middle;
}

/* Price Change Indicators */
.price-change.positive {
  color: #4CD964;
}

.price-change.negative {
  color: #FF3B30;
}

/* Price Container */
.price-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background: rgba(10, 14, 23, 0.7);
  border-radius: 8px;
  margin: 0.5rem 0;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.price-container:hover {
  background: rgba(16, 22, 36, 0.8);
  box-shadow: 0 0 15px rgba(0, 180, 216, 0.3);
}

/* Price Label */
.price-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Price Chart Container */
.price-chart-container {
  width: 100%;
  height: 100px;
  margin-top: 1rem;
  position: relative;
}

/* Price Stats */
.price-stats {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 0.5rem;
  font-size: 0.8rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.7rem;
  margin-bottom: 0.2rem;
}

.stat-value {
  font-weight: bold;
}

/* Price History */
.price-history {
  width: 100%;
  margin-top: 1rem;
  font-size: 0.8rem;
}

.history-item {
  display: flex;
  justify-content: space-between;
  padding: 0.3rem 0;
  border-bottom: 1px solid var(--border-color);
}

.history-label {
  color: var(--text-secondary);
}

.history-value {
  font-weight: 500;
}

/* Animations */
@keyframes pricePulseGreen {
  0%, 100% { text-shadow: 0 0 10px rgba(0, 255, 0, 0.5); }
  50% { text-shadow: 0 0 20px rgba(0, 255, 0, 0.8); }
}

@keyframes pricePulseRed {
  0%, 100% { text-shadow: 0 0 10px rgba(255, 59, 48, 0.5); }
  50% { text-shadow: 0 0 20px rgba(255, 59, 48, 0.8); }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .price-display {
    font-size: 1.2rem;
  }
  
  .price-change {
    font-size: 0.9rem;
  }
  
  .price-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .stat-item {
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
  }
}
