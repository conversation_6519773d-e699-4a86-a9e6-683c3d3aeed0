/**
 * Strategy Manager - Centralized strategy management
 * Handles strategy selection, application, and UI updates
 */

class StrategyManager {
  constructor() {
    this.currentStrategy = null;
    this.initialized = false;
    this.strategies = window.TRADING_STRATEGIES || {};
    
    // Bind methods
    this.init = this.init.bind(this);
    this.setupEventListeners = this.setupEventListeners.bind(this);
    this.toggleMenu = this.toggleMenu.bind(this);
    this.closeMenu = this.closeMenu.bind(this);
    this.applySelectedStrategy = this.applySelectedStrategy.bind(this);
    this.updateStrategyUI = this.updateStrategyUI.bind(this);
    this.onStrategyApplied = this.onStrategyApplied.bind(this);
  }

  /**
   * Initialize the strategy manager
   */
  init() {
    if (this.initialized) {
      console.log('Strategy Manager already initialized');
      return;
    }
    
    try {
      // Initialize UI elements
      this.strategyButton = document.getElementById('strategyButton');
      this.strategyMenu = document.getElementById('strategyMenu');
      
      if (!this.strategyButton || !this.strategyMenu) {
        console.warn('Strategy elements not found in DOM');
        return;
      }
      
      // Create and populate strategy select if it doesn't exist
      let selectEl = this.strategyMenu.querySelector('select');
      if (!selectEl) {
        selectEl = document.createElement('select');
        selectEl.className = 'strategy-select';
        
        // Add strategy options
        Object.entries(this.strategies).forEach(([id, strategy]) => {
          const option = document.createElement('option');
          option.value = id;
          option.textContent = strategy.name;
          selectEl.appendChild(option);
        });
        
        this.strategyMenu.appendChild(selectEl);
      }
      
      // Create apply button if it doesn't exist
      if (!this.strategyMenu.querySelector('.apply-strategy')) {
        const applyBtn = document.createElement('button');
        applyBtn.className = 'apply-strategy';
        applyBtn.textContent = 'Apply Strategy';
        this.strategyMenu.appendChild(applyBtn);
      }
      
      this.setupEventListeners();
      this.initialized = true;
      console.log('Strategy Manager initialized successfully');
      
    } catch (error) {
      console.error('Error initializing Strategy Manager:', error);
    }
  }

  /**
   * Set up event listeners
   */
  setupEventListeners() {
    // Menu toggle
    this.strategyButton.addEventListener('click', (e) => {
      e.stopPropagation();
      this.toggleMenu();
    });

    // Apply strategy
    const applyBtn = this.strategyMenu.querySelector('.apply-strategy');
    if (applyBtn) {
      applyBtn.addEventListener('click', this.applySelectedStrategy);
    }

    // Close on outside click
    document.addEventListener('click', (e) => {
      if (this.strategyMenu && !this.strategyMenu.contains(e.target) && 
          this.strategyButton && !this.strategyButton.contains(e.target)) {
        this.closeMenu();
      }
    });
  }

  /**
   * Toggle the strategy menu visibility
   */
  toggleMenu() {
    if (!this.strategyMenu) return;
    
    const isVisible = this.strategyMenu.classList.toggle('visible');
    
    if (isVisible) {
      // Position menu below button
      const rect = this.strategyButton.getBoundingClientRect();
      this.strategyMenu.style.top = `${rect.bottom + window.scrollY}px`;
      this.strategyMenu.style.left = `${rect.left + window.scrollX}px`;
      
      // Update selected option to current strategy without auto-applying
      if (this.currentStrategy) {
        const select = this.strategyMenu.querySelector('select');
        if (select) {
          const option = Array.from(select.options).find(
            opt => opt.value === this.currentStrategy.id
          );
          if (option) {
            select.value = option.value;
          }
        }
      }
    }
  }

  /**
   * Close the strategy menu
   */
  closeMenu() {
    if (this.strategyMenu) {
      this.strategyMenu.classList.remove('visible');
    }
  }

  /**
   * Apply the selected strategy
   */
  applySelectedStrategy() {
    const select = this.strategyMenu?.querySelector('select');
    if (!select) return;
    
    const strategyId = select.value;
    this.currentStrategy = this.strategies[strategyId];
    
    if (!this.currentStrategy) {
      console.error('Selected strategy not found:', strategyId);
      return;
    }
    
    // Close menu after selection
    this.closeMenu();
    
    console.log(`Applying strategy: ${this.currentStrategy.name}`);
    
    // Update UI
    this.updateStrategyUI();
    this.closeMenu();
    
    // Trigger strategy application
    this.onStrategyApplied();
  }

  /**
   * Update UI elements to reflect current strategy
   */
  updateStrategyUI() {
    if (!this.currentStrategy) return;
    
    // Update strategy name display
    const strategyNameEl = document.getElementById('currentStrategyName');
    if (strategyNameEl) {
      strategyNameEl.textContent = this.currentStrategy.name;
    }
    
    // Update Oracle Matrix if available
    if (window.updateSignalMatrix) {
      window.updateSignalMatrix();
    }
    
    // Show strategy applied notification
    this.showNotification(`Strategy Applied: ${this.currentStrategy.name}`);
  }

  /**
   * Handle strategy application
   */
  onStrategyApplied() {
    // Emit custom event for other components
    const event = new CustomEvent('strategy:changed', {
      detail: { 
        strategy: this.currentStrategy,
        timestamp: Date.now()
      }
    });
    document.dispatchEvent(event);
    
    // Log strategy application
    console.group('Strategy Applied');
    console.log('Name:', this.currentStrategy.name);
    console.log('Indicators:', this.currentStrategy.indicators);
    console.groupEnd();
  }
  
  /**
   * Show a notification
   * @param {string} message - The message to display
   */
  showNotification(message) {
    // Create notification element if it doesn't exist
    let notification = document.getElementById('strategyNotification');
    if (!notification) {
      notification = document.createElement('div');
      notification.id = 'strategyNotification';
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 25px;
        background: rgba(0, 20, 40, 0.9);
        color: #fff;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        transform: translateX(120%);
        transition: transform 0.3s ease-in-out;
        max-width: 300px;
        word-wrap: break-word;
      `;
      document.body.appendChild(notification);
    }
    
    // Update and show notification
    notification.textContent = message;
    notification.style.transform = 'translateX(0)';
    
    // Auto-hide after 3 seconds
    setTimeout(() => {
      if (notification) {
        notification.style.transform = 'translateX(120%)';
      }
    }, 3000);
  }
}

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', () => {
  // Create global reference
  if (!window.strategyManager) {
    window.strategyManager = new StrategyManager();
    window.strategyManager.init();
  }
});

// Export for module systems
try {
  if (typeof module !== 'undefined' && module.exports) {
    module.exports = StrategyManager;
  }
} catch (e) { /* Not in a module system */ }
