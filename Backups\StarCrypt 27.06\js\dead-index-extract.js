// Extracted and unreferenced code from index.html (bloat removal)
// This file contains all logic, legacy objects, and UI code not referenced by any active module or UI element.
// You may reintegrate any section by moving it back to the appropriate JS or HTML file.

/* ========================================================================
   LEGACY/UNUSED STRATEGY OBJECTS (see lines ~6930+ in index.html)
   ======================================================================== */
const OLD_TRADING_STRATEGIES = {
  admiral_toa: {
    name: 'Admiral TOA',
    indicators: ['rsi', 'stochRsi', 'bollingerBands', 'atr', 'macd', 'williamsR', 'ultimateOscillator', 'mfi', 'adx'],
    helperText: `<p><strong>Step 1: Check Momentum (<span class=\"indicator\">RSI14</span> & <span class=\"indicator\">Stoch RSI</span>)</strong></p>\n<p>- Look for <span class=\"indicator\">RSI14</span> > <span class=\"condition-sell\">70 (overbought)</span> or < <span class=\"condition-buy\">30 (oversold)</span>. Confidence: ~65% for reversals.</p>\n<p>- Confirm with <span class=\"indicator\">Stoch RSI</span> > <span class=\"condition-sell\">80 (overbought)</span> or < <span class=\"condition-buy\">20 (oversold)</span>. Combined Confidence: ~70%.</p>\n<p><strong>Step 2: Confirm with <span class=\"indicator\">Bollinger Bands</span> & <span class=\"indicator\">ATR</span></strong></p>\n<p>- Price near the <span class=\"condition-sell\">upper band (overbought)</span> or <span class=\"condition-buy\">lower band (oversold)</span> with high <span class=\"indicator\">ATR</span> (>1% of price) increases reversal likelihood. Confidence: ~75% when combined with <span class=\"indicator\">RSI</span>/<span class=\"indicator\">Stoch RSI</span>.</p>\n<p><strong>Step 3: Look for <span class=\"indicator\">MACD</span> Convergence </strong></p>\n<p>- <span class=\"indicator\">MACD</span> crossing <span class=\"condition-buy\">above signal line (bullish)</span> or <span class=\"condition-sell\">below (bearish)</span> with <span class=\"indicator\">RSI</span>/<span class=\"indicator\">Stoch RSI</span> confirmation. Confidence: ~80% for strong signals.</p>\n<p><strong>Step 4: Additional Confirmation (<span class=\"indicator\">Williams %R</span>, <span class=\"indicator\">UltOscillator</span>, <span class=\"indicator\">MFI</span>)</strong></p>\n<p>- <span class=\"indicator\">Williams %R</span>, <span class=\"indicator\">UltOscillator</span>, and <span class=\"indicator\">MFI</span> aligning with <span class=\"indicator\">RSI</span>/<span class=\"indicator\">Stoch RSI</span> (e.g., all overbought) boosts confidence. Confidence: ~85% with 4+ indicators.</p>\n<p><strong>Step 5: Check Trend Strength (<span class=\"indicator\">ADX</span>)</strong></p>\n<p>- <span class=\"indicator\">ADX</span> > <span class=\"condition-trend\">25</span> confirms a strong trend, increasing trade reliability. Final convergence confidence: ~90% with all steps aligned.</p>`,
  },
  fractal_surge: {
    name: 'Fractal Surge',
    indicators: ['rsi', 'macd', 'bollingerBands', 'williamsR', 'adx'],
    helperText: `<p><strong>Step 1: Identify Fractal Pattern (<span class=\"indicator\">Bollinger Bands</span>)</strong></p>\n<p>- Look for price <span class=\"condition-buy\">breaking out of lower band</span> or <span class=\"condition-sell\">breaking out of upper band</span> after period of low volatility (band squeeze).</p>\n<p><strong>Step 2: Confirm Momentum Shift (<span class=\"indicator\">RSI</span>)</strong></p>\n<p>- <span class=\"indicator\">RSI</span> showing divergence from price or crossing <span class=\"condition-buy\">above 40</span> from below or <span class=\"condition-sell\">below 60</span> from above.</p>\n<p><strong>Step 3: Validate with <span class=\"indicator\">MACD</span></strong></p>\n<p>- <span class=\"indicator\">MACD</span> histogram showing increasing momentum in direction of breakout.</p>\n<p><strong>Step 4: Confirm Trend Strength (<span class=\"indicator\">ADX</span> & <span class=\"indicator\">Williams %R</span>)</strong></p>\n<p>- <span class=\"indicator\">ADX</span> > <span class=\"condition-trend\">20</span> and rising, with <span class=\"indicator\">Williams %R</span> confirming direction.</p>`,
  },
  x_sentiment_blaster: {
    name: 'X Sentiment Blaster',
    indicators: ['rsi', 'macd', 'mfi', 'ultimateOscillator', 'adx'],
    helperText: `<p><strong>Step 1: Gauge Market Sentiment (<span class=\"indicator\">MFI</span>)</strong></p>\n<p>- <span class=\"indicator\">MFI</span> < <span class=\"condition-buy\">20</span> indicates strong buying opportunity, > <span class=\"condition-sell\">80</span> indicates selling opportunity.</p>\n<p><strong>Step 2: Confirm with <span class=\"indicator\">Ultimate Oscillator</span></strong></p>\n<p>- <span class=\"indicator\">Ultimate Oscillator</span> showing similar extreme readings as MFI.</p>\n<p><strong>Step 3: Check <span class=\"indicator\">RSI</span> for Confirmation</strong></p>\n<p>- <span class=\"indicator\">RSI</span> aligning with MFI and Ultimate Oscillator readings.</p>\n<p><strong>Step 4: Validate with <span class=\"indicator\">MACD</span> & <span class=\"indicator\">ADX</span></strong></p>\n<p>- <span class=\"indicator\">MACD</span> showing crossover in direction of sentiment, with <span class=\"indicator\">ADX</span> > <span class=\"condition-trend\">25</span>.</p>`,
  },
  quantum_entropy: {
    name: 'Quantum Entropy',
    indicators: ['bollingerBands', 'atr', 'adx', 'williamsR', 'macd'],
    helperText: `<p><strong>Step 1: Measure Market Chaos (<span class=\"indicator\">ATR</span>)</strong></p>\n<p>- <span class=\"indicator\">ATR</span> > <span class=\"condition-trend\">1.5%</span> of price indicates high volatility, < <span class=\"condition-trend\">0.5%</span> indicates low volatility.</p>\n<p><strong>Step 2: Identify Volatility Breakouts (<span class=\"indicator\">Bollinger Bands</span>)</strong></p>\n<p>- After period of band contraction (low entropy), look for expansion and price breaking <span class=\"condition-buy\">above middle band</span> or <span class=\"condition-sell\">below middle band</span>.</p>\n<p><strong>Step 3: Confirm Direction (<span class=\"indicator\">Williams %R</span>)</strong></p>\n<p>- <span class=\"indicator\">Williams %R</span> crossing <span class=\"condition-buy\">above -50</span> from below or <span class=\"condition-sell\">below -50</span> from above.</p>\n<p><strong>Step 4: Validate with <span class=\"indicator\">MACD</span> & <span class=\"indicator\">ADX</span></strong></p>\n<p>- <span class=\"indicator\">MACD</span> histogram expanding in direction of breakout, with <span class=\"indicator\">ADX</span> rising above <span class=\"condition-trend\">20</span>.</p>`,
  },
  cross_asset_nebula: {
    name: 'Cross-Asset Nebula',
    indicators: ['rsi', 'macd', 'bollingerBands', 'adx', 'atr'],
    helperText: `<p><strong>Step 1: Identify Cross-Asset Correlations</strong></p>\n<p>- Monitor correlations between crypto and traditional markets (BTC/ETH vs. S&P500, Gold, DXY).</p>\n<p><strong>Step 2: Look for Correlation Breakdowns (<span class=\"indicator\">RSI</span>)</strong></p>\n<p>- When <span class=\"indicator\">RSI</span> diverges from correlated assets, potential opportunity emerges.</p>\n<p><strong>Step 3: Confirm with <span class=\"indicator\">Bollinger Bands</span> & <span class=\"indicator\">ATR</span></strong></p>\n<p>- <span class=\"indicator\">Bollinger Bands</span> showing expansion with increased <span class=\"indicator\">ATR</span> signals potential trend change.</p>\n<p><strong>Step 4: Validate Direction (<span class=\"indicator\">MACD</span> & <span class=\"indicator\">ADX</span>)</strong></p>\n<p>- <span class=\"indicator\">MACD</span> crossing signal line with <span class=\"indicator\">ADX</span> > <span class=\"condition-trend\">20</span> confirms trend direction.</p>`,
  },
  time_warp_scalper: {
    name: 'Time Warp Scalper',
    indicators: ['rsi', 'stochRsi', 'bollingerBands', 'macd', 'williamsR'],
    helperText: `<p><strong>Step 1: Identify Time Frame Convergence (<span class=\"indicator\">RSI</span> & <span class=\"indicator\">Stoch RSI</span>)</strong></p>\n<p>- Look for <span class=\"indicator\">RSI</span> and <span class=\"indicator\">Stoch RSI</span> alignment across multiple timeframes (1m, 5m, 15m).</p>\n<p><strong>Step 2: Confirm with <span class=\"indicator\">Bollinger Bands</span></strong></p>\n<p>- Price touching or crossing <span class=\"condition-buy\">lower band</span> or <span class=\"condition-sell\">upper band</span> on multiple timeframes.</p>\n<p><strong>Step 3: Check <span class=\"indicator\">MACD</span> for Confirmation</strong></p>\n<p>- <span class=\"indicator\">MACD</span> histogram showing momentum shift in same direction across timeframes.</p>\n<p><strong>Step 4: Final Validation (<span class=\"indicator\">Williams %R</span>)</strong></p>\n<p>- <span class=\"indicator\">Williams %R</span> confirming oversold/overbought conditions across timeframes.</p>`,
  },
}

/* ========================================================================
   BIOGRAPHY/INFO PANELS (not referenced directly by modules)
   ======================================================================== */
const admiralToaBiography = `
  <div class="admiral-bio">
    <h3>Admiral T.O.A. - Time Of Arrival</h3>
    <p>Admiral Time Of Arrival, known throughout the crypto cosmos as Admiral T.O.A., is a legendary figure who traverses the vast expanses of the trading universe in search of the rarest phenomenon in existence: the Mass Convergence of Light Signals.</p>
    <p>Born in the Satoshi Nebula during the great Bitcoin Genesis Block, Admiral T.O.A. was gifted with an extraordinary ability to perceive patterns in market chaos that others could not see. His eyes, rumored to be enhanced with quantum neural implants, can process thousands of timeframes simultaneously, identifying the precise moment when multiple indicators align across the spectrum.</p>
    <p>As captain of the StarCrypt Enterprise, he navigates the treacherous waters of market volatility, always following the sweet $SCENT of safer liquidity. His mission: to seek out profitable entry points, to boldly trade where no degen has traded before.</p>
    <p>The Admiral's trading philosophy combines the mathematical precision of Einstein's upgraded ghost with the instinctual wisdom of a seasoned degen. He believes that when multiple signals converge across timeframes, they create a harmonic resonance in the market fabric – a moment he calls "The Convergence" – where risk is minimized and potential reward maximized.</p>
    <p>His crew of specialized indicators – from the logical RSI Lieutenant to the volatile MACD Commander – work in perfect harmony under his guidance, each contributing their unique perspective to form a complete picture of market conditions.</p>
    <p>"The stars don't lie," Admiral T.O.A. often says, "and neither do properly calibrated indicators when they all point in the same direction."</p>
    <p>When not charting courses through bull and bear territories, the Admiral can be found in the Liquidity Lounge, sipping on his favorite drink – a "Green Candle Mojito" – while sharing tales of legendary trades and near-misses with aspiring crypto navigators.</p>
    <p>Remember: In the vast ocean of market noise, Admiral T.O.A. is your guide to finding the signal.</p>
  </div>
`

// Any further legacy, unused, or UI-only logic can be appended below as needed.

// To reintegrate, move the relevant objects or HTML back to the main codebase and re-link as appropriate.
