/**
 * @file Manages the application's theme (dark/light mode).
 * @module ThemeManager
 */

(function(window, document) {
  'use strict';

  const ThemeManager = {
    /**
     * Initializes the ThemeManager, applying the saved theme or system preference.
     * @param {string} initialTheme - The theme to apply ('dark' or 'light').
     */
    init: function(initialTheme) {
      console.log('[ThemeManager] Initializing with theme:', initialTheme);
      this.applyTheme(initialTheme);
      this.setupSystemThemeListener();
    },

    /**
     * Applies the specified theme to the document body.
     * @param {string} theme - The theme to apply ('dark' or 'light').
     */
    applyTheme: function(theme) {
      document.body.classList.remove('dark-mode', 'light-mode');
      document.body.classList.add(theme + '-mode');
      localStorage.setItem('theme', theme);
      console.log('[ThemeManager] Applied theme:', theme);
    },

    /**
     * Toggles between 'dark' and 'light' themes.
     */
    toggleTheme: function() {
      const currentTheme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
      const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
      this.applyTheme(newTheme);
    },

    /**
     * Sets up a listener for changes in the system's preferred color scheme.
     */
    setupSystemThemeListener: function() {
      if (window.matchMedia) {
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
          const systemTheme = e.matches ? 'dark' : 'light';
          // Only apply system theme if no specific theme is saved
          if (!localStorage.getItem('theme')) {
            this.applyTheme(systemTheme);
          }
        });
      }
    }
  };

  // Expose to the global scope
  window.ThemeManager = ThemeManager;

})(window, document);
