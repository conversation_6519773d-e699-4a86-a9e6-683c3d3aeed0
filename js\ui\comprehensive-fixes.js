/**
 * Comprehensive Fixes for StarCrypt
 * Addresses multiple critical issues:
 * 1. WebSocket "indicator is not defined" errors
 * 2. Timeframe checkbox placement issues
 * 3. ML control panel visibility issues
 * 4. Duplicate code and event handlers
 */

class ComprehensiveFixes {
  constructor() {
    this.init();
  }

  init() {
    console.log('[ComprehensiveFixes] Initializing comprehensive fixes...');
    
    try {
      // Fix WebSocket indicator errors first (highest priority)
      this.fixWebSocketIndicatorErrors();
      
      // Fix timeframe checkbox placement
      this.fixTimeframeCheckboxPlacement();
      
      // Fix ML control panel visibility
      this.fixMLControlPanelVisibility();
      
      // Clean up duplicate handlers
      this.cleanupDuplicateHandlers();
      
      console.log('[ComprehensiveFixes] All comprehensive fixes applied successfully');
    } catch (error) {
      console.error('[ComprehensiveFixes] Error applying comprehensive fixes:', error);
    }
  }

  /**
   * Fix WebSocket "indicator is not defined" errors
   * The issue is in the main WebSocket handler in index.html around line 9697
   */
  fixWebSocketIndicatorErrors() {
    console.log('[ComprehensiveFixes] Fixing WebSocket indicator errors...');
    
    // Override the problematic WebSocket message handler
    if (window.ws && window.ws.onmessage) {
      const originalHandler = window.ws.onmessage;
      
      window.ws.onmessage = function(event) {
        try {
          const msg = JSON.parse(event.data);
          
          // Enhanced validation for indicator messages
          if (msg.type === 'indicators') {
            if (!msg.data || typeof msg.data !== 'object') {
              console.warn('[ComprehensiveFixes] Invalid indicators data structure:', msg);
              return;
            }
            
            // Validate each indicator in the data
            const validatedData = {};
            Object.keys(msg.data).forEach(key => {
              // Check if key is a valid indicator name
              if (key && typeof key === 'string' && key !== 'undefined' && key !== 'null' && key.length > 0) {
                // Additional validation for indicator data
                if (msg.data[key] && typeof msg.data[key] === 'object') {
                  validatedData[key] = msg.data[key];
                } else {
                  console.warn('[ComprehensiveFixes] Invalid indicator data for:', key, msg.data[key]);
                }
              } else {
                console.warn('[ComprehensiveFixes] Invalid indicator key:', key, 'Type:', typeof key);
              }
            });
            
            // Only process if we have valid indicators
            if (Object.keys(validatedData).length > 0) {
              msg.data = validatedData;
              originalHandler.call(this, { data: JSON.stringify(msg) });
            } else {
              console.warn('[ComprehensiveFixes] No valid indicators found in message, skipping');
            }
          } else {
            // For non-indicator messages, use original handler
            originalHandler.call(this, event);
          }
        } catch (error) {
          console.error('[ComprehensiveFixes] WebSocket message processing error:', error.message);
          // Don't call original handler if there's a parsing error
        }
      };
      
      console.log('[ComprehensiveFixes] WebSocket indicator error fixes applied');
    }
  }

  /**
   * Fix timeframe checkbox placement - should be in toggleTimeframesButton menu
   */
  fixTimeframeCheckboxPlacement() {
    console.log('[ComprehensiveFixes] Fixing timeframe checkbox placement...');
    
    // Find misplaced timeframe controls in ticker container
    const tickerContainer = document.querySelector('.ticker-container');
    const misplacedControls = tickerContainer?.querySelector('.timeframe-checkbox-controls');
    
    if (misplacedControls) {
      console.log('[ComprehensiveFixes] Found misplaced timeframe controls, relocating...');
      
      // Remove from ticker container
      misplacedControls.remove();
      
      // Find or create the timeframes menu
      let timeframesMenu = document.getElementById('timeframesMenu');
      if (!timeframesMenu) {
        // Create timeframes menu if it doesn't exist
        timeframesMenu = document.createElement('div');
        timeframesMenu.id = 'timeframesMenu';
        timeframesMenu.className = 'menu-box';
        timeframesMenu.innerHTML = `
          <div class="menu-header">
            <h3>⏱️ Timeframe Selection</h3>
            <button class="close-button" onclick="this.closest('.menu-box').classList.remove('open')">×</button>
          </div>
          <div class="menu-content"></div>
        `;
        document.body.appendChild(timeframesMenu);
      }
      
      // Add controls to the proper menu
      const menuContent = timeframesMenu.querySelector('.menu-content');
      if (menuContent) {
        menuContent.appendChild(misplacedControls);
        console.log('[ComprehensiveFixes] Timeframe controls relocated to proper menu');
      }
      
      // Ensure the toggle button opens the menu
      const toggleButton = document.getElementById('toggleTimeframesButton');
      if (toggleButton) {
        // Remove existing listeners
        const newButton = toggleButton.cloneNode(true);
        toggleButton.parentNode.replaceChild(newButton, toggleButton);
        
        // Add proper event listener
        newButton.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          
          // Close other menus
          document.querySelectorAll('.menu-box.open').forEach(menu => {
            if (menu.id !== 'timeframesMenu') {
              menu.classList.remove('open');
            }
          });
          
          // Toggle timeframes menu
          timeframesMenu.classList.toggle('open');
          console.log('[ComprehensiveFixes] Timeframes menu toggled');
        });
      }
    }
  }

  /**
   * Fix ML control panel visibility issues
   */
  fixMLControlPanelVisibility() {
    console.log('[ComprehensiveFixes] Fixing ML control panel visibility...');
    
    // Find hidden ML panels
    const hiddenPanels = [
      document.querySelector('.ml-advanced-options'),
      document.querySelector('.ml-historical-analysis'),
      document.querySelector('#mlAdvancedOptionsPanel'),
      document.querySelector('#mlHistoricalAnalysisPanel')
    ].filter(Boolean);
    
    hiddenPanels.forEach((panel, index) => {
      if (panel) {
        console.log(`[ComprehensiveFixes] Making ML panel ${index + 1} visible...`);
        
        // Ensure panel is visible and properly positioned
        Object.assign(panel.style, {
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: '10000',
          backgroundColor: 'rgba(26, 26, 46, 0.95)',
          border: '2px solid #4ECDC4',
          borderRadius: '12px',
          padding: '20px',
          maxWidth: '600px',
          maxHeight: '80vh',
          overflow: 'auto',
          display: 'block',
          visibility: 'visible',
          opacity: '1'
        });
        
        // Add close button if not present
        if (!panel.querySelector('.close-button')) {
          const closeButton = document.createElement('button');
          closeButton.className = 'close-button';
          closeButton.innerHTML = '×';
          closeButton.style.cssText = `
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            color: #4ECDC4;
            font-size: 24px;
            cursor: pointer;
            z-index: 10001;
          `;
          closeButton.onclick = () => panel.style.display = 'none';
          panel.appendChild(closeButton);
        }
        
        // Add drag functionality
        this.makeDraggable(panel);
      }
    });
    
    // Create toggle buttons for ML panels in ticker container
    this.createMLToggleButtons();
  }

  /**
   * Create toggle buttons for ML panels
   */
  createMLToggleButtons() {
    const tickerContainer = document.querySelector('.ticker-container');
    if (!tickerContainer) return;
    
    // Check if buttons already exist
    if (tickerContainer.querySelector('.ml-toggle-buttons')) return;
    
    const buttonContainer = document.createElement('div');
    buttonContainer.className = 'ml-toggle-buttons';
    buttonContainer.style.cssText = `
      display: flex;
      gap: 8px;
      margin-left: 10px;
    `;
    
    const buttons = [
      { id: 'mlAdvancedToggle', text: '🤖 ML Options', target: '.ml-advanced-options' },
      { id: 'mlHistoryToggle', text: '📊 ML History', target: '.ml-historical-analysis' }
    ];
    
    buttons.forEach(({ id, text, target }) => {
      const button = document.createElement('button');
      button.id = id;
      button.className = 'ml-toggle-btn';
      button.textContent = text;
      button.style.cssText = `
        background: linear-gradient(135deg, #6c5ce7, #a29bfe);
        border: 1px solid #6c5ce7;
        color: white;
        padding: 6px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 11px;
        font-weight: bold;
        transition: all 0.3s ease;
      `;
      
      button.addEventListener('click', () => {
        const panel = document.querySelector(target);
        if (panel) {
          const isVisible = panel.style.display !== 'none';
          panel.style.display = isVisible ? 'none' : 'block';
          console.log(`[ComprehensiveFixes] Toggled ${target} panel`);
        }
      });
      
      button.addEventListener('mouseenter', () => {
        button.style.transform = 'translateY(-1px)';
        button.style.boxShadow = '0 4px 8px rgba(108, 92, 231, 0.3)';
      });
      
      button.addEventListener('mouseleave', () => {
        button.style.transform = 'translateY(0)';
        button.style.boxShadow = 'none';
      });
      
      buttonContainer.appendChild(button);
    });
    
    // Insert after Admiral button
    const admiralButton = tickerContainer.querySelector('#admiralButton');
    if (admiralButton && admiralButton.parentNode) {
      admiralButton.parentNode.insertBefore(buttonContainer, admiralButton.nextSibling);
    } else {
      tickerContainer.appendChild(buttonContainer);
    }
    
    console.log('[ComprehensiveFixes] ML toggle buttons created');
  }

  /**
   * Make element draggable
   */
  makeDraggable(element) {
    let isDragging = false;
    let currentX;
    let currentY;
    let initialX;
    let initialY;
    let xOffset = 0;
    let yOffset = 0;
    
    element.addEventListener('mousedown', (e) => {
      if (e.target.classList.contains('close-button')) return;
      
      initialX = e.clientX - xOffset;
      initialY = e.clientY - yOffset;
      
      if (e.target === element || e.target.classList.contains('menu-header')) {
        isDragging = true;
        element.style.cursor = 'grabbing';
      }
    });
    
    document.addEventListener('mousemove', (e) => {
      if (isDragging) {
        e.preventDefault();
        currentX = e.clientX - initialX;
        currentY = e.clientY - initialY;
        xOffset = currentX;
        yOffset = currentY;
        
        element.style.transform = `translate(${currentX}px, ${currentY}px)`;
      }
    });
    
    document.addEventListener('mouseup', () => {
      initialX = currentX;
      initialY = currentY;
      isDragging = false;
      element.style.cursor = 'default';
    });
  }

  /**
   * Clean up duplicate handlers and code
   */
  cleanupDuplicateHandlers() {
    console.log('[ComprehensiveFixes] Cleaning up duplicate handlers...');
    
    // Remove duplicate WebSocket handlers
    this.removeDuplicateWebSocketHandlers();
    
    // Remove duplicate event listeners
    this.removeDuplicateEventListeners();
    
    // Clean up duplicate DOM elements
    this.cleanupDuplicateElements();
  }

  removeDuplicateWebSocketHandlers() {
    // Count WebSocket instances
    const wsInstances = [];
    if (window.ws) wsInstances.push('window.ws');
    if (window.websocket) wsInstances.push('window.websocket');
    if (window.socket) wsInstances.push('window.socket');
    
    if (wsInstances.length > 1) {
      console.warn('[ComprehensiveFixes] Multiple WebSocket instances detected:', wsInstances);
      
      // Keep only the main window.ws instance
      if (window.websocket && window.websocket !== window.ws) {
        try {
          window.websocket.close();
          delete window.websocket;
        } catch (e) {
          console.warn('[ComprehensiveFixes] Error closing duplicate websocket:', e);
        }
      }
      
      if (window.socket && window.socket !== window.ws) {
        try {
          window.socket.close();
          delete window.socket;
        } catch (e) {
          console.warn('[ComprehensiveFixes] Error closing duplicate socket:', e);
        }
      }
    }
  }

  removeDuplicateEventListeners() {
    // Find elements with multiple click listeners
    const elementsWithListeners = document.querySelectorAll('[data-has-click-listener="true"]');
    
    elementsWithListeners.forEach(element => {
      // Clone element to remove all event listeners
      const newElement = element.cloneNode(true);
      element.parentNode.replaceChild(newElement, element);
      
      // Re-add single event listener based on element type
      if (newElement.classList.contains('signal-circle')) {
        newElement.addEventListener('click', (e) => {
          console.log('[ComprehensiveFixes] Signal circle clicked:', newElement.dataset);
        });
      }
    });
    
    console.log('[ComprehensiveFixes] Cleaned up duplicate event listeners');
  }

  cleanupDuplicateElements() {
    // Remove duplicate signal containers
    const signalContainers = document.querySelectorAll('.signal-circle');
    const seen = new Set();
    
    signalContainers.forEach(container => {
      const key = `${container.dataset.ind}-${container.dataset.tf}`;
      if (seen.has(key)) {
        console.log('[ComprehensiveFixes] Removing duplicate signal container:', key);
        container.remove();
      } else {
        seen.add(key);
      }
    });
    
    // Remove duplicate menu boxes
    const menuBoxes = document.querySelectorAll('.menu-box');
    const menuIds = new Set();
    
    menuBoxes.forEach(menu => {
      if (menu.id && menuIds.has(menu.id)) {
        console.log('[ComprehensiveFixes] Removing duplicate menu:', menu.id);
        menu.remove();
      } else if (menu.id) {
        menuIds.add(menu.id);
      }
    });
  }
}

// Initialize comprehensive fixes
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    window.comprehensiveFixes = new ComprehensiveFixes();
  }, 3000); // Wait for other systems to initialize
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ComprehensiveFixes;
}
