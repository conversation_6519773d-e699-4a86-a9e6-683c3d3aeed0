/**
 * Enhanced Timeframe Selector for StarCrypt
 * Comprehensive timeframe management with 30m and 3d support and live adding/removing
 */

class EnhancedTimeframeSelector {
  constructor() {
    this.defaultTimeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '3d', '1w'];
    this.availableTimeframes = [
      '1s', '5s', '15s', '30s',
      '1m', '2m', '3m', '4m', '5m', '10m', '15m', '30m',
      '1h', '2h', '3h', '4h', '6h', '8h', '12h',
      '1d', '2d', '3d', '5d',
      '1w', '2w', '1M', '3M', '6M', '1Y'
    ];
    
    this.activeTimeframes = [...this.defaultTimeframes];
    this.currentTimeframe = '1h';
    this.customTimeframes = [];
    
    // Timeframe conversion to seconds
    this.timeframeSeconds = {
      '1s': 1, '5s': 5, '15s': 15, '30s': 30,
      '1m': 60, '2m': 120, '3m': 180, '4m': 240, '5m': 300, '10m': 600, '15m': 900, '30m': 1800,
      '1h': 3600, '2h': 7200, '3h': 10800, '4h': 14400, '6h': 21600, '8h': 28800, '12h': 43200,
      '1d': 86400, '2d': 172800, '3d': 259200, '5d': 432000,
      '1w': 604800, '2w': 1209600, '1M': 2592000, '3M': 7776000, '6M': 15552000, '1Y': 31536000
    };
    
    this.timeframeGroups = {
      'Seconds': ['1s', '5s', '15s', '30s'],
      'Minutes': ['1m', '2m', '3m', '4m', '5m', '10m', '15m', '30m'],
      'Hours': ['1h', '2h', '3h', '4h', '6h', '8h', '12h'],
      'Days': ['1d', '2d', '3d', '5d'],
      'Weeks/Months': ['1w', '2w', '1M', '3M', '6M', '1Y']
    };
    
    this.isInitialized = false;
    this.init();
  }

  init() {
    console.log('[EnhancedTimeframeSelector] Initializing enhanced timeframe selector...');
    
    // Update global timeframes
    this.updateGlobalTimeframes();
    
    // Setup event listeners
    this.setupEventListeners();
    
    // Initialize UI
    this.initializeUI();
    
    this.isInitialized = true;
    console.log('[EnhancedTimeframeSelector] Enhanced timeframe selector initialized successfully');
  }

  updateGlobalTimeframes() {
    // Update global TIMEFRAMES array
    window.TIMEFRAMES = [...this.activeTimeframes];
    
    // Update TIMEFRAME_SECONDS
    window.TIMEFRAME_SECONDS = { ...this.timeframeSeconds };
    
    // Dispatch event for other components
    document.dispatchEvent(new CustomEvent('timeframesUpdated', {
      detail: {
        activeTimeframes: this.activeTimeframes,
        availableTimeframes: this.availableTimeframes
      }
    }));
  }

  setupEventListeners() {
    // Listen for timeframe menu requests
    document.addEventListener('renderTimeframesMenu', () => {
      this.renderEnhancedTimeframesMenu();
    });
    
    // Listen for apply timeframes button
    document.addEventListener('click', (e) => {
      if (e.target.id === 'applyTimeframesButton') {
        this.applyTimeframeSelection();
      }
      
      if (e.target.id === 'addCustomTimeframeButton') {
        this.showAddCustomTimeframeDialog();
      }
      
      if (e.target.classList.contains('remove-timeframe-btn')) {
        const timeframe = e.target.dataset.timeframe;
        this.removeTimeframe(timeframe);
      }
      
      if (e.target.classList.contains('timeframe-preset-btn')) {
        const preset = e.target.dataset.preset;
        this.applyTimeframePreset(preset);
      }
    });
    
    // Listen for custom timeframe form submission
    document.addEventListener('submit', (e) => {
      if (e.target.id === 'customTimeframeForm') {
        e.preventDefault();
        this.addCustomTimeframe();
      }
    });
  }

  initializeUI() {
    // Override the original renderTimeframesMenu function
    if (typeof window.renderTimeframesMenu === 'function') {
      window.originalRenderTimeframesMenu = window.renderTimeframesMenu;
    }
    
    window.renderTimeframesMenu = () => {
      this.renderEnhancedTimeframesMenu();
    };
  }

  renderEnhancedTimeframesMenu() {
    console.log('[EnhancedTimeframeSelector] Rendering enhanced timeframes menu...');
    
    const timeframesOptions = document.getElementById('timeframesOptions');
    if (!timeframesOptions) {
      console.error('[EnhancedTimeframeSelector] Timeframes options container not found');
      return;
    }

    timeframesOptions.innerHTML = `
      <div class="enhanced-timeframes-container">
        <!-- Timeframe Presets -->
        <div class="timeframe-presets">
          <h4>Quick Presets</h4>
          <div class="preset-buttons">
            <button class="timeframe-preset-btn" data-preset="scalping">Scalping</button>
            <button class="timeframe-preset-btn" data-preset="daytrading">Day Trading</button>
            <button class="timeframe-preset-btn" data-preset="swingtrading">Swing Trading</button>
            <button class="timeframe-preset-btn" data-preset="longterm">Long Term</button>
            <button class="timeframe-preset-btn" data-preset="all">All Timeframes</button>
          </div>
        </div>

        <!-- Active Timeframes -->
        <div class="active-timeframes-section">
          <h4>Active Timeframes</h4>
          <div class="active-timeframes-list">
            ${this.renderActiveTimeframes()}
          </div>
        </div>

        <!-- Available Timeframes by Group -->
        <div class="available-timeframes-section">
          <h4>Available Timeframes</h4>
          ${this.renderTimeframeGroups()}
        </div>

        <!-- Custom Timeframe -->
        <div class="custom-timeframe-section">
          <h4>Custom Timeframe</h4>
          <button id="addCustomTimeframeButton" class="add-custom-btn">+ Add Custom Timeframe</button>
          <div id="customTimeframeDialog" class="custom-timeframe-dialog" style="display: none;">
            <form id="customTimeframeForm">
              <div class="form-group">
                <label for="customTimeframeValue">Value:</label>
                <input type="number" id="customTimeframeValue" min="1" max="999" required>
              </div>
              <div class="form-group">
                <label for="customTimeframeUnit">Unit:</label>
                <select id="customTimeframeUnit" required>
                  <option value="s">Seconds</option>
                  <option value="m">Minutes</option>
                  <option value="h">Hours</option>
                  <option value="d">Days</option>
                  <option value="w">Weeks</option>
                </select>
              </div>
              <div class="form-actions">
                <button type="submit">Add</button>
                <button type="button" onclick="this.closest('.custom-timeframe-dialog').style.display='none'">Cancel</button>
              </div>
            </form>
          </div>
        </div>

        <!-- Live Update Options -->
        <div class="live-update-section">
          <h4>Live Update Options</h4>
          <div class="live-update-controls">
            <label class="checkbox-label">
              <input type="checkbox" id="autoUpdateTimeframes" checked>
              Auto-update signal matrix when timeframes change
            </label>
            <label class="checkbox-label">
              <input type="checkbox" id="preserveTimeframeOrder" checked>
              Preserve timeframe order in matrix
            </label>
          </div>
        </div>
      </div>
    `;

    this.setupEnhancedEventListeners();
  }

  renderActiveTimeframes() {
    return this.activeTimeframes.map(timeframe => `
      <div class="active-timeframe-item">
        <span class="timeframe-label">${timeframe}</span>
        <span class="timeframe-duration">(${this.getTimeframeDuration(timeframe)})</span>
        ${this.activeTimeframes.length > 1 ? `
          <button class="remove-timeframe-btn" data-timeframe="${timeframe}" title="Remove timeframe">×</button>
        ` : ''}
      </div>
    `).join('');
  }

  renderTimeframeGroups() {
    return Object.entries(this.timeframeGroups).map(([groupName, timeframes]) => `
      <div class="timeframe-group">
        <h5>${groupName}</h5>
        <div class="timeframe-checkboxes">
          ${timeframes.map(timeframe => `
            <label class="timeframe-checkbox-label">
              <input type="checkbox" 
                     data-timeframe="${timeframe}" 
                     ${this.activeTimeframes.includes(timeframe) ? 'checked' : ''}
                     ${this.activeTimeframes.includes(timeframe) ? 'disabled' : ''}>
              <span class="timeframe-text">${timeframe}</span>
              <span class="timeframe-desc">(${this.getTimeframeDuration(timeframe)})</span>
            </label>
          `).join('')}
        </div>
      </div>
    `).join('');
  }

  getTimeframeDuration(timeframe) {
    const seconds = this.timeframeSeconds[timeframe];
    if (!seconds) return 'Unknown';
    
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m`;
    if (seconds < 86400) return `${Math.floor(seconds / 3600)}h`;
    if (seconds < 604800) return `${Math.floor(seconds / 86400)}d`;
    if (seconds < 2592000) return `${Math.floor(seconds / 604800)}w`;
    return `${Math.floor(seconds / 2592000)}M`;
  }

  setupEnhancedEventListeners() {
    // Handle timeframe checkbox changes
    document.querySelectorAll('input[data-timeframe]').forEach(checkbox => {
      checkbox.addEventListener('change', (e) => {
        const timeframe = e.target.dataset.timeframe;
        if (e.target.checked) {
          this.addTimeframe(timeframe);
        }
      });
    });
  }

  addTimeframe(timeframe) {
    if (!this.activeTimeframes.includes(timeframe)) {
      this.activeTimeframes.push(timeframe);
      this.sortTimeframes();
      this.updateActiveTimeframesDisplay();
      
      if (document.getElementById('autoUpdateTimeframes')?.checked) {
        this.updateGlobalTimeframes();
        this.updateSignalMatrix();
      }
      
      console.log(`[EnhancedTimeframeSelector] Added timeframe: ${timeframe}`);
    }
  }

  removeTimeframe(timeframe) {
    if (this.activeTimeframes.length > 1) {
      this.activeTimeframes = this.activeTimeframes.filter(tf => tf !== timeframe);
      this.updateActiveTimeframesDisplay();
      
      if (document.getElementById('autoUpdateTimeframes')?.checked) {
        this.updateGlobalTimeframes();
        this.updateSignalMatrix();
      }
      
      console.log(`[EnhancedTimeframeSelector] Removed timeframe: ${timeframe}`);
    }
  }

  sortTimeframes() {
    if (document.getElementById('preserveTimeframeOrder')?.checked) {
      this.activeTimeframes.sort((a, b) => {
        return this.timeframeSeconds[a] - this.timeframeSeconds[b];
      });
    }
  }

  updateActiveTimeframesDisplay() {
    const activeTimeframesList = document.querySelector('.active-timeframes-list');
    if (activeTimeframesList) {
      activeTimeframesList.innerHTML = this.renderActiveTimeframes();
    }
    
    // Update checkboxes
    document.querySelectorAll('input[data-timeframe]').forEach(checkbox => {
      const timeframe = checkbox.dataset.timeframe;
      checkbox.checked = this.activeTimeframes.includes(timeframe);
      checkbox.disabled = this.activeTimeframes.includes(timeframe);
    });
  }

  applyTimeframePreset(preset) {
    console.log(`[EnhancedTimeframeSelector] Applying preset: ${preset}`);
    
    const presets = {
      scalping: ['1m', '5m', '15m', '30m'],
      daytrading: ['5m', '15m', '30m', '1h', '4h'],
      swingtrading: ['1h', '4h', '1d', '3d'],
      longterm: ['1d', '3d', '1w', '1M'],
      all: [...this.defaultTimeframes]
    };
    
    if (presets[preset]) {
      this.activeTimeframes = [...presets[preset]];
      this.updateActiveTimeframesDisplay();
      
      if (document.getElementById('autoUpdateTimeframes')?.checked) {
        this.updateGlobalTimeframes();
        this.updateSignalMatrix();
      }
    }
  }

  showAddCustomTimeframeDialog() {
    const dialog = document.getElementById('customTimeframeDialog');
    if (dialog) {
      dialog.style.display = 'block';
    }
  }

  addCustomTimeframe() {
    const value = parseInt(document.getElementById('customTimeframeValue').value);
    const unit = document.getElementById('customTimeframeUnit').value;
    
    if (value && unit) {
      const customTimeframe = `${value}${unit}`;
      const seconds = this.calculateCustomTimeframeSeconds(value, unit);
      
      // Add to available timeframes and seconds mapping
      if (!this.availableTimeframes.includes(customTimeframe)) {
        this.availableTimeframes.push(customTimeframe);
        this.timeframeSeconds[customTimeframe] = seconds;
        this.customTimeframes.push(customTimeframe);
        
        // Add to appropriate group
        const groupName = this.getTimeframeGroupName(unit);
        if (this.timeframeGroups[groupName]) {
          this.timeframeGroups[groupName].push(customTimeframe);
          this.timeframeGroups[groupName].sort((a, b) => 
            this.timeframeSeconds[a] - this.timeframeSeconds[b]
          );
        }
        
        // Re-render menu
        this.renderEnhancedTimeframesMenu();
        
        console.log(`[EnhancedTimeframeSelector] Added custom timeframe: ${customTimeframe}`);
      }
      
      // Hide dialog
      document.getElementById('customTimeframeDialog').style.display = 'none';
      
      // Reset form
      document.getElementById('customTimeframeForm').reset();
    }
  }

  calculateCustomTimeframeSeconds(value, unit) {
    const multipliers = {
      's': 1,
      'm': 60,
      'h': 3600,
      'd': 86400,
      'w': 604800
    };
    
    return value * (multipliers[unit] || 1);
  }

  getTimeframeGroupName(unit) {
    const groupMap = {
      's': 'Seconds',
      'm': 'Minutes',
      'h': 'Hours',
      'd': 'Days',
      'w': 'Weeks/Months'
    };
    
    return groupMap[unit] || 'Custom';
  }

  applyTimeframeSelection() {
    console.log('[EnhancedTimeframeSelector] Applying timeframe selection...');
    
    // Update global timeframes
    this.updateGlobalTimeframes();
    
    // Update signal matrix
    this.updateSignalMatrix();
    
    // Close menu
    const timeframesMenu = document.getElementById('timeframesMenu');
    if (timeframesMenu) {
      timeframesMenu.classList.remove('active');
    }
    
    // Show confirmation
    this.showToast(`Timeframes updated: ${this.activeTimeframes.join(', ')}`, 'success');
    
    console.log('[EnhancedTimeframeSelector] Timeframe selection applied successfully');
  }

  updateSignalMatrix() {
    // Trigger signal matrix update
    if (typeof window.renderIndicatorTables === 'function') {
      window.renderIndicatorTables();
    }
    
    if (typeof window.updateAllSignalLights === 'function') {
      window.updateAllSignalLights();
    }
    
    // Dispatch event for other components
    document.dispatchEvent(new CustomEvent('signalMatrixUpdated', {
      detail: { activeTimeframes: this.activeTimeframes }
    }));
  }

  showToast(message, type = 'info') {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    toast.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: ${type === 'success' ? '#4CAF50' : '#2196F3'};
      color: white;
      padding: 12px 20px;
      border-radius: 4px;
      z-index: 10000;
      animation: slideIn 0.3s ease-out;
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
      toast.style.animation = 'slideOut 0.3s ease-in';
      setTimeout(() => toast.remove(), 300);
    }, 3000);
  }

  getActiveTimeframes() {
    return [...this.activeTimeframes];
  }

  getCurrentTimeframe() {
    return this.currentTimeframe;
  }

  setCurrentTimeframe(timeframe) {
    if (this.activeTimeframes.includes(timeframe)) {
      this.currentTimeframe = timeframe;
      console.log(`[EnhancedTimeframeSelector] Current timeframe set to: ${timeframe}`);
      return true;
    }
    return false;
  }

  destroy() {
    console.log('[EnhancedTimeframeSelector] Destroying enhanced timeframe selector...');
    
    // Restore original function if it existed
    if (window.originalRenderTimeframesMenu) {
      window.renderTimeframesMenu = window.originalRenderTimeframesMenu;
    }
    
    this.isInitialized = false;
  }
}

// Initialize enhanced timeframe selector
window.enhancedTimeframeSelector = new EnhancedTimeframeSelector();

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EnhancedTimeframeSelector;
}
