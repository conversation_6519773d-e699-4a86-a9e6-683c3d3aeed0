// Import PriceChart component
import PriceChart from './components/PriceChart.js';

// Main Application Initialization
class StarCryptApp {
  constructor() {
    this.initialized = false;
    this.modules = {
      themeManager: window.ThemeManager,
      websocket: null
    };
    this.priceChart = null;
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.init());
    } else {
      setTimeout(() => this.init(), 0);
    }
  }
  
  // Initialize the application
  async init() {
    if (this.initialized) return;
    
    try {
      console.log('Initializing StarCrypt...');
      
      // Initialize theme manager
      if (this.modules.themeManager && typeof this.modules.themeManager.init === 'function') {
        this.modules.themeManager.init();
      }
      
      // Initialize UI components
      this.initUI();
      
      // Initialize WebSocket connection
      this.initWebSocket();
      
      // Initialize chart
      this.initChart();
      
      // Hide loading overlay
      this.hideLoadingOverlay();
      
      this.initialized = true;
      console.log('StarCrypt initialized successfully');
      
    } catch (error) {
      console.error('Initialization error:', error);
      this.showError('Failed to initialize application. Please check the console for details.');
    }
  }
  
  // Initialize the price chart
  initChart() {
    try {
      console.log('=== Initializing Price Chart ===');
      
      // Clean up any existing chart first
      this.cleanupChart();
      
      // Initialize the PriceChart component
      this.priceChart = new PriceChart('chart-container');
      console.log('PriceChart initialized successfully');
      
    } catch (error) {
      console.error('Error initializing chart:', error);
      this.showError('Failed to initialize chart. Please refresh the page.');
    }
  }
  
  // Clean up chart resources
  cleanupChart() {
    if (this.priceChart) {
      try {
        this.priceChart.destroy();
        this.priceChart = null;
      } catch (e) {
        console.warn('Error cleaning up PriceChart:', e);
      }
    }
    
    // Clean up any canvas elements in the chart container
    const chartContainer = document.getElementById('chart-container');
    if (chartContainer) {
      chartContainer.innerHTML = '';
    }
  }
  
  // Initialize WebSocket connection
  initWebSocket() {
    console.log('Initializing WebSocket...');
    // TODO: Implement WebSocket connection
  }
  
  // Initialize UI components
  initUI() {
    console.log('Initializing UI components...');
    // TODO: Initialize other UI components
  }
  
  // Show error message to user
  showError(message) {
    console.error('Error:', message);
    // TODO: Implement error display in UI
  }
  
  // Hide loading overlay
  hideLoadingOverlay() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
      overlay.style.display = 'none';
    }
  }
}

// Initialize the application when the script loads
const app = new StarCryptApp();

// Make app globally available for debugging
window.StarCryptApp = app;
