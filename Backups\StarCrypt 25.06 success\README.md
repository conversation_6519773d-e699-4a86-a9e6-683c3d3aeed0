# StarCrypt Enterprise

StarCrypt Enterprise is a sophisticated cryptocurrency trading interface designed for real-time market analysis and signal processing. It provides a comprehensive suite of tools, including a customizable signal matrix, multiple trading strategies, and an interactive TradingView chart, to empower traders with actionable insights.

## Key Features

- **The Oracle Matrix**: A dynamic signal matrix that visualizes indicator states across multiple timeframes.
- **Strategy-Driven Analysis**: Switch between multiple trading strategies (e.g., convergence, momentum, AI-driven) to tailor the UI and signal processing to your needs.
- **Real-Time Data**: Integrated WebSocket connection for live price and indicator updates.
- **Interactive Charting**: Embedded TradingView widget for advanced charting and technical analysis.
- **Customizable UI**: Features a theme toggle for light/dark modes and a responsive layout.

## Getting Started

To run the StarCrypt application locally, you need a local web server. A simple one can be run using `npx`.

1.  **Navigate to the project directory:**
    ```bash
    cd path/to/StarCrypt
    ```

2.  **Start the web server:**
    ```bash
    npx http-server -p 3000
    ```

3.  **Open the application:**
    Open your web browser and navigate to `http://localhost:3000`.

## Recent Updates (June 2025)

This project recently underwent a significant refactoring and cleanup effort to improve stability, maintainability, and user experience.

### Architectural Improvements

- **Centralized Initialization**: All UI components and modules are now initialized through a single, consolidated `DOMContentLoaded` event handler, eliminating race conditions and redundant initializations.
- **Disabled Legacy Code**: Obsolete and conflicting "fixer" scripts (`signal-fixer.js`, `menu-fix.js`, etc.) have been disabled to prevent them from overriding core functionality.
- **Global State Management**: Key objects like `TRADING_STRATEGIES` have been moved to `global-variables.js` to serve as a single source of truth, removing duplicates from other modules.
- **Dynamic Container Creation**: Fallback logic has been implemented in `strategy-selector.js` to dynamically create essential UI containers if they are missing from the DOM, preventing application crashes.

### UI Cleanup & Enhancements

- **Redundant Elements Removed**: Legacy UI elements, including a duplicate loading progress bar, a floating `apiStats` box, and an extra `cosmic-indicators` container, have been removed from the HTML and JavaScript.
- **Centralized Menu Handling**: All menu buttons within the `.ticker-controls` container are now wired through a single, robust `menu-handler.js` module.
- **Header Centering**: The main header text is now correctly centered for a cleaner look.

### Bug Fixes

- **WebSocket Race Condition**: Resolved a critical race condition where WebSocket messages could arrive before the signal matrix UI was fully rendered. The WebSocket handler now waits for an `isMatrixReady` flag before processing indicator updates.
- **Module Loading Errors**: Fixed numerous 404 and MIME type errors by adding missing JavaScript modules (`theme-manager.js`, `theme-toggle.js`).
- **Syntax & Reference Errors**: Corrected various syntax errors, incorrect variable references, and broken function calls across several files, including `index.html`, `update-signal-lights.js`, and `signal-matrix.js`.
