// indicator-fix.js - Extremely simple fix for indicator row display issues
// This script overrides all other indicator row creation and handling

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('[IndicatorFix] Installing simple indicator row fix...')

  // Wait to ensure all other scripts have loaded
  setTimeout(() => {
    // Override all other functions
    overrideIndicatorFunctions()

    // Run initial rebuild
    rebuildIndicatorRows()

    // Add observer to detect DOM changes
    setupObserver()
  }, 1000)
})

// Override all functions that might compete with our implementation
function overrideIndicatorFunctions() {
  // Replace all createIndicatorRow functions
  window.createIndicatorRow = createProperIndicatorRow

  // Override any other row creation functions
  if (typeof window.createConsistentIndicatorRow !== 'undefined') {
    window.createConsistentIndicatorRow = createProperIndicatorRow
  }
  if (typeof window.getOrCreateIndicatorRow !== 'undefined') {
    window.getOrCreateIndicatorRow = function (ind) {
      return createProperIndicatorRow(ind)
    }
  }

  // Completely override the updateAllSignalLights function to prevent recursion
  let isUpdating = false
  const originalUpdateFunction = window.updateAllSignalLights

  window.updateAllSignalLights = function () {
    if (isUpdating) {
      console.log('[IndicatorFix] Prevented recursive updateAllSignalLights call')
      return
    }

    try {
      isUpdating = true
      console.log('[IndicatorFix] Updating signal lights...')

      // Get all indicators from strategies
      const indicators = getAllIndicators()

      // Update each indicator for each timeframe
      indicators.forEach(indicator => {
        if (window.TIMEFRAMES) {
          window.TIMEFRAMES.forEach(timeframe => {
            updateSignalLight(indicator, timeframe)
          })
        }
      })
    } catch (err) {
      console.error('[IndicatorFix] Error updating signal lights:', err)
    } finally {
      isUpdating = false
    }
  }
}

// Rebuild all indicator rows from scratch
function rebuildIndicatorRows() {
  console.log('[IndicatorFix] Rebuilding all indicator rows...')

  // Find all tables
  const tables = document.querySelectorAll('.momentum-table tbody, .oracle-matrix tbody')

  if (!tables || tables.length === 0) {
    console.error('[IndicatorFix] No indicator tables found')
    return
  }

  // Get all indicator names
  const indicators = getAllIndicators()

  // Clear and rebuild each table
  tables.forEach(table => {
    // Remove existing rows
    const existingRows = table.querySelectorAll('tr[data-indicator]')
    existingRows.forEach(row => row.parentNode.removeChild(row))

    // Create new rows
    indicators.forEach(indicator => {
      const row = createProperIndicatorRow(indicator)
      table.appendChild(row)
    })
  })

  // Update signal lights
  setTimeout(() => {
    if (typeof window.updateAllSignalLights === 'function') {
      window.updateAllSignalLights()
    }
  }, 500)
}

// Create a proper indicator row with consistent attributes
function createProperIndicatorRow(indicator) {
  // Capitalize indicator name for display
  const displayName = getDisplayName(indicator)

  // Create row
  const row = document.createElement('tr')
  row.className = 'indicator-row signal-row'
  row.setAttribute('data-indicator', indicator)
  row.setAttribute('data-ind', indicator)
  row.id = `indicator-row-${indicator}`

  // Create name cell
  const nameCell = document.createElement('td')
  nameCell.className = 'signal-name'
  nameCell.textContent = displayName
  row.appendChild(nameCell)

  // Add mini chart cell if supported
  const chartCell = document.createElement('td')
  chartCell.className = 'mini-chart-cell'
  const chartContainer = document.createElement('div')
  chartContainer.id = `${indicator}-chart-container`
  chartContainer.className = 'mini-chart-container'
  chartCell.appendChild(chartContainer)
  row.appendChild(chartCell)

  // Create signal cells for each timeframe
  if (window.TIMEFRAMES) {
    window.TIMEFRAMES.forEach(timeframe => {
      const signalCell = document.createElement('td')
      signalCell.className = 'signal-light-cell'

      // Create signal circle with all needed attributes
      const signalCircle = document.createElement('div')
      signalCircle.className = 'signal-circle grey-light'
      signalCircle.id = `${indicator}-${timeframe}-signal`
      signalCircle.setAttribute('data-ind', indicator)
      signalCircle.setAttribute('data-tf', timeframe)
      signalCircle.setAttribute('data-indicator', indicator)
      signalCircle.setAttribute('data-timeframe', timeframe)
      signalCircle.textContent = timeframe

      signalCell.appendChild(signalCircle)
      row.appendChild(signalCell)
    })
  }

  return row
}

// Update a single signal light
function updateSignalLight(indicator, timeframe) {
  // Find the signal circle
  const circleId = `${indicator}-${timeframe}-signal`
  const circle = document.getElementById(circleId)

  if (!circle) {
    return
  }

  // Reset class
  circle.className = 'signal-circle'

  // Check if we have data for this indicator/timeframe
  if (window.indicatorsData &&
      window.indicatorsData[timeframe] &&
      window.indicatorsData[timeframe][indicator]) {
    const data = window.indicatorsData[timeframe][indicator]
    const color = data.color || 'grey'

    // Apply appropriate class
    if (color === '#00FF00' || color === 'green') {
      circle.classList.add('green-light')
    } else if (color === '#0000FF' || color === 'blue') {
      circle.classList.add('blue-light')
    } else if (color === '#FF0000' || color === 'red') {
      circle.classList.add('red-light')
    } else if (color === '#FFA500' || color === 'orange') {
      circle.classList.add('orange-light')
    } else {
      circle.classList.add('grey-light')
    }

    // Set tooltip
    if (data.value) {
      circle.setAttribute('data-tooltip', `${getDisplayName(indicator)} (${timeframe}): ${data.value}`)
    }
  } else {
    // No data, use neutral state
    circle.classList.add('grey-light')
    circle.setAttribute('data-tooltip', `${getDisplayName(indicator)} (${timeframe}): No data`)
  }
}

// Get all indicators from strategies
function getAllIndicators() {
  const indicators = new Set()

  // Add from TRADING_STRATEGIES
  if (window.TRADING_STRATEGIES) {
    Object.values(window.TRADING_STRATEGIES).forEach(strategy => {
      if (strategy && strategy.indicators) {
        strategy.indicators.forEach(ind => indicators.add(ind))
      }
    })
  }

  // Add from INDICATORS object if available
  if (window.INDICATORS) {
    Object.values(window.INDICATORS).forEach(group => {
      if (Array.isArray(group)) {
        group.forEach(ind => indicators.add(ind))
      }
    })
  }

  // Default indicators if none found
  if (indicators.size === 0) {
    ['rsi', 'macd', 'bollingerBands', 'stochRsi', 'williamsR', 'mfi'].forEach(ind => indicators.add(ind))
  }

  return Array.from(indicators)
}

// Get display name for indicator
function getDisplayName(indicator) {
  const displayNames = {
    rsi: 'RSI',
    stochRsi: 'STOCH RSI',
    williamsR: 'WILLIAMS %R',
    ultimateOscillator: 'ULT OSC',
    mfi: 'MFI',
    macd: 'MACD',
    bollingerBands: 'BOLL BANDS',
    adx: 'ADX',
    atr: 'ATR',
    vwap: 'VWAP',
    fractal: 'FRACTAL',
    volume: 'VOLUME',
    ml: 'ML',
    sentiment: 'SENTIMENT',
    entropy: 'ENTROPY',
    correlation: 'CORRELATION',
    time_anomaly: 'TIME ANOMALY',
  }

  return displayNames[indicator] || indicator.toUpperCase()
}

// Set up observer to watch for DOM changes
function setupObserver() {
  const observer = new MutationObserver(mutations => {
    for (const mutation of mutations) {
      if (mutation.type === 'childList' &&
          (mutation.target.classList.contains('oracle-matrix') ||
           mutation.target.classList.contains('momentum-table'))) {
        console.log('[IndicatorFix] Detected table changes, rebuilding...')
        setTimeout(rebuildIndicatorRows, 100)
        break
      }
    }
  })

  observer.observe(document.body, {
    childList: true,
    subtree: true,
  })

  // Also set up periodic check
  setInterval(() => {
    const tables = document.querySelectorAll('.momentum-table tbody, .oracle-matrix tbody')
    if (tables.length > 0) {
      const hasRows = document.querySelectorAll('.indicator-row').length > 0
      if (!hasRows) {
        console.log('[IndicatorFix] Found tables without indicator rows, rebuilding...')
        rebuildIndicatorRows()
      }
    }
  }, 5000)
}

console.log('[IndicatorFix] Script loaded successfully')
