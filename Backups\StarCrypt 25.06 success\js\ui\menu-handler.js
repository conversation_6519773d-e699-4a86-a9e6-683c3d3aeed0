/**
 * StarCrypt Menu Handler
 * 
 * Centralized menu management for all UI menus
 */

class MenuHandler {
  constructor() {
    this.activeMenu = null;
    this.menus = ['strategyMenu', 'indicatorMenu', 'thresholdsMenu', 'logicMenu'];
    this.initialize();
  }

  initialize() {
    try {
      console.log('[<PERSON>u<PERSON><PERSON><PERSON>] Starting initialization...');
      
      // Ensure menu containers exist first
      this.ensureMenuContainersExist();
      
      // Setup event handlers
      this.setupMenuButtons();
      this.setupClickOutsideHandler();
      
      // Initialize menus that need it
      this.initializeMenus();
      
      console.log('[<PERSON>u<PERSON>and<PERSON>] Initialized menu handler');
    } catch (error) {
      console.error('[<PERSON>u<PERSON><PERSON><PERSON>] Error during initialization:', error);
    }
  }
  
  initializeMenus() {
    // Initialize any menus that need special setup
    if (typeof window.initializeThresholdsMenu === 'function') {
      window.initializeThresholdsMenu();
    }
    
    if (typeof window.initializeLogicMenu === 'function') {
      window.initializeLogicMenu();
    }
  }

  ensureMenuContainersExist() {
    console.log('[MenuHandler] Ensuring menu containers exist...');
    
    // Ensure ticker container exists
    let tickerContainer = document.querySelector('.ticker-container');
    if (!tickerContainer) {
      console.log('[MenuHandler] Creating ticker container');
      tickerContainer = document.createElement('div');
      tickerContainer.className = 'ticker-container';
      tickerContainer.style.position = 'relative';
      document.body.prepend(tickerContainer);
    }
    
    // Create menu containers
    this.menus.forEach(menuId => {
      let menu = document.getElementById(menuId);
      if (!menu) {
        console.log(`[MenuHandler] Creating menu container: ${menuId}`);
        menu = document.createElement('div');
        menu.id = menuId;
        menu.className = `menu-container ${menuId.toLowerCase().replace('menu', '-menu')}`;
        menu.style.display = 'none';
        menu.style.position = 'absolute';
        menu.style.top = '100%';
        menu.style.left = '0';
        menu.style.zIndex = '1000';
        menu.style.background = '#1a1a2e';
        menu.style.border = '1px solid #2a2a4a';
        menu.style.borderRadius = '4px';
        menu.style.padding = '10px';
        menu.style.minWidth = '250px';
        menu.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
        
        // Create header with close button
        const header = document.createElement('div');
        header.style.display = 'flex';
        header.style.justifyContent = 'space-between';
        header.style.alignItems = 'center';
        header.style.marginBottom = '10px';
        header.style.paddingBottom = '5px';
        header.style.borderBottom = '1px solid #2a2a4a';
        
        const title = document.createElement('h3');
        title.textContent = menuId.replace(/([A-Z])/g, ' $1').replace('Menu', '').trim();
        title.style.margin = '0';
        title.style.fontSize = '14px';
        title.style.color = '#fff';
        
        const closeBtn = document.createElement('button');
        closeBtn.className = 'close-menu';
        closeBtn.innerHTML = '&times;';
        closeBtn.style.background = 'transparent';
        closeBtn.style.border = 'none';
        closeBtn.style.color = '#fff';
        closeBtn.style.cursor = 'pointer';
        closeBtn.style.fontSize = '18px';
        closeBtn.style.lineHeight = '1';
        closeBtn.style.padding = '0 5px';
        closeBtn.onclick = (e) => {
          e.stopPropagation();
          this.closeAllMenus();
        };
        
        header.appendChild(title);
        header.appendChild(closeBtn);
        
        // Create content container
        const content = document.createElement('div');
        content.className = 'menu-content';
        content.id = `${menuId}Content`;
        content.style.maxHeight = '70vh';
        content.style.overflowY = 'auto';
        
        menu.appendChild(header);
        menu.appendChild(content);
        
        // Append to ticker container
        tickerContainer.appendChild(menu);
        
        console.log(`[MenuHandler] Created and appended ${menuId} container`);
      }
    });
  }

  setupMenuButtons() {
    console.log('[MenuHandler] Setting up menu buttons...');
    
    // Remove any existing click handlers
    if (this.handleMenuClick) {
      document.removeEventListener('click', this.handleMenuClick);
    }
    
    // Create a new click handler
    this.handleMenuClick = (e) => {
      // Find the closest menu button that was clicked
      const button = e.target.closest('.menu-button');
      if (!button) return;
      
      e.preventDefault();
      e.stopPropagation();
      
      // Map button IDs to menu IDs
      const buttonToMenuMap = {
        'strategyButton': 'strategyMenu',
        'toggleMenuButton': 'indicatorMenu',
        'toggleThresholdsButton': 'thresholdsMenu',
        'toggleLogicButton': 'logicMenu'
      };
      
      const menuId = buttonToMenuMap[button.id];
      if (menuId) {
        console.log(`[MenuHandler] Toggling menu: ${menuId} from button: ${button.id}`);
        this.toggleMenu(menuId, button);
      }
    };
    
    // Add the click handler to the document
    document.addEventListener('click', this.handleMenuClick);
    
    console.log('[MenuHandler] Menu buttons setup complete');
  }

  toggleMenu(menuId) {
    try {
      console.log(`[MenuHandler] Toggling menu: ${menuId}`);
      
      // Ensure menu container exists
      this.ensureMenuContainersExist();
      
      const menu = document.getElementById(menuId);
      if (!menu) {
        console.error(`[MenuHandler] Menu not found: ${menuId}`);
        return;
      }
      
      // If clicking the currently active menu button, close the menu
      if (this.activeMenu === menuId) {
        console.log(`[MenuHandler] Closing active menu: ${menuId}`);
        this.closeAllMenus();
        return;
      }
      
      // Close any open menu first
      this.closeAllMenus();
      
      // Clear any existing content before showing
      const content = menu.querySelector('.menu-content');
      if (content) {
        content.innerHTML = '';
      }
      
      // Open the clicked menu
      menu.style.display = 'block';
      this.activeMenu = menuId;
      
      // Position the menu below its button if possible
      const buttonId = this.getButtonIdForMenu(menuId);
      if (buttonId) {
        const button = document.getElementById(buttonId);
        if (button) {
          const buttonRect = button.getBoundingClientRect();
          
          // Position the menu below the button
          menu.style.position = 'fixed';
          menu.style.top = `${buttonRect.bottom + window.scrollY}px`;
          menu.style.left = `${buttonRect.left + window.scrollX}px`;
          menu.style.zIndex = '1000';
          
          // Force reflow to ensure menu dimensions are calculated
          void menu.offsetHeight;
          
          // Get menu dimensions after rendering
          const menuRect = menu.getBoundingClientRect();
          const viewportWidth = window.innerWidth || document.documentElement.clientWidth;
          const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
          
          // Adjust if menu goes off right side of screen
          if (menuRect.right > viewportWidth) {
            menu.style.left = `${Math.max(10, viewportWidth - menuRect.width - 10)}px`;
          }
          
          // Adjust if menu goes off bottom of screen
          if (menuRect.bottom > viewportHeight) {
            menu.style.top = `${Math.max(10, buttonRect.top - menuRect.height)}px`;
          }
          
          console.log(`[MenuHandler] Positioned menu ${menuId} at (${menu.style.left}, ${menu.style.top})`);
        } else {
          console.warn(`[MenuHandler] Button not found: ${buttonId}`);
        }
      } else {
        console.warn(`[MenuHandler] No button ID found for menu: ${menuId}`);
      }
      
      // Initialize menu-specific content if needed
      if (menuId === 'thresholdsMenu') {
        console.log('[MenuHandler] Initializing thresholds menu...');
        // Create threshold sliders container if it doesn't exist
        if (!document.getElementById('threshold-sliders') && content) {
          const sliders = document.createElement('div');
          sliders.id = 'threshold-sliders';
          sliders.className = 'threshold-sliders';
          content.appendChild(sliders);
        }
        
        if (typeof window.initializeThresholdsMenu === 'function') {
          window.initializeThresholdsMenu();
        } else {
          console.warn('[MenuHandler] initializeThresholdsMenu function not found');
        }
      } 
      else if (menuId === 'logicMenu') {
        console.log('[MenuHandler] Initializing logic menu...');
        if (typeof window.initializeLogicMenu === 'function') {
          window.initializeLogicMenu();
        } else {
          console.warn('[MenuHandler] initializeLogicMenu function not found');
        }
      }
      
      console.log(`[MenuHandler] Successfully toggled menu: ${menuId}`);
      
    } catch (error) {
      console.error(`[MenuHandler] Error toggling menu ${menuId}:`, error);
    }
  }
  
  getButtonIdForMenu(menuId) {
    // Try to find the button by class first, then by ID
    const buttonMap = {
      'strategyMenu': { class: '.strategy-button', id: 'strategyButton' },
      'indicatorMenu': { class: '.indicator-button', id: 'indicatorButton' },
      'thresholdsMenu': { class: '.thresholds-button', id: 'thresholdsButton' },
      'logicMenu': { class: '.logic-button', id: 'logicButton' }
    };
    
    const buttonInfo = buttonMap[menuId];
    if (!buttonInfo) return null;
    
    // Try to find by class first
    const buttonByClass = document.querySelector(buttonInfo.class);
    if (buttonByClass) return buttonByClass.id || buttonInfo.id;
    
    // If not found by class, try by ID
    const buttonById = document.getElementById(buttonInfo.id);
    if (buttonById) return buttonInfo.id;
    
    return null;
  }
  
  closeAllMenus() {
    this.menus.forEach(menuId => {
      const menu = document.getElementById(menuId);
      if (menu) {
        menu.style.display = 'none';
        
        // Clean up any dynamically created content
        const content = menu.querySelector('.menu-content');
        if (content) {
          // Keep the content element but clear its children
          while (content.firstChild) {
            content.removeChild(content.firstChild);
          }
        }
      }
    });
    this.activeMenu = null;
  }
  
  setupClickOutsideHandler() {
    document.addEventListener('click', (e) => {
      if (this.activeMenu) {
        const menu = document.getElementById(this.activeMenu);
        const buttonId = this.getButtonIdForMenu(this.activeMenu);
        const button = buttonId ? document.getElementById(buttonId) : null;
        
        // Check if click is outside both the menu and its button
        if (menu && !menu.contains(e.target) && 
            (!button || !button.contains(e.target))) {
          this.closeAllMenus();
        }
      }
    });
  }
}

// Initialize the menu handler when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.menuHandler = new MenuHandler();
});
