// Market Trend Engine - Core visualization and analysis system
class MarketTrendEngine {
  constructor() {
    this.state = {
      trendStrength: 0, // -100 to +100
      marketPhase: 'ranging', // trending-up, trending-down, ranging
      keyLevels: {
        support: [],
        resistance: [],
      },
      indicators: {},
      lastUpdate: 0,
      updateInterval: 1000, // ms
      isProcessing: false,
    }
    this.initialize()
  }

  async initialize() {
    console.log('Initializing Market Trend Engine...')
    this.setupEventListeners()
    this.startUpdateCycle()
  }

  setupEventListeners() {
    // Listen for WebSocket data
    window.addEventListener('indicatorUpdate', (e) => {
      this.handleIndicatorUpdate(e.detail)
    })

    // Window resize handler
    let resizeTimeout
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimeout)
      resizeTimeout = setTimeout(() => this.updateVisualizations(), 200)
    })
  }

  startUpdateCycle() {
    setInterval(() => this.updateMarketAnalysis(), this.state.updateInterval)
  }

  handleIndicatorUpdate(data) {
    // Strict validation: ensure data is an object and not processing already
    if (!data || typeof data !== 'object' || this.state.isProcessing) return

    this.state.isProcessing = true

    try {
      // Update indicators data - with proper validation
      if (data.timeframe && typeof data.indicators === 'object' && data.indicators) {
        if (!this.state.indicators[data.timeframe]) {
          this.state.indicators[data.timeframe] = {}
        }

        Object.assign(this.state.indicators[data.timeframe], data.indicators)

        // Trigger analysis update
        this.updateMarketAnalysis()
      }
    } catch (error) {
      console.error('Error processing indicator update:', error)
    } finally {
      this.state.isProcessing = false
    }
  }

  updateMarketAnalysis() {
    if (this.state.isProcessing) return

    this.state.isProcessing = true

    try {
      const analysis = this.analyzeMarket()
      this.state.trendStrength = analysis.trendStrength
      this.state.marketPhase = analysis.marketPhase
      this.state.keyLevels = analysis.keyLevels
      this.state.lastUpdate = Date.now()

      this.updateVisualizations()
      this.triggerCustomEvent('marketAnalysisUpdate', analysis)
    } catch (error) {
      console.error('Error in market analysis:', error)
    } finally {
      this.state.isProcessing = false
    }
  }

  analyzeMarket() {
    const timeframes = Object.keys(this.state.indicators)
    if (timeframes.length === 0) return {
      trendStrength: 0,
      marketPhase: 'ranging',
      keyLevels: { support: [], resistance: [] },
    }

    // Multi-timeframe trend analysis
    let weightedTrend = 0
    let totalWeight = 0
    const timeframesByWeight = {
      1: 1, 5: 2, 15: 3, 60: 4, 240: 3, 1440: 2,
    }

    timeframes.forEach(tf => {
      const weight = timeframesByWeight[tf] || 1
      const indicators = this.state.indicators[tf]

      // Simple trend calculation (can be enhanced)
      if (indicators.rsi) {
        const rsi = indicators.rsi.value || 50
        weightedTrend += (rsi - 50) * weight
        totalWeight += weight
      }
    })

    const trendStrength = totalWeight > 0 ? (weightedTrend / totalWeight) * 2 : 0

    return {
      trendStrength: Math.max(-100, Math.min(100, trendStrength)),
      marketPhase: this.determineMarketPhase(trendStrength),
      keyLevels: this.calculateKeyLevels(),
    }
  }

  determineMarketPhase(trendStrength) {
    const absStrength = Math.abs(trendStrength)
    if (absStrength > 30) {
      return trendStrength > 0 ? 'trending-up' : 'trending-down'
    }
    return 'ranging'
  }

  calculateKeyLevels() {
    // Implement key level detection logic
    return {
      support: [],
      resistance: [],
    }
  }

  updateVisualizations() {
    // Update trend indicator
    this.updateTrendIndicator()

    // Update market phase visualization
    this.updateMarketPhaseVisual()

    // Update key levels display
    this.updateKeyLevelsDisplay()
  }

  updateTrendIndicator() {
    const indicator = document.getElementById('trend-strength-indicator')
    if (!indicator) return

    const strength = this.state.trendStrength
    const percentage = Math.abs(strength)
    const isPositive = strength >= 0

    // Update indicator bar
    const bar = indicator.querySelector('.trend-bar')
    if (bar) {
      bar.style.width = `${percentage}%`
      bar.style.backgroundColor = isPositive ? '#4CAF50' : '#F44336'
      bar.style.left = isPositive ? '50%' : `${50 - percentage}%`
    }

    // Update value display
    const valueDisplay = indicator.querySelector('.trend-value')
    if (valueDisplay) {
      valueDisplay.textContent = `${isPositive ? '+' : ''}${strength.toFixed(1)}`
      valueDisplay.style.color = isPositive ? '#4CAF50' : '#F44336'
    }
  }

  updateMarketPhaseVisual() {
    const container = document.getElementById('market-phase-display')
    if (!container) return

    const phase = this.state.marketPhase
    container.className = `market-phase ${phase}`

    const phaseText = {
      'trending-up': '↑ Uptrend',
      'trending-down': '↓ Downtrend',
      ranging: '↔ Ranging',
    }[phase] || 'Unknown'

    container.textContent = phaseText
  }

  updateKeyLevelsDisplay() {
    // Implement key levels visualization
  }

  triggerCustomEvent(eventName, detail = {}) {
    const event = new CustomEvent(eventName, { detail })
    window.dispatchEvent(event)
  }
}

// Initialize the engine when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  window.marketTrendEngine = new MarketTrendEngine()
})
