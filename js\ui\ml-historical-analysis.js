/**
 * ML Historical Analysis Features for StarCrypt
 * History search with golden highlights, convergence analysis, Fibonacci, and time-based predictions
 */

class MLHistoricalAnalysis {
  constructor() {
    this.selectedLights = new Set();
    this.historicalData = [];
    this.convergenceEvents = [];
    this.fibonacciLevels = [];
    this.timeBasedPredictions = {
      today: null,
      yesterday: null,
      lastWeek: null,
      lastMonth: null,
      all: null
    };

    this.analysisOptions = {
      searchDepth: 100, // Number of historical points to analyze
      convergenceThreshold: 0.8, // Minimum correlation for convergence
      fibonacciEnabled: true,
      goldenHighlights: true,
      autoAnalysis: true
    };

    this.init();
  }

  init() {
    console.log('[MLHistoricalAnalysis] Initializing historical analysis features...');

    try {
      this.createHistoricalAnalysisPanel();
      this.setupLightSelectionSystem();
      this.setupFibonacciAnalysis();
      this.initializeTimeBasedPredictions();
      this.startHistoricalAnalysis();

      console.log('[MLHistoricalAnalysis] Historical analysis features initialized successfully');
    } catch (error) {
      console.error('[MLHistoricalAnalysis] Error initializing historical analysis:', error);
    }
  }

  createHistoricalAnalysisPanel() {
    console.log('[MLHistoricalAnalysis] Creating historical analysis panel...');

    // Find or create historical analysis container
    let analysisContainer = document.getElementById('ml-historical-analysis');
    if (!analysisContainer) {
      analysisContainer = document.createElement('div');
      analysisContainer.id = 'ml-historical-analysis';
      analysisContainer.className = 'ml-historical-container';

      // Insert after advanced ML options
      const advancedML = document.getElementById('ml-advanced-options');
      if (advancedML && advancedML.parentNode) {
        advancedML.parentNode.insertBefore(analysisContainer, advancedML.nextSibling);
      }
    }

    analysisContainer.innerHTML = `
      <div class="historical-analysis-header">
        <h3>📊 Historical Analysis & Convergence Engine</h3>
        <button class="analysis-toggle-button" id="toggleHistoricalAnalysis">🔍 Analyze</button>
      </div>

      <div class="historical-analysis-content" id="historicalAnalysisContent">
        <div class="light-selection-panel">
          <h4>💡 Signal Light Selection</h4>
          <div class="selection-instructions">
            Click signal lights to select them for convergence analysis. Selected lights will have golden glowing trim.
          </div>
          <div class="selected-lights-display" id="selectedLightsDisplay">
            <span class="no-selection">No lights selected</span>
          </div>
          <div class="selection-controls">
            <button class="clear-selection-btn" id="clearSelection">Clear Selection</button>
            <button class="analyze-convergence-btn" id="analyzeConvergence">🔍 Analyze Convergence</button>
          </div>
        </div>

        <div class="convergence-results-panel">
          <h4>🎯 Convergence Analysis Results</h4>
          <div class="convergence-stats" id="convergenceStats">
            <div class="stat-item">
              <span class="stat-label">Total Convergences:</span>
              <span class="stat-value" id="totalConvergences">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Green Convergences:</span>
              <span class="stat-value green" id="greenConvergences">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Red Convergences:</span>
              <span class="stat-value red" id="redConvergences">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Success Rate:</span>
              <span class="stat-value" id="successRate">0%</span>
            </div>
          </div>
          <div class="convergence-timeline" id="convergenceTimeline">
            <div class="timeline-placeholder">Select lights and analyze to see convergence timeline</div>
          </div>
        </div>

        <div class="fibonacci-panel">
          <h4>📐 Fibonacci Analysis</h4>
          <div class="fibonacci-controls">
            <label class="fibonacci-toggle">
              <input type="checkbox" id="fibonacciEnabled" checked>
              <span class="checkbox-custom"></span>
              Enable Fibonacci Analysis
            </label>
            <button class="calculate-fibonacci-btn" id="calculateFibonacci">📊 Calculate Levels</button>
          </div>
          <div class="fibonacci-levels" id="fibonacciLevels">
            <div class="fibonacci-placeholder">Enable Fibonacci analysis to see retracement levels</div>
          </div>
        </div>

        <div class="time-predictions-panel">
          <h4>⏰ Time-Based Predictions</h4>
          <div class="prediction-dropdown">
            <select id="timePredictionSelect">
              <option value="today">Best Prediction Today</option>
              <option value="yesterday">Best Prediction Yesterday</option>
              <option value="lastWeek">Best Prediction Last Week</option>
              <option value="lastMonth">Best Prediction Last Month</option>
              <option value="all">Best Prediction All Time</option>
            </select>
            <button class="load-prediction-btn" id="loadTimePrediction">📈 Load Prediction</button>
          </div>
          <div class="prediction-display" id="timePredictionDisplay">
            <div class="prediction-placeholder">Select a time period to see the best prediction</div>
          </div>
        </div>

        <div class="advanced-options-panel">
          <h4>⚙️ Advanced Options</h4>
          <div class="option-row">
            <label>Search Depth:</label>
            <input type="range" id="searchDepthSlider" min="50" max="500" step="50" value="${this.analysisOptions.searchDepth}">
            <span class="option-value">${this.analysisOptions.searchDepth} points</span>
          </div>
          <div class="option-row">
            <label>Convergence Threshold:</label>
            <input type="range" id="convergenceThresholdSlider" min="0.5" max="1.0" step="0.1" value="${this.analysisOptions.convergenceThreshold}">
            <span class="option-value">${(this.analysisOptions.convergenceThreshold * 100).toFixed(0)}%</span>
          </div>
          <div class="option-row">
            <label class="option-toggle">
              <input type="checkbox" id="autoAnalysisToggle" ${this.analysisOptions.autoAnalysis ? 'checked' : ''}>
              <span class="checkbox-custom"></span>
              Auto Analysis
            </label>
          </div>
        </div>
      </div>
    `;

    this.setupHistoricalAnalysisEvents();
    this.applyHistoricalAnalysisStyles();
  }

  setupHistoricalAnalysisEvents() {
    // Toggle analysis panel
    const toggleBtn = document.getElementById('toggleHistoricalAnalysis');
    const content = document.getElementById('historicalAnalysisContent');

    if (toggleBtn && content) {
      toggleBtn.addEventListener('click', () => {
        content.classList.toggle('collapsed');
        toggleBtn.textContent = content.classList.contains('collapsed') ? '🔍 Analyze' : '❌ Close';
      });
    }

    // Clear selection
    const clearBtn = document.getElementById('clearSelection');
    if (clearBtn) {
      clearBtn.addEventListener('click', () => {
        this.clearLightSelection();
      });
    }

    // Analyze convergence
    const analyzeBtn = document.getElementById('analyzeConvergence');
    if (analyzeBtn) {
      analyzeBtn.addEventListener('click', () => {
        this.analyzeSelectedLights();
      });
    }

    // Fibonacci controls
    const fibToggle = document.getElementById('fibonacciEnabled');
    if (fibToggle) {
      fibToggle.addEventListener('change', (e) => {
        this.analysisOptions.fibonacciEnabled = e.target.checked;
        if (e.target.checked) {
          this.calculateFibonacciLevels();
        }
      });
    }

    const fibBtn = document.getElementById('calculateFibonacci');
    if (fibBtn) {
      fibBtn.addEventListener('click', () => {
        this.calculateFibonacciLevels();
      });
    }

    // Time prediction controls
    const timePredSelect = document.getElementById('timePredictionSelect');
    const loadPredBtn = document.getElementById('loadTimePrediction');

    if (loadPredBtn) {
      loadPredBtn.addEventListener('click', () => {
        const period = timePredSelect?.value || 'today';
        this.loadTimePrediction(period);
      });
    }

    // Advanced options
    const searchDepthSlider = document.getElementById('searchDepthSlider');
    if (searchDepthSlider) {
      searchDepthSlider.addEventListener('input', (e) => {
        this.analysisOptions.searchDepth = parseInt(e.target.value);
        const display = e.target.nextElementSibling;
        if (display) {
          display.textContent = `${this.analysisOptions.searchDepth} points`;
        }
      });
    }

    const convergenceSlider = document.getElementById('convergenceThresholdSlider');
    if (convergenceSlider) {
      convergenceSlider.addEventListener('input', (e) => {
        this.analysisOptions.convergenceThreshold = parseFloat(e.target.value);
        const display = e.target.nextElementSibling;
        if (display) {
          display.textContent = `${(this.analysisOptions.convergenceThreshold * 100).toFixed(0)}%`;
        }
      });
    }

    const autoToggle = document.getElementById('autoAnalysisToggle');
    if (autoToggle) {
      autoToggle.addEventListener('change', (e) => {
        this.analysisOptions.autoAnalysis = e.target.checked;
      });
    }
  }

  setupLightSelectionSystem() {
    console.log('[MLHistoricalAnalysis] Setting up light selection system...');

    // Add click handlers to all signal lights
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('signal-light') || e.target.classList.contains('circle')) {
        this.toggleLightSelection(e.target);
      }
    });
  }

  toggleLightSelection(lightElement) {
    const lightId = this.getLightId(lightElement);

    if (this.selectedLights.has(lightId)) {
      // Deselect
      this.selectedLights.delete(lightId);
      lightElement.classList.remove('golden-selected');
    } else {
      // Select
      this.selectedLights.add(lightId);
      lightElement.classList.add('golden-selected');
    }

    this.updateSelectedLightsDisplay();
    console.log(`[MLHistoricalAnalysis] Light ${lightId} ${this.selectedLights.has(lightId) ? 'selected' : 'deselected'}`);
  }

  getLightId(lightElement) {
    // Generate unique ID for the light based on its position and indicator
    const indicator = lightElement.closest('[data-indicator]')?.dataset.indicator || 'unknown';
    const timeframe = lightElement.closest('[data-timeframe]')?.dataset.timeframe || 'unknown';
    const position = Array.from(lightElement.parentNode.children).indexOf(lightElement);

    return `${indicator}-${timeframe}-${position}`;
  }

  clearLightSelection() {
    console.log('[MLHistoricalAnalysis] Clearing light selection...');

    // Remove golden highlights from all lights
    document.querySelectorAll('.golden-selected').forEach(light => {
      light.classList.remove('golden-selected');
    });

    this.selectedLights.clear();
    this.updateSelectedLightsDisplay();
  }

  updateSelectedLightsDisplay() {
    const display = document.getElementById('selectedLightsDisplay');
    if (!display) return;

    if (this.selectedLights.size === 0) {
      display.innerHTML = '<span class="no-selection">No lights selected</span>';
    } else {
      const lightsList = Array.from(this.selectedLights).map(lightId => {
        const [indicator, timeframe] = lightId.split('-');
        return `<span class="selected-light-tag">${indicator.toUpperCase()} (${timeframe})</span>`;
      }).join('');

      display.innerHTML = `
        <div class="selected-count">${this.selectedLights.size} lights selected:</div>
        <div class="selected-lights-list">${lightsList}</div>
      `;
    }
  }

  analyzeSelectedLights() {
    if (this.selectedLights.size === 0) {
      console.warn('[MLHistoricalAnalysis] No lights selected for analysis');
      return;
    }

    console.log(`[MLHistoricalAnalysis] Analyzing convergence for ${this.selectedLights.size} selected lights...`);

    // Simulate convergence analysis
    const convergenceData = this.performConvergenceAnalysis();
    this.displayConvergenceResults(convergenceData);
  }

  performConvergenceAnalysis() {
    console.log('[MLHistoricalAnalysis] Performing convergence analysis...');

    // Simulate historical convergence analysis
    const totalPoints = this.analysisOptions.searchDepth;
    const convergences = [];

    // Generate simulated convergence events
    for (let i = 0; i < totalPoints; i++) {
      const timestamp = Date.now() - (i * 60000); // 1 minute intervals
      const isConvergence = Math.random() > (1 - this.analysisOptions.convergenceThreshold);

      if (isConvergence) {
        const isGreen = Math.random() > 0.5;
        const wasSuccessful = Math.random() > 0.3; // 70% success rate

        convergences.push({
          timestamp,
          type: isGreen ? 'green' : 'red',
          successful: wasSuccessful,
          strength: Math.random() * 0.5 + 0.5, // 0.5 to 1.0
          priceChange: (Math.random() - 0.5) * 0.1 // -5% to +5%
        });
      }
    }

    const greenConvergences = convergences.filter(c => c.type === 'green').length;
    const redConvergences = convergences.filter(c => c.type === 'red').length;
    const successfulConvergences = convergences.filter(c => c.successful).length;
    const successRate = convergences.length > 0 ? (successfulConvergences / convergences.length) * 100 : 0;

    return {
      total: convergences.length,
      green: greenConvergences,
      red: redConvergences,
      successRate,
      events: convergences.slice(0, 10) // Show last 10 events
    };
  }

  displayConvergenceResults(data) {
    console.log('[MLHistoricalAnalysis] Displaying convergence results:', data);

    // Update statistics
    document.getElementById('totalConvergences').textContent = data.total;
    document.getElementById('greenConvergences').textContent = data.green;
    document.getElementById('redConvergences').textContent = data.red;
    document.getElementById('successRate').textContent = `${data.successRate.toFixed(1)}%`;

    // Update timeline
    const timeline = document.getElementById('convergenceTimeline');
    if (timeline && data.events.length > 0) {
      timeline.innerHTML = `
        <div class="timeline-header">Recent Convergence Events:</div>
        ${data.events.map(event => `
          <div class="timeline-event ${event.type} ${event.successful ? 'successful' : 'failed'}">
            <span class="event-time">${new Date(event.timestamp).toLocaleTimeString()}</span>
            <span class="event-type">${event.type.toUpperCase()}</span>
            <span class="event-strength">${(event.strength * 100).toFixed(0)}%</span>
            <span class="event-result">${event.successful ? '✅' : '❌'}</span>
            <span class="event-change">${event.priceChange > 0 ? '+' : ''}${(event.priceChange * 100).toFixed(2)}%</span>
          </div>
        `).join('')}
      `;
    }
  }

  calculateFibonacciLevels() {
    if (!this.analysisOptions.fibonacciEnabled) return;

    console.log('[MLHistoricalAnalysis] Calculating Fibonacci levels...');

    // Get current price data
    const currentPrice = this.getCurrentPrice();
    if (!currentPrice) return;

    // Calculate recent high and low
    const recentData = this.getRecentPriceData(50);
    if (recentData.length < 2) return;

    const high = Math.max(...recentData);
    const low = Math.min(...recentData);
    const range = high - low;

    // Standard Fibonacci retracement levels
    const fibLevels = [0, 0.236, 0.382, 0.5, 0.618, 0.786, 1.0];

    this.fibonacciLevels = fibLevels.map(level => ({
      level: level,
      price: high - (range * level),
      percentage: level * 100,
      type: level === 0 ? 'resistance' : level === 1 ? 'support' : 'retracement'
    }));

    this.displayFibonacciLevels();
  }

  displayFibonacciLevels() {
    const container = document.getElementById('fibonacciLevels');
    if (!container || this.fibonacciLevels.length === 0) return;

    container.innerHTML = `
      <div class="fibonacci-header">Fibonacci Retracement Levels:</div>
      ${this.fibonacciLevels.map(fib => `
        <div class="fibonacci-level ${fib.type}">
          <span class="fib-percentage">${fib.percentage.toFixed(1)}%</span>
          <span class="fib-price">$${fib.price.toFixed(8)}</span>
          <span class="fib-type">${fib.type}</span>
        </div>
      `).join('')}
    `;
  }

  loadTimePrediction(period) {
    console.log(`[MLHistoricalAnalysis] Loading time prediction for: ${period}`);

    // Simulate time-based prediction loading
    const prediction = this.generateTimePrediction(period);
    this.displayTimePrediction(prediction);
  }

  generateTimePrediction(period) {
    const currentPrice = this.getCurrentPrice() || 50000;

    // Generate different predictions based on time period
    const predictions = {
      today: {
        direction: 'bullish',
        confidence: 78,
        targetPrice: currentPrice * 1.025,
        reasoning: 'Strong momentum indicators and volume surge detected',
        timeframe: '4-6 hours'
      },
      yesterday: {
        direction: 'bearish',
        confidence: 65,
        targetPrice: currentPrice * 0.985,
        reasoning: 'Resistance level rejection and profit-taking signals',
        timeframe: 'End of day'
      },
      lastWeek: {
        direction: 'bullish',
        confidence: 82,
        targetPrice: currentPrice * 1.045,
        reasoning: 'Weekly trend reversal and institutional accumulation',
        timeframe: '3-5 days'
      },
      lastMonth: {
        direction: 'neutral',
        confidence: 55,
        targetPrice: currentPrice * 1.008,
        reasoning: 'Consolidation phase with mixed signals',
        timeframe: '1-2 weeks'
      },
      all: {
        direction: 'bullish',
        confidence: 89,
        targetPrice: currentPrice * 1.125,
        reasoning: 'Historical pattern analysis shows strong upward potential',
        timeframe: '2-4 weeks'
      }
    };

    return predictions[period] || predictions.today;
  }

  displayTimePrediction(prediction) {
    const display = document.getElementById('timePredictionDisplay');
    if (!display) return;

    const directionIcon = prediction.direction === 'bullish' ? '📈' : prediction.direction === 'bearish' ? '📉' : '➡️';
    const confidenceClass = prediction.confidence >= 80 ? 'high' : prediction.confidence >= 60 ? 'medium' : 'low';

    display.innerHTML = `
      <div class="time-prediction-card">
        <div class="prediction-header">
          <span class="prediction-direction ${prediction.direction}">${directionIcon} ${prediction.direction.toUpperCase()}</span>
          <span class="prediction-confidence ${confidenceClass}">${prediction.confidence}%</span>
        </div>
        <div class="prediction-target">
          <span class="target-label">Target Price:</span>
          <span class="target-price">$${prediction.targetPrice.toFixed(8)}</span>
        </div>
        <div class="prediction-timeframe">
          <span class="timeframe-label">Timeframe:</span>
          <span class="timeframe-value">${prediction.timeframe}</span>
        </div>
        <div class="prediction-reasoning">
          <strong>Analysis:</strong> ${prediction.reasoning}
        </div>
      </div>
    `;
  }

  getCurrentPrice() {
    // Try to get current price from various sources
    if (window.currentPrice) return window.currentPrice;
    if (window.indicatorsData) {
      const timeframes = Object.keys(window.indicatorsData);
      if (timeframes.length > 0) {
        return window.indicatorsData[timeframes[0]]?.currentPrice;
      }
    }
    return null;
  }

  getRecentPriceData(count = 50) {
    // Simulate recent price data
    const currentPrice = this.getCurrentPrice() || 50000;
    const prices = [];

    for (let i = 0; i < count; i++) {
      const variation = (Math.random() - 0.5) * 0.1; // ±5% variation
      prices.push(currentPrice * (1 + variation));
    }

    return prices;
  }

  initializeTimeBasedPredictions() {
    console.log('[MLHistoricalAnalysis] Initializing time-based predictions...');

    // Generate initial predictions for all time periods
    Object.keys(this.timeBasedPredictions).forEach(period => {
      this.timeBasedPredictions[period] = this.generateTimePrediction(period);
    });
  }

  startHistoricalAnalysis() {
    console.log('[MLHistoricalAnalysis] Starting historical analysis engine...');

    // Auto-analysis interval
    if (this.analysisOptions.autoAnalysis) {
      setInterval(() => {
        if (this.selectedLights.size > 0) {
          this.analyzeSelectedLights();
        }

        if (this.analysisOptions.fibonacciEnabled) {
          this.calculateFibonacciLevels();
        }
      }, 30000); // Every 30 seconds
    }
  }

  applyHistoricalAnalysisStyles() {
    const style = document.createElement('style');
    style.textContent = `
      /* ML Historical Analysis Styling */
      .ml-historical-container {
        background: rgba(0, 10, 20, 0.9);
        border: 1px solid rgba(255, 215, 0, 0.3);
        border-radius: 8px;
        margin: 10px 0;
        padding: 15px;
        font-family: 'Courier New', monospace;
      }

      .historical-analysis-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        border-bottom: 1px solid rgba(255, 215, 0, 0.2);
        padding-bottom: 10px;
      }

      .historical-analysis-header h3 {
        color: #ffd700;
        margin: 0;
        font-size: 16px;
      }

      .analysis-toggle-button {
        background: linear-gradient(135deg, #ffd700, #ffaa00);
        border: none;
        color: #000;
        padding: 5px 10px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        font-weight: bold;
      }

      .historical-analysis-content {
        transition: all 0.3s ease;
        overflow: hidden;
      }

      .historical-analysis-content.collapsed {
        max-height: 0;
        opacity: 0;
        padding: 0;
      }

      /* Golden Selection Effects */
      .signal-light.golden-selected,
      .circle.golden-selected {
        box-shadow: 0 0 20px #ffd700, 0 0 40px #ffd700, 0 0 60px #ffd700;
        border: 2px solid #ffd700;
        animation: goldenPulse 2s infinite;
      }

      @keyframes goldenPulse {
        0%, 100% {
          box-shadow: 0 0 20px #ffd700, 0 0 40px #ffd700, 0 0 60px #ffd700;
          transform: scale(1);
        }
        50% {
          box-shadow: 0 0 30px #ffd700, 0 0 60px #ffd700, 0 0 90px #ffd700;
          transform: scale(1.05);
        }
      }

      /* Panel Styling */
      .light-selection-panel,
      .convergence-results-panel,
      .fibonacci-panel,
      .time-predictions-panel,
      .advanced-options-panel {
        background: rgba(0, 20, 40, 0.5);
        border: 1px solid rgba(255, 215, 0, 0.2);
        border-radius: 6px;
        padding: 12px;
        margin: 10px 0;
      }

      .light-selection-panel h4,
      .convergence-results-panel h4,
      .fibonacci-panel h4,
      .time-predictions-panel h4,
      .advanced-options-panel h4 {
        color: #ffd700;
        margin: 0 0 10px 0;
        font-size: 14px;
      }

      .selection-instructions {
        color: #cccccc;
        font-size: 12px;
        margin-bottom: 10px;
        font-style: italic;
      }

      .selected-lights-display {
        background: rgba(0, 0, 0, 0.3);
        padding: 8px;
        border-radius: 4px;
        margin: 10px 0;
        min-height: 30px;
      }

      .no-selection {
        color: #888888;
        font-style: italic;
      }

      .selected-count {
        color: #ffd700;
        font-weight: bold;
        margin-bottom: 5px;
      }

      .selected-lights-list {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
      }

      .selected-light-tag {
        background: linear-gradient(135deg, #ffd700, #ffaa00);
        color: #000;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 10px;
        font-weight: bold;
      }

      .selection-controls {
        display: flex;
        gap: 10px;
        margin-top: 10px;
      }

      .clear-selection-btn,
      .analyze-convergence-btn,
      .calculate-fibonacci-btn,
      .load-prediction-btn {
        background: rgba(255, 215, 0, 0.2);
        border: 1px solid #ffd700;
        color: #ffd700;
        padding: 6px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 11px;
        transition: all 0.3s ease;
      }

      .clear-selection-btn:hover,
      .analyze-convergence-btn:hover,
      .calculate-fibonacci-btn:hover,
      .load-prediction-btn:hover {
        background: rgba(255, 215, 0, 0.4);
        transform: translateY(-1px);
      }

      /* Convergence Results */
      .convergence-stats {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        margin-bottom: 15px;
      }

      .stat-item {
        display: flex;
        justify-content: space-between;
        padding: 5px;
        background: rgba(0, 0, 0, 0.3);
        border-radius: 3px;
      }

      .stat-label {
        color: #cccccc;
        font-size: 11px;
      }

      .stat-value {
        color: #ffffff;
        font-weight: bold;
        font-size: 11px;
      }

      .stat-value.green { color: #00ff00; }
      .stat-value.red { color: #ff0000; }

      .convergence-timeline {
        max-height: 200px;
        overflow-y: auto;
        background: rgba(0, 0, 0, 0.2);
        border-radius: 4px;
        padding: 8px;
      }

      .timeline-header {
        color: #ffd700;
        font-weight: bold;
        margin-bottom: 8px;
        font-size: 12px;
      }

      .timeline-event {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 8px;
        margin: 2px 0;
        border-radius: 3px;
        font-size: 10px;
      }

      .timeline-event.green { background: rgba(0, 255, 0, 0.1); border-left: 3px solid #00ff00; }
      .timeline-event.red { background: rgba(255, 0, 0, 0.1); border-left: 3px solid #ff0000; }
      .timeline-event.successful { opacity: 1; }
      .timeline-event.failed { opacity: 0.6; }

      /* Fibonacci Styling */
      .fibonacci-controls {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 10px;
      }

      .fibonacci-toggle {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #ffffff;
        cursor: pointer;
      }

      .checkbox-custom {
        width: 16px;
        height: 16px;
        border: 2px solid #ffd700;
        border-radius: 3px;
        position: relative;
      }

      .fibonacci-toggle input[type="checkbox"]:checked + .checkbox-custom::after {
        content: '✓';
        position: absolute;
        top: -2px;
        left: 1px;
        color: #ffd700;
        font-weight: bold;
        font-size: 12px;
      }

      .fibonacci-levels {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 4px;
        padding: 8px;
      }

      .fibonacci-header {
        color: #ffd700;
        font-weight: bold;
        margin-bottom: 8px;
        font-size: 12px;
      }

      .fibonacci-level {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 3px 6px;
        margin: 2px 0;
        border-radius: 3px;
        font-size: 11px;
      }

      .fibonacci-level.resistance { background: rgba(255, 0, 0, 0.1); color: #ff6666; }
      .fibonacci-level.support { background: rgba(0, 255, 0, 0.1); color: #66ff66; }
      .fibonacci-level.retracement { background: rgba(255, 215, 0, 0.1); color: #ffd700; }

      /* Time Predictions */
      .prediction-dropdown {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
      }

      .prediction-dropdown select {
        background: rgba(0, 20, 40, 0.8);
        border: 1px solid rgba(255, 215, 0, 0.3);
        color: #ffffff;
        padding: 5px 10px;
        border-radius: 4px;
        flex: 1;
      }

      .time-prediction-card {
        background: rgba(0, 0, 0, 0.3);
        border-radius: 6px;
        padding: 12px;
      }

      .prediction-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
      }

      .prediction-direction {
        font-weight: bold;
        font-size: 14px;
      }

      .prediction-direction.bullish { color: #00ff00; }
      .prediction-direction.bearish { color: #ff0000; }
      .prediction-direction.neutral { color: #ffaa00; }

      .prediction-confidence {
        font-weight: bold;
        font-size: 12px;
      }

      .prediction-confidence.high { color: #00ff00; }
      .prediction-confidence.medium { color: #ffaa00; }
      .prediction-confidence.low { color: #ff6600; }

      .prediction-target,
      .prediction-timeframe {
        display: flex;
        justify-content: space-between;
        margin: 5px 0;
        font-size: 12px;
      }

      .target-price {
        color: #00ff88;
        font-weight: bold;
      }

      .prediction-reasoning {
        background: rgba(0, 0, 0, 0.2);
        padding: 8px;
        border-radius: 4px;
        font-size: 11px;
        color: #cccccc;
        line-height: 1.4;
        margin-top: 10px;
      }

      /* Advanced Options */
      .option-row {
        display: flex;
        align-items: center;
        gap: 10px;
        margin: 8px 0;
      }

      .option-row label {
        color: #ffffff;
        font-size: 12px;
        min-width: 120px;
      }

      .option-row input[type="range"] {
        flex: 1;
        margin: 0 10px;
      }

      .option-value {
        color: #ffd700;
        font-weight: bold;
        min-width: 60px;
        font-size: 11px;
      }

      .option-toggle {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #ffffff;
        cursor: pointer;
      }

      .option-toggle input[type="checkbox"]:checked + .checkbox-custom::after {
        content: '✓';
        position: absolute;
        top: -2px;
        left: 1px;
        color: #ffd700;
        font-weight: bold;
        font-size: 12px;
      }
    `;
    document.head.appendChild(style);
  }
}

// Initialize ML historical analysis
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    window.mlHistoricalAnalysis = new MLHistoricalAnalysis();
  }, 4000);
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = MLHistoricalAnalysis;
}