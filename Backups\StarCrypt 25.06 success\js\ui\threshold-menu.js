// thresholds-menu.js
(function() {
  window.isUpdatingSlidersProgrammatically = false;
  window.isUpdatingSignalLightsFromThresholds = false;
  window.TRADING_STRATEGIES = window.TRADING_STRATEGIES || {};
  window.INDICATORS = window.INDICATORS || { momentum: [], trend: [], volume: [], ml: [] };
  window.logMessages = window.logMessages || [];

  // Default threshold values
  window.defaultThresholds = {
    rsi: { red: 70, orange: 60, blue: 40, green: 30 },
    stochRsi: { red: 80, orange: 65, blue: 35, green: 20 },
    williamsR: { red: 80, orange: 65, blue: 35, green: 20 },
    ultimateOscillator: { red: 70, orange: 60, blue: 40, green: 30 },
    mfi: { red: 80, orange: 65, blue: 35, green: 20 },
    adx: { red: 80, orange: 65, blue: 35, green: 20 },
    bollingerBands: { red: 98, orange: 96, blue: 4, green: 2 },
    atr: { high: 2, moderate: 1, low: 0.5 },
    macd: { red: 70, orange: 60, blue: 40, green: 30 },
    volume: { red: 80, orange: 65, blue: 35, green: 20 }
  };

  // Initialize thresholds from localStorage or use defaults
  if (typeof window.thresholds === 'undefined') {
    const savedThresholds = localStorage.getItem('userThresholds');
    if (savedThresholds) {
      try {
        window.thresholds = JSON.parse(savedThresholds);
        // Ensure all default thresholds exist
        for (const key in window.defaultThresholds) {
          if (!window.thresholds[key]) {
            window.thresholds[key] = JSON.parse(JSON.stringify(window.defaultThresholds[key]));
          }
        }
      } catch (e) {
        console.error('Error loading saved thresholds:', e);
        window.thresholds = JSON.parse(JSON.stringify(window.defaultThresholds));
      }
    } else {
      window.thresholds = JSON.parse(JSON.stringify(window.defaultThresholds));
    }
  }

  // Main function to render threshold sliders
  function renderThresholdSliders(strategy) {
    try {
      const slidersContainer = document.getElementById('threshold-sliders');
      if (!slidersContainer) {
        console.error('Threshold sliders container not found');
        return;
      }
      
      // Clear existing sliders
      slidersContainer.innerHTML = '';
      
      // Get the current strategy's indicators or use all available
      const strategyIndicators = (strategy && strategy.indicators) || 
        [...new Set([...INDICATORS.momentum, ...INDICATORS.trend, ...INDICATORS.volume, ...INDICATORS.ml])];
      
      // Create a slider group for each indicator
      strategyIndicators.forEach(indicator => {
        if (!window.thresholds[indicator]) return; // Skip if no thresholds defined for this indicator
        
        const sliderGroup = document.createElement('div');
        sliderGroup.className = 'slider-group';
        sliderGroup.innerHTML = `
          <div class="slider-header">
            <h4>${formatIndicatorName(indicator)}</h4>
            <div class="threshold-values" id="${indicator}-values">
              Green: ${window.thresholds[indicator].green} | 
              Blue: ${window.thresholds[indicator].blue} | 
              Orange: ${window.thresholds[indicator].orange} | 
              Red: ${window.thresholds[indicator].red}
            </div>
          </div>
          <div class="slider-container" id="${indicator}-slider">
            <div class="slider-track">
              <div class="slider-segment green" id="${indicator}-green-segment"></div>
              <div class="slider-segment blue" id="${indicator}-blue-segment"></div>
              <div class="slider-segment neutral" id="${indicator}-neutral-segment"></div>
              <div class="slider-segment orange" id="${indicator}-orange-segment"></div>
              <div class="slider-segment red" id="${indicator}-red-segment"></div>
              
              <div class="slider-thumb" id="${indicator}-green-thumb" data-threshold="green" style="left: ${window.thresholds[indicator].green}%"></div>
              <div class="slider-thumb" id="${indicator}-blue-thumb" data-threshold="blue" style="left: ${window.thresholds[indicator].blue}%"></div>
              <div class="slider-thumb" id="${indicator}-orange-thumb" data-threshold="orange" style="left: ${window.thresholds[indicator].orange}%"></div>
              <div class="slider-thumb" id="${indicator}-red-thumb" data-threshold="red" style="left: ${window.thresholds[indicator].red}%"></div>
            </div>
          </div>
        `;
        
        slidersContainer.appendChild(sliderGroup);
        
        // Make thumbs draggable
        ['green', 'blue', 'orange', 'red'].forEach(threshold => {
          const thumb = document.getElementById(`${indicator}-${threshold}-thumb`);
          const sliderBar = document.getElementById(`${indicator}-slider`);
          if (thumb && sliderBar) {
            makeThumbDraggable(thumb, indicator, sliderBar);
          }
        });
      });
      
      // Add reset button if not exists
      if (!document.getElementById('resetThresholds')) {
        const resetBtn = document.createElement('button');
        resetBtn.id = 'resetThresholds';
        resetBtn.className = 'reset-thresholds-button';
        resetBtn.textContent = 'Reset to Defaults';
        resetBtn.addEventListener('click', resetThresholds);
        slidersContainer.appendChild(resetBtn);
      }
    } catch (error) {
      console.error('Error rendering threshold sliders:', error);
    }
  }

  // Helper function to format indicator names for display
  function formatIndicatorName(name) {
    return name
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .replace('Rsi', 'RSI')
      .replace('Macd', 'MACD')
      .replace('Adx', 'ADX')
      .replace('Mfi', 'MFI')
      .replace('Bollinger', 'Bollinger ')
      .replace('Williams', 'Williams ')
      .replace('Ultimate', 'Ultimate ')
      .replace('Oscillator', 'Oscillator ')
      .replace('  ', ' ')
      .trim();
  }

  // Make slider thumbs draggable
  function makeThumbDraggable(thumb, indicatorName, sliderBar) {
    let isDragging = false;
    let startX, startLeft;

    function startDrag(e) {
      isDragging = true;
      startX = e.clientX || e.touches[0].clientX;
      startLeft = parseFloat(thumb.style.left) || 0;
      document.addEventListener('mousemove', drag);
      document.addEventListener('touchmove', drag, { passive: false });
      document.addEventListener('mouseup', stopDrag);
      document.addEventListener('touchend', stopDrag);
      e.preventDefault();
      e.stopPropagation();
    }

    function drag(e) {
      if (!isDragging) return;
      e.preventDefault();
      
      const x = e.clientX || e.touches[0].clientX;
      const sliderRect = sliderBar.getBoundingClientRect();
      let newLeft = startLeft + ((x - startX) / sliderRect.width) * 100;
      newLeft = Math.max(0, Math.min(100, newLeft));
      
      thumb.style.left = `${newLeft}%`;
      const thumbType = thumb.dataset.threshold;
      window.thresholds[indicatorName][thumbType] = newLeft;
      
      updateThresholdDisplay(indicatorName);
    }

    function stopDrag() {
      if (!isDragging) return;
      isDragging = false;
      document.removeEventListener('mousemove', drag);
      document.removeEventListener('touchmove', drag);
      document.removeEventListener('mouseup', stopDrag);
      document.removeEventListener('touchend', stopDrag);
      localStorage.setItem('userThresholds', JSON.stringify(window.thresholds));
      if (window.updateAllSignalLights) window.updateAllSignalLights();
    }

    thumb.addEventListener('mousedown', startDrag);
    thumb.addEventListener('touchstart', startDrag, { passive: false });
  }

  // Reset all thresholds to their default values
  function resetThresholds() {
    if (confirm('Are you sure you want to reset all thresholds to their default values?')) {
      window.thresholds = JSON.parse(JSON.stringify(window.defaultThresholds));
      localStorage.setItem('userThresholds', JSON.stringify(window.thresholds));
      renderThresholdSliders();
      
      // Notify the user
      if (window.showToast) {
        window.showToast('Thresholds reset to default values', 'success');
      } else {
        alert('Thresholds reset to default values');
      }
      
      // Update signal lights if the function exists
      if (typeof window.updateSignalLights === 'function') {
        window.updateSignalLights();
      }
    }
  }

  // Update the visual display of threshold values
  function updateThresholdDisplay(indicatorName) {
    const th = window.thresholds[indicatorName];
    if (!th) return;

    // Update slider segments
    const sliderBar = document.getElementById(`${indicatorName}-slider`);
    if (sliderBar) {
      const segments = sliderBar.querySelectorAll('.slider-segment');
      segments[0].style.width = `${th.green}%`; // Green
      segments[1].style.left = `${th.green}%`;
      segments[1].style.width = `${th.blue - th.green}%`; // Blue
      segments[2].style.left = `${th.blue}%`;
      segments[2].style.width = `${th.orange - th.blue}%`; // Neutral
      segments[3].style.left = `${th.orange}%`;
      segments[3].style.width = `${th.red - th.orange}%`; // Orange
      segments[4].style.left = `${th.red}%`; // Red
      segments[4].style.width = `${100 - th.red}%`;
    }

    // Update thumb positions
    const container = document.querySelector(`.slider-group h4:contains("${formatIndicatorName(indicatorName)}")`)?.parentNode;
    if (container) {
      container.querySelector('.green-thumb').style.left = `${th.green}%`;
      container.querySelector('.blue-thumb').style.left = `${th.blue}%`;
      container.querySelector('.orange-thumb').style.left = `${th.orange}%`;
      container.querySelector('.red-thumb').style.left = `${th.red}%`;
    }

    // Update zone labels
    const labelsContainer = document.getElementById(`${indicatorName}-values`);
    if (labelsContainer) {
      labelsContainer.textContent = `Green: ${th.green} | Blue: ${th.blue} | Orange: ${th.orange} | Red: ${th.red}`;
    }
  }

  // Initialize the thresholds menu
  function initializeThresholdsMenu() {
    console.log('[Thresholds] Initializing thresholds menu');
    
    // Load saved thresholds or use defaults
    const savedThresholds = localStorage.getItem('userThresholds');
    if (savedThresholds) {
      try {
        window.thresholds = JSON.parse(savedThresholds);
      } catch (e) {
        console.error('Error parsing saved thresholds:', e);
        window.thresholds = JSON.parse(JSON.stringify(window.defaultThresholds));
      }
    } else {
      window.thresholds = JSON.parse(JSON.stringify(window.defaultThresholds));
    }
    
    // Get the menu container (should be created by menu-handler)
    const menu = document.getElementById('thresholdsMenu');
    if (!menu) {
      console.error('[Thresholds] Menu container not found');
      return;
    }
    
    // Get or create the menu content area
    let content = menu.querySelector('.menu-content');
    if (!content) {
      content = document.createElement('div');
      content.className = 'menu-content';
      menu.appendChild(content);
    }
    
    // Clear any existing content
    content.innerHTML = '';
    
    // Create the sliders container
    const slidersContainer = document.createElement('div');
    slidersContainer.id = 'threshold-sliders';
    slidersContainer.className = 'threshold-sliders-container';
    content.appendChild(slidersContainer);
    
    // Create actions container
    const actionsDiv = document.createElement('div');
    actionsDiv.className = 'menu-actions';
    
    // Add reset button
    const resetButton = document.createElement('button');
    resetButton.className = 'reset-button';
    resetButton.textContent = 'Reset to Defaults';
    resetButton.addEventListener('click', resetThresholds);
    actionsDiv.appendChild(resetButton);
    
    // Add actions to menu
    menu.insertBefore(actionsDiv, content);
    
    // Update the sliders with the current strategy
    renderThresholdSliders(window.currentStrategy || 'admiral_toa');
    
    console.log('[Thresholds] Menu initialization complete');
  }

  // Expose public API
  window.initializeThresholdsMenu = initializeThresholdsMenu;
  window.renderThresholdSliders = renderThresholdSliders;
  window.updateThresholdDisplay = updateThresholdDisplay;
  window.resetThresholds = resetThresholds;

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeThresholdsMenu);
  } else {
    setTimeout(initializeThresholdsMenu, 0);
  }
})();