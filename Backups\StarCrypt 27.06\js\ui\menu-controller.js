/**
 * Unified Menu Controller for StarCrypt
 * 
 * This file consolidates all menu button event handling logic from:
 * - js/ui/menu-handler.js
 * - js/strategy-manager.js
 * - js/ui/strategy-selector.js
 * 
 * It handles all menu button interactions inside the #tickerContainer
 */

class MenuController {
  constructor() {
    this.activeMenu = null;
    this.menus = ['strategyMenu', 'indicatorMenu', 'thresholdsMenu', 'logicMenu', 'timeframeMenu'];
    this.menuButtonMap = {
      'strategyButton': 'strategyMenu',
      'toggleMenuButton': 'indicatorMenu',
      'toggleThresholdsButton': 'thresholdsMenu',
      'toggleLogicButton': 'logicMenu',
      'toggleTimeframesButton': 'timeframeMenu'
    };
    this.initialize();
  }

  initialize() {
    try {
      console.log('[MenuController] Starting initialization...');
      
      // Ensure menu containers exist first
      this.ensureMenuContainersExist();
      
      // Setup event handlers
      this.setupMenuButtons();
      this.setupClickOutsideHandler();
      
      // Initialize menus that need it
      this.initializeMenus();
      
      console.log('[MenuController] Initialized menu controller');
    } catch (error) {
      console.error('[MenuController] Error initializing Menu Controller:', error);
    }
  }
  
  initializeMenus() {
    // Initialize any menus that need special setup
    if (typeof window.initializeThresholdsMenu === 'function') {
      window.initializeThresholdsMenu();
    }
    
    if (typeof window.initializeLogicMenu === 'function') {
      window.initializeLogicMenu();
    }
    
    // Initialize strategy selector
    if (typeof window.initializeStrategySelector === 'function') {
      window.initializeStrategySelector();
    }
  }

  ensureMenuContainersExist() {
    console.log('[MenuController] Ensuring menu containers exist...');
    
    // Ensure ticker container exists
    let tickerContainer = document.querySelector('.ticker-container') || document.getElementById('tickerContainer');
    if (!tickerContainer) {
      console.log('[MenuController] Creating ticker container');
      tickerContainer = document.createElement('div');
      tickerContainer.className = 'ticker-container';
      tickerContainer.id = 'tickerContainer';
      tickerContainer.style.position = 'relative';
      document.body.prepend(tickerContainer);
    }
    
    // Create menu containers
    this.menus.forEach(menuId => {
      let menu = document.getElementById(menuId);
      if (!menu) {
        console.log(`[MenuController] Creating menu container: ${menuId}`);
        menu = document.createElement('div');
        menu.id = menuId;
        menu.className = `menu-container ${menuId.toLowerCase().replace('menu', '-menu')}`;
        menu.style.display = 'none';
        menu.style.position = 'absolute';
        menu.style.top = '100%';
        menu.style.left = '0';
        menu.style.zIndex = '1000';
        menu.style.background = '#1a1a2e';
        menu.style.border = '1px solid #2a2a4a';
        menu.style.borderRadius = '4px';
        menu.style.padding = '10px';
        menu.style.minWidth = '250px';
        menu.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
        
        // Create header with close button
        const header = document.createElement('div');
        header.style.display = 'flex';
        header.style.justifyContent = 'space-between';
        header.style.alignItems = 'center';
        header.style.marginBottom = '10px';
        header.style.paddingBottom = '5px';
        header.style.borderBottom = '1px solid #2a2a4a';
        
        const title = document.createElement('h3');
        title.textContent = menuId.replace(/([A-Z])/g, ' $1').replace('Menu', '').trim();
        title.style.margin = '0';
        title.style.fontSize = '14px';
        title.style.color = '#fff';
        
        const closeBtn = document.createElement('button');
        closeBtn.className = 'close-menu';
        closeBtn.innerHTML = '&times;';
        closeBtn.style.background = 'transparent';
        closeBtn.style.border = 'none';
        closeBtn.style.color = '#fff';
        closeBtn.style.cursor = 'pointer';
        closeBtn.style.fontSize = '18px';
        closeBtn.style.lineHeight = '1';
        closeBtn.style.padding = '0 5px';
        closeBtn.onclick = (e) => {
          e.stopPropagation();
          this.closeAllMenus();
        };
        
        header.appendChild(title);
        header.appendChild(closeBtn);
        
        // Create content container
        const content = document.createElement('div');
        content.className = 'menu-content';
        content.id = `${menuId}Content`;
        content.style.maxHeight = '70vh';
        content.style.overflowY = 'auto';
        
        menu.appendChild(header);
        menu.appendChild(content);
        
        // Append to ticker container
        tickerContainer.appendChild(menu);
        
        console.log(`[MenuController] Created and appended ${menuId} container`);
      }
    });
  }

  setupMenuButtons() {
    console.log('[MenuController] Setting up menu buttons...');
    
    // Remove any existing click handlers by cloning and replacing buttons
    Object.keys(this.menuButtonMap).forEach(buttonId => {
      const button = document.getElementById(buttonId);
      if (button) {
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);
      }
    });
    
    // Create a new click handler for all menu buttons
    document.addEventListener('click', (e) => {
      // Find the closest menu button that was clicked
      const button = e.target.closest('.menu-button') || e.target.closest('button[id$="Button"]');
      if (!button) return;
      
      const buttonId = button.id;
      const menuId = this.menuButtonMap[buttonId];
      
      if (menuId) {
        e.preventDefault();
        e.stopPropagation();
        console.log(`[MenuController] Toggling menu: ${menuId} from button: ${buttonId}`);
        this.toggleMenu(menuId, button);
      }
    });
    
    console.log('[MenuController] Menu buttons setup complete');
  }

  toggleMenu(menuId, button) {
    try {
      console.log(`[MenuController] Toggling menu: ${menuId}`);
      
      // Ensure menu container exists
      this.ensureMenuContainersExist();
      
      const menu = document.getElementById(menuId);
      if (!menu) {
        console.error(`[MenuController] Menu not found: ${menuId}`);
        return;
      }
      
      // If clicking the currently active menu button, close the menu
      if (this.activeMenu === menuId) {
        console.log(`[MenuController] Closing active menu: ${menuId}`);
        this.closeAllMenus();
        return;
      }
      
      // Close any open menu first
      this.closeAllMenus();
      
      // Open the clicked menu
      menu.style.display = 'block';
      this.activeMenu = menuId;
      
      // Position the menu below its button
      if (button) {
        const buttonRect = button.getBoundingClientRect();
        
        // Position the menu below the button
        menu.style.position = 'fixed';
        menu.style.top = `${buttonRect.bottom + window.scrollY}px`;
        menu.style.left = `${buttonRect.left + window.scrollX}px`;
        menu.style.zIndex = '1000';
        
        // Force reflow to ensure menu dimensions are calculated
        void menu.offsetHeight;
        
        // Get menu dimensions after rendering
        const menuRect = menu.getBoundingClientRect();
        const viewportWidth = window.innerWidth || document.documentElement.clientWidth;
        const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
        
        // Adjust if menu goes off right side of screen
        if (menuRect.right > viewportWidth) {
          menu.style.left = `${Math.max(10, viewportWidth - menuRect.width - 10)}px`;
        }
        
        // Adjust if menu goes off bottom of screen
        if (menuRect.bottom > viewportHeight) {
          menu.style.top = `${Math.max(10, buttonRect.top - menuRect.height)}px`;
        }
        
        console.log(`[MenuController] Positioned menu ${menuId} at (${menu.style.left}, ${menu.style.top})`);
      }
      
      // Initialize menu-specific content if needed
      if (menuId === 'strategyMenu') {
        // If strategy menu has a selector, update it
        const selector = document.getElementById('strategySelector');
        if (selector && window.currentStrategy) {
          selector.value = window.currentStrategy;
          if (typeof window.updateStrategyDescription === 'function') {
            window.updateStrategyDescription();
          }
        }
      }
      else if (menuId === 'thresholdsMenu') {
        console.log('[MenuController] Initializing thresholds menu...');
        // Create threshold sliders container if it doesn't exist
        const content = menu.querySelector('.menu-content');
        if (!document.getElementById('threshold-sliders') && content) {
          const sliders = document.createElement('div');
          sliders.id = 'threshold-sliders';
          sliders.className = 'threshold-sliders';
          content.appendChild(sliders);
        }
        
        if (typeof window.initializeThresholdsMenu === 'function') {
          window.initializeThresholdsMenu();
        } else {
          console.warn('[MenuController] initializeThresholdsMenu function not found');
        }
      } 
      else if (menuId === 'logicMenu') {
        console.log('[MenuController] Initializing logic menu...');
        if (typeof window.initializeLogicMenu === 'function') {
          window.initializeLogicMenu();
        } else {
          console.warn('[MenuController] initializeLogicMenu function not found');
        }
      }
      else if (menuId === 'indicatorMenu') {
        console.log('[MenuController] Initializing indicator menu...');
        if (typeof window.updateIndicatorMenu === 'function') {
          window.updateIndicatorMenu(window.currentStrategy, false);
        } else {
          console.warn('[MenuController] updateIndicatorMenu function not found');
        }
      }
      
      console.log(`[MenuController] Successfully toggled menu: ${menuId}`);
      
    } catch (error) {
      console.error(`[MenuController] Error toggling menu ${menuId}:`, error);
    }
  }
  
  closeAllMenus() {
    this.menus.forEach(menuId => {
      const menu = document.getElementById(menuId);
      if (menu) {
        menu.style.display = 'none';
      }
    });
    this.activeMenu = null;
  }
  
  setupClickOutsideHandler() {
    document.addEventListener('click', (e) => {
      if (this.activeMenu) {
        const menu = document.getElementById(this.activeMenu);
        const buttonId = Object.keys(this.menuButtonMap).find(key => this.menuButtonMap[key] === this.activeMenu);
        const button = buttonId ? document.getElementById(buttonId) : null;
        
        // Check if click is outside both the menu and its button
        if (menu && !menu.contains(e.target) && 
            (!button || !button.contains(e.target))) {
          this.closeAllMenus();
        }
      }
    });
  }
}

// Initialize the menu controller when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  try {
    // Make sure we don't have multiple instances
    if (!window.menuController) {
      window.menuController = new MenuController();
      console.log('Menu Controller successfully initialized');
    }
  } catch (error) {
    console.error('Error initializing Menu Controller:', error);
  }
});
