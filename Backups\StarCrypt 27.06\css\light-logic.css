/* Light Logic and Logic Menu Styles */
.light-logic-description {
  margin: 10px 0;
  font-size: 0.9rem;
  color: #CCCCCC;
  line-height: 1.4;
}

.light-logic-option {
  margin: 15px 0;
  padding: 10px;
  background: rgba(0, 20, 40, 0.5);
  border-radius: 5px;
  border: 1px solid rgba(0, 255, 255, 0.2);
}

.option-description {
  margin-top: 5px;
  font-size: 0.8rem;
  color: #AAAAAA;
  font-style: italic;
}

/* Strategy button styles */
.strategy-button {
  background: linear-gradient(to right, #00BFFF, #00FFFF) !important;
  color: #000 !important;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: bold;
}

.strategy-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 191, 255, 0.4);
}

/* Light logic controls */
.light-logic-controls {
  padding: 15px;
}

.light-logic-option h4 {
  margin: 0 0 10px 0;
  color: #00FFFF;
  font-size: 1rem;
}

/* Logic Menu Styles */
#logicMenu {
  position: absolute;
  top: 100%;
  right: 0;
  width: 280px;
  background: #1a1a2e;
  border: 1px solid #303045;
  border-radius: 4px 0 4px 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
  z-index: 1000;
  padding: 15px;
  color: #e0e0e0;
  display: none;
}

#logicMenu.active {
  display: block;
  animation: fadeIn 0.3s ease-in-out;
}

#logicMenu h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #00ccff;
  font-size: 1.1rem;
  border-bottom: 1px solid #303045;
  padding-bottom: 8px;
}

.logic-settings {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.logic-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.logic-group label {
  font-size: 0.9rem;
  color: #aaa;
}

.logic-selector {
  padding: 6px 8px;
  background: #0f0f1a;
  border: 1px solid #303045;
  color: #e0e0e0;
  border-radius: 4px;
  font-size: 0.9rem;
}

.logic-description {
  font-size: 0.8rem;
  color: #888;
  margin: 5px 0 0;
  line-height: 1.4;
}

.apply-logic-button {
  background: #2a5a8a;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  margin-top: 10px;
  transition: background 0.2s;
}

.apply-logic-button:hover {
  background: #3a6a9a;
}

/* Animation for menu appearance */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* System notification */
.system-notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 10px 15px;
  border-radius: 4px;
  z-index: 2000;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

/* Preview light styles */
.preview-light {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 10px;
  vertical-align: middle;
}

/* Light logic presets */
.light-logic-presets {
  margin: 15px 0;
  padding: 10px;
  background: rgba(0, 20, 40, 0.3);
  border-radius: 5px;
  border: 1px solid rgba(0, 150, 255, 0.2);
}

.light-logic-presets h5 {
  margin: 0 0 10px 0;
  color: #00BFFF;
  font-size: 0.9rem;
}

.preset-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.preset-button {
  background: rgba(0, 150, 255, 0.2);
  border: 1px solid rgba(0, 150, 255, 0.5);
  color: #00BFFF;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.2s ease;
}

.preset-button:hover {
  background: rgba(0, 150, 255, 0.4);
  transform: translateY(-1px);
}

/* Slider styles */
.slider-container {
  margin: 10px 0;
  position: relative;
  padding: 15px 0;
}

.slider-container label {
  display: block;
  margin-bottom: 5px;
  color: #00BFFF;
  font-size: 0.9rem;
}

.slider-container input[type="range"] {
  width: 100%;
  -webkit-appearance: none;
  appearance: none;
  height: 4px;
  border-radius: 2px;
  background: rgba(0, 150, 255, 0.2);
  outline: none;
}

.slider-container input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #00BFFF;
  cursor: pointer;
  transition: all 0.2s ease;
}

.slider-container input[type="range"]:hover::-webkit-slider-thumb {
  transform: scale(1.2);
  box-shadow: 0 0 10px rgba(0, 191, 255, 0.8);
}

/* Color picker styles */
.color-picker-container {
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.color-picker-container label {
  margin-right: 10px;
  color: #00BFFF;
  font-size: 0.9rem;
  min-width: 100px;
}

.color-picker {
  width: 30px;
  height: 30px;
  border: 2px solid #333;
  border-radius: 4px;
  cursor: pointer;
  background: #000;
}

/* Animation preview */
.animation-preview {
  width: 100%;
  height: 60px;
  margin: 15px 0;
  background: rgba(0, 20, 40, 0.3);
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.animation-preview-light {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #00BFFF;
  position: relative;
}
