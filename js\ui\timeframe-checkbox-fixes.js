/**
 * Timeframe Checkbox Fixes for StarCrypt
 * Adds clickable checkboxes for timeframe selection with proper toggle functionality
 */

class TimeframeCheckboxFixes {
  constructor() {
    this.defaultTimeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];
    this.activeTimeframes = [...this.defaultTimeframes];
    this.currentTimeframe = '1h';
    this.maxTimeframes = 7;
    
    this.init();
  }

  init() {
    console.log('[TimeframeCheckboxFixes] Initializing timeframe checkbox functionality...');
    
    try {
      this.loadSavedTimeframes();
      this.enhanceExistingTimeframeSelectors();
      this.addTimeframeStyles();
      this.bindGlobalEvents();
      
      console.log('[TimeframeCheckboxFixes] Timeframe checkbox fixes applied successfully');
    } catch (error) {
      console.error('[TimeframeCheckboxFixes] Error applying timeframe checkbox fixes:', error);
    }
  }

  loadSavedTimeframes() {
    const saved = localStorage.getItem('activeTimeframes');
    if (saved) {
      try {
        this.activeTimeframes = JSON.parse(saved);
        console.log('[TimeframeCheckboxFixes] Loaded saved timeframes:', this.activeTimeframes);
      } catch (error) {
        console.warn('[TimeframeCheckboxFixes] Failed to load saved timeframes, using defaults');
      }
    }
  }

  saveTimeframes() {
    localStorage.setItem('activeTimeframes', JSON.stringify(this.activeTimeframes));
  }

  enhanceExistingTimeframeSelectors() {
    // Find all timeframe containers and enhance them
    const containers = [
      document.getElementById('timeframeControls'),
      document.querySelector('.timeframe-selector'),
      document.querySelector('.timeframe-buttons'),
      document.querySelector('#timeframesMenu .menu-content')
    ].filter(Boolean);

    containers.forEach(container => {
      this.enhanceTimeframeContainer(container);
    });

    // Monitor for new timeframe containers
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            if (node.id === 'timeframeControls' || 
                node.classList?.contains('timeframe-selector') ||
                node.classList?.contains('timeframe-buttons')) {
              this.enhanceTimeframeContainer(node);
            }
            
            const nestedTimeframes = node.querySelectorAll?.('#timeframeControls, .timeframe-selector, .timeframe-buttons');
            nestedTimeframes?.forEach(tf => this.enhanceTimeframeContainer(tf));
          }
        });
      });
    });

    observer.observe(document.body, { childList: true, subtree: true });
  }

  enhanceTimeframeContainer(container) {
    if (!container || container.dataset.enhanced === 'true') return;
    
    container.dataset.enhanced = 'true';
    
    // Check if this is a menu container or button container
    if (container.id === 'timeframeControls' || container.classList.contains('timeframe-buttons')) {
      this.enhanceButtonContainer(container);
    } else {
      this.enhanceMenuContainer(container);
    }
    
    console.log('[TimeframeCheckboxFixes] Enhanced timeframe container');
  }

  enhanceButtonContainer(container) {
    // Add checkbox controls above the timeframe buttons
    const checkboxContainer = document.createElement('div');
    checkboxContainer.className = 'timeframe-checkbox-controls';
    checkboxContainer.innerHTML = this.generateCheckboxControlsHTML();
    
    // Insert before the existing content
    container.insertBefore(checkboxContainer, container.firstChild);
    
    // Bind events for this container
    this.bindCheckboxEvents(checkboxContainer);
    
    // Update existing buttons to reflect active timeframes
    this.updateButtonVisibility(container);
  }

  enhanceMenuContainer(container) {
    // Add comprehensive checkbox interface for menu
    const menuContent = document.createElement('div');
    menuContent.className = 'timeframe-menu-enhanced';
    menuContent.innerHTML = this.generateMenuHTML();
    
    container.appendChild(menuContent);
    this.bindMenuEvents(menuContent);
  }

  generateCheckboxControlsHTML() {
    return `
      <div class="timeframe-checkbox-header">
        <h4>📊 Active Timeframes</h4>
        <div class="timeframe-controls">
          <button class="timeframe-toggle-all" id="toggleAllTimeframes">
            <span class="toggle-icon">☑️</span>
            Toggle All
          </button>
          <button class="timeframe-reset" id="resetTimeframes">
            <span class="reset-icon">🔄</span>
            Reset
          </button>
        </div>
      </div>
      
      <div class="timeframe-checkboxes-grid">
        ${this.defaultTimeframes.map(timeframe => this.generateCheckboxHTML(timeframe)).join('')}
      </div>
      
      <div class="timeframe-status">
        <span class="active-count">${this.activeTimeframes.length}/${this.maxTimeframes} active</span>
        <span class="max-warning ${this.activeTimeframes.length >= this.maxTimeframes ? 'visible' : ''}">
          Maximum timeframes reached
        </span>
      </div>
    `;
  }

  generateCheckboxHTML(timeframe) {
    const isActive = this.activeTimeframes.includes(timeframe);
    const isDisabled = !isActive && this.activeTimeframes.length >= this.maxTimeframes;
    
    return `
      <div class="timeframe-checkbox-item ${isActive ? 'active' : ''} ${isDisabled ? 'disabled' : ''}">
        <input type="checkbox" 
               id="timeframe-checkbox-${timeframe}" 
               data-timeframe="${timeframe}"
               ${isActive ? 'checked' : ''}
               ${isDisabled ? 'disabled' : ''}>
        <label for="timeframe-checkbox-${timeframe}" class="timeframe-checkbox-label">
          <span class="checkbox-custom"></span>
          <span class="timeframe-name">${timeframe.toUpperCase()}</span>
          <span class="timeframe-desc">${this.getTimeframeDescription(timeframe)}</span>
        </label>
      </div>
    `;
  }

  generateMenuHTML() {
    return `
      <div class="timeframe-menu-header">
        <h3>⏱️ Timeframe Selection</h3>
        <p>Select up to ${this.maxTimeframes} timeframes to display in the signal matrix</p>
      </div>
      
      <div class="timeframe-groups">
        <div class="timeframe-group">
          <h4>Short Term</h4>
          <div class="timeframe-group-checkboxes">
            ${['1m', '5m', '15m', '30m'].map(tf => this.generateCheckboxHTML(tf)).join('')}
          </div>
        </div>
        
        <div class="timeframe-group">
          <h4>Medium Term</h4>
          <div class="timeframe-group-checkboxes">
            ${['1h', '4h', '12h'].map(tf => this.generateCheckboxHTML(tf)).join('')}
          </div>
        </div>
        
        <div class="timeframe-group">
          <h4>Long Term</h4>
          <div class="timeframe-group-checkboxes">
            ${['1d', '3d', '1w', '1M'].map(tf => this.generateCheckboxHTML(tf)).join('')}
          </div>
        </div>
      </div>
      
      <div class="timeframe-presets">
        <h4>Quick Presets</h4>
        <div class="preset-buttons">
          <button class="preset-btn" data-preset="scalping">Scalping (1m,5m,15m)</button>
          <button class="preset-btn" data-preset="daytrading">Day Trading (5m,15m,1h,4h)</button>
          <button class="preset-btn" data-preset="swing">Swing Trading (1h,4h,1d,1w)</button>
          <button class="preset-btn" data-preset="default">Default (1m,5m,15m,1h,4h,1d,1w)</button>
        </div>
      </div>
      
      <div class="timeframe-actions">
        <button class="apply-timeframes-btn" id="applyTimeframeSelection">Apply Selection</button>
        <button class="cancel-timeframes-btn" id="cancelTimeframeSelection">Cancel</button>
      </div>
    `;
  }

  getTimeframeDescription(timeframe) {
    const descriptions = {
      '1m': '1 Minute',
      '5m': '5 Minutes',
      '15m': '15 Minutes',
      '30m': '30 Minutes',
      '1h': '1 Hour',
      '4h': '4 Hours',
      '12h': '12 Hours',
      '1d': '1 Day',
      '3d': '3 Days',
      '1w': '1 Week',
      '1M': '1 Month'
    };
    return descriptions[timeframe] || timeframe;
  }

  bindCheckboxEvents(container) {
    // Checkbox change events
    const checkboxes = container.querySelectorAll('input[data-timeframe]');
    checkboxes.forEach(checkbox => {
      checkbox.addEventListener('change', (e) => {
        this.handleCheckboxChange(e.target);
      });
    });

    // Toggle all button
    const toggleAllBtn = container.querySelector('#toggleAllTimeframes');
    if (toggleAllBtn) {
      toggleAllBtn.addEventListener('click', () => this.toggleAllTimeframes());
    }

    // Reset button
    const resetBtn = container.querySelector('#resetTimeframes');
    if (resetBtn) {
      resetBtn.addEventListener('click', () => this.resetToDefaults());
    }
  }

  bindMenuEvents(container) {
    this.bindCheckboxEvents(container);

    // Preset buttons
    const presetBtns = container.querySelectorAll('.preset-btn');
    presetBtns.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const preset = e.target.dataset.preset;
        this.applyPreset(preset);
      });
    });

    // Apply button
    const applyBtn = container.querySelector('#applyTimeframeSelection');
    if (applyBtn) {
      applyBtn.addEventListener('click', () => this.applyTimeframeSelection());
    }

    // Cancel button
    const cancelBtn = container.querySelector('#cancelTimeframeSelection');
    if (cancelBtn) {
      cancelBtn.addEventListener('click', () => this.cancelTimeframeSelection());
    }
  }

  bindGlobalEvents() {
    // Listen for timeframe menu requests
    document.addEventListener('click', (e) => {
      if (e.target.id === 'toggleTimeframesButton' || e.target.classList.contains('timeframe-menu-trigger')) {
        this.showTimeframeMenu();
      }
    });
  }

  handleCheckboxChange(checkbox) {
    const timeframe = checkbox.dataset.timeframe;
    const isChecked = checkbox.checked;
    
    if (isChecked) {
      // Add timeframe if not at max limit
      if (this.activeTimeframes.length < this.maxTimeframes && !this.activeTimeframes.includes(timeframe)) {
        this.activeTimeframes.push(timeframe);
        this.showNotification(`Added ${timeframe.toUpperCase()} timeframe`, 'success');
      } else if (this.activeTimeframes.length >= this.maxTimeframes) {
        checkbox.checked = false;
        this.showNotification(`Maximum ${this.maxTimeframes} timeframes allowed`, 'warning');
        return;
      }
    } else {
      // Remove timeframe
      const index = this.activeTimeframes.indexOf(timeframe);
      if (index > -1) {
        this.activeTimeframes.splice(index, 1);
        this.showNotification(`Removed ${timeframe.toUpperCase()} timeframe`, 'info');
      }
    }
    
    this.updateAllCheckboxes();
    this.updateButtonVisibility();
    this.saveTimeframes();
    this.applyTimeframeChanges();
  }

  toggleAllTimeframes() {
    if (this.activeTimeframes.length === this.defaultTimeframes.length) {
      // Uncheck all except current timeframe
      this.activeTimeframes = [this.currentTimeframe];
      this.showNotification('Disabled all timeframes except current', 'info');
    } else {
      // Check all default timeframes
      this.activeTimeframes = [...this.defaultTimeframes];
      this.showNotification('Enabled all default timeframes', 'success');
    }
    
    this.updateAllCheckboxes();
    this.updateButtonVisibility();
    this.saveTimeframes();
    this.applyTimeframeChanges();
  }

  resetToDefaults() {
    this.activeTimeframes = [...this.defaultTimeframes];
    this.updateAllCheckboxes();
    this.updateButtonVisibility();
    this.saveTimeframes();
    this.applyTimeframeChanges();
    this.showNotification('Reset to default timeframes', 'success');
  }

  applyPreset(preset) {
    const presets = {
      scalping: ['1m', '5m', '15m'],
      daytrading: ['5m', '15m', '1h', '4h'],
      swing: ['1h', '4h', '1d', '1w'],
      default: ['1m', '5m', '15m', '1h', '4h', '1d', '1w']
    };
    
    if (presets[preset]) {
      this.activeTimeframes = [...presets[preset]];
      this.updateAllCheckboxes();
      this.updateButtonVisibility();
      this.saveTimeframes();
      this.applyTimeframeChanges();
      this.showNotification(`Applied ${preset} preset`, 'success');
    }
  }

  updateAllCheckboxes() {
    document.querySelectorAll('input[data-timeframe]').forEach(checkbox => {
      const timeframe = checkbox.dataset.timeframe;
      const isActive = this.activeTimeframes.includes(timeframe);
      const isDisabled = !isActive && this.activeTimeframes.length >= this.maxTimeframes;
      
      checkbox.checked = isActive;
      checkbox.disabled = isDisabled;
      
      const item = checkbox.closest('.timeframe-checkbox-item');
      if (item) {
        item.classList.toggle('active', isActive);
        item.classList.toggle('disabled', isDisabled);
      }
    });
    
    // Update status displays
    document.querySelectorAll('.active-count').forEach(el => {
      el.textContent = `${this.activeTimeframes.length}/${this.maxTimeframes} active`;
    });
    
    document.querySelectorAll('.max-warning').forEach(el => {
      el.classList.toggle('visible', this.activeTimeframes.length >= this.maxTimeframes);
    });
  }

  updateButtonVisibility(container = null) {
    const containers = container ? [container] : document.querySelectorAll('#timeframeControls, .timeframe-buttons');
    
    containers.forEach(cont => {
      const buttons = cont.querySelectorAll('.timeframe-btn');
      buttons.forEach(btn => {
        const timeframe = btn.dataset.timeframe;
        if (timeframe) {
          btn.style.display = this.activeTimeframes.includes(timeframe) ? '' : 'none';
        }
      });
    });
  }

  applyTimeframeSelection() {
    this.applyTimeframeChanges();
    this.showNotification('Timeframe selection applied', 'success');
    
    // Close menu if open
    const menu = document.querySelector('.timeframe-menu-enhanced');
    if (menu) {
      menu.closest('.menu-box')?.classList.remove('open');
    }
  }

  cancelTimeframeSelection() {
    // Reload from saved state
    this.loadSavedTimeframes();
    this.updateAllCheckboxes();
    this.updateButtonVisibility();
    this.showNotification('Changes cancelled', 'info');
    
    // Close menu if open
    const menu = document.querySelector('.timeframe-menu-enhanced');
    if (menu) {
      menu.closest('.menu-box')?.classList.remove('open');
    }
  }

  applyTimeframeChanges() {
    // Update signal matrix columns
    this.updateSignalMatrixColumns();
    
    // Dispatch custom event
    window.dispatchEvent(new CustomEvent('timeframesChanged', {
      detail: { 
        activeTimeframes: this.activeTimeframes,
        currentTimeframe: this.currentTimeframe
      }
    }));
    
    // Update other systems
    if (typeof window.updateSignalMatrix === 'function') {
      window.updateSignalMatrix();
    }
    
    if (typeof window.renderSignalMatrix === 'function') {
      window.renderSignalMatrix();
    }
  }

  updateSignalMatrixColumns() {
    const signalMatrix = document.getElementById('momentum-indicators');
    if (!signalMatrix) return;
    
    // Update column visibility
    this.defaultTimeframes.forEach(timeframe => {
      const columns = signalMatrix.querySelectorAll(`[data-timeframe="${timeframe}"]`);
      const isActive = this.activeTimeframes.includes(timeframe);
      
      columns.forEach(column => {
        column.style.display = isActive ? '' : 'none';
      });
    });
    
    console.log(`[TimeframeCheckboxFixes] Updated signal matrix columns: ${this.activeTimeframes.join(', ')}`);
  }

  showTimeframeMenu() {
    // Create or show timeframe menu
    let menu = document.getElementById('timeframeSelectionMenu');
    if (!menu) {
      menu = document.createElement('div');
      menu.id = 'timeframeSelectionMenu';
      menu.className = 'menu-box timeframe-menu';
      menu.innerHTML = `
        <div class="menu-header">
          <h3>Timeframe Selection</h3>
          <button class="close-button" onclick="this.closest('.menu-box').classList.remove('open')">×</button>
        </div>
        <div class="menu-content"></div>
      `;
      document.body.appendChild(menu);
    }
    
    const content = menu.querySelector('.menu-content');
    content.innerHTML = this.generateMenuHTML();
    this.bindMenuEvents(content);
    
    menu.classList.add('open');
  }

  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `timeframe-notification ${type}`;
    notification.textContent = message;
    
    Object.assign(notification.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      padding: '12px 20px',
      borderRadius: '6px',
      color: '#ffffff',
      fontWeight: 'bold',
      zIndex: '10000',
      opacity: '0',
      transform: 'translateX(100%)',
      transition: 'all 0.3s ease',
      backgroundColor: type === 'success' ? '#4CAF50' : type === 'warning' ? '#FF9800' : type === 'error' ? '#f44336' : '#2196F3'
    });
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.style.opacity = '1';
      notification.style.transform = 'translateX(0)';
    }, 10);
    
    setTimeout(() => {
      notification.style.opacity = '0';
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }

  addTimeframeStyles() {
    const style = document.createElement('style');
    style.textContent = `
      .timeframe-checkbox-controls {
        margin-bottom: 15px;
        padding: 15px;
        background: rgba(26, 26, 46, 0.8);
        border: 1px solid #303045;
        border-radius: 8px;
      }
      
      .timeframe-checkbox-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
      }
      
      .timeframe-checkbox-header h4 {
        margin: 0;
        color: #00ffff;
        font-size: 16px;
      }
      
      .timeframe-controls {
        display: flex;
        gap: 8px;
      }
      
      .timeframe-toggle-all, .timeframe-reset {
        background: linear-gradient(135deg, #4ECDC4, #44A08D);
        border: 1px solid #4ECDC4;
        color: white;
        padding: 6px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 11px;
        font-weight: bold;
        display: flex;
        align-items: center;
        gap: 4px;
        transition: all 0.3s ease;
      }
      
      .timeframe-toggle-all:hover, .timeframe-reset:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(78, 205, 196, 0.3);
      }
      
      .timeframe-checkboxes-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 10px;
        margin-bottom: 15px;
      }
      
      .timeframe-checkbox-item {
        position: relative;
      }
      
      .timeframe-checkbox-item input[type="checkbox"] {
        display: none;
      }
      
      .timeframe-checkbox-label {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        border: 1px solid #303045;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        background: rgba(26, 26, 46, 0.6);
      }
      
      .timeframe-checkbox-item.disabled .timeframe-checkbox-label {
        opacity: 0.5;
        cursor: not-allowed;
      }
      
      .timeframe-checkbox-label:hover:not(.disabled) {
        border-color: #4ECDC4;
        background: rgba(78, 205, 196, 0.1);
      }
      
      .timeframe-checkbox-item.active .timeframe-checkbox-label {
        border-color: #4ECDC4;
        background: rgba(78, 205, 196, 0.2);
      }
      
      .checkbox-custom {
        width: 16px;
        height: 16px;
        border: 2px solid #4ECDC4;
        border-radius: 3px;
        position: relative;
        transition: all 0.3s ease;
      }
      
      .timeframe-checkbox-item.active .checkbox-custom {
        background: #4ECDC4;
      }
      
      .timeframe-checkbox-item.active .checkbox-custom::after {
        content: '✓';
        position: absolute;
        top: -2px;
        left: 2px;
        color: white;
        font-size: 12px;
        font-weight: bold;
      }
      
      .timeframe-name {
        font-weight: bold;
        color: #ffffff;
        min-width: 30px;
      }
      
      .timeframe-desc {
        font-size: 10px;
        color: #888;
      }
      
      .timeframe-status {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #888;
      }
      
      .max-warning {
        color: #FF9800;
        opacity: 0;
        transition: opacity 0.3s ease;
      }
      
      .max-warning.visible {
        opacity: 1;
      }
      
      .timeframe-groups {
        margin-bottom: 20px;
      }
      
      .timeframe-group {
        margin-bottom: 15px;
      }
      
      .timeframe-group h4 {
        margin: 0 0 10px 0;
        color: #4ECDC4;
        font-size: 14px;
      }
      
      .timeframe-group-checkboxes {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 8px;
      }
      
      .timeframe-presets {
        margin-bottom: 20px;
      }
      
      .preset-buttons {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 8px;
      }
      
      .preset-btn {
        background: linear-gradient(135deg, #6c5ce7, #a29bfe);
        border: 1px solid #6c5ce7;
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 11px;
        transition: all 0.3s ease;
      }
      
      .preset-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(108, 92, 231, 0.3);
      }
      
      .timeframe-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
      }
      
      .apply-timeframes-btn, .cancel-timeframes-btn {
        padding: 10px 20px;
        border-radius: 6px;
        cursor: pointer;
        font-weight: bold;
        transition: all 0.3s ease;
      }
      
      .apply-timeframes-btn {
        background: linear-gradient(135deg, #4CAF50, #66BB6A);
        border: 1px solid #4CAF50;
        color: white;
      }
      
      .cancel-timeframes-btn {
        background: linear-gradient(135deg, #f44336, #EF5350);
        border: 1px solid #f44336;
        color: white;
      }
      
      .apply-timeframes-btn:hover, .cancel-timeframes-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }
    `;
    document.head.appendChild(style);
  }
}

// Initialize timeframe checkbox fixes
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    window.timeframeCheckboxFixes = new TimeframeCheckboxFixes();
  }, 2000);
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = TimeframeCheckboxFixes;
}
