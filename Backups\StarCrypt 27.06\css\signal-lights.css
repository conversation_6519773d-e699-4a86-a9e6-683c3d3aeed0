/* Signal Light Styles */

/* Signal Matrix Timeframe Selection */
.signal-cell.timeframe {
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
  position: relative;
  z-index: 1;
}

.signal-cell.timeframe.selected-timeframe {
  background: rgba(0, 150, 255, 0.2);
  box-shadow: 0 0 10px rgba(0, 150, 255, 0.5);
  font-weight: bold;
  z-index: 2;
}

/* Highlight entire column */
.signal-cell.highlight-column {
  background: rgba(0, 150, 255, 0.1);
  position: relative;
}

/* Add a subtle border to the left of the highlighted column */
.signal-cell.highlight-column:not(:first-child) {
  border-left: 1px solid rgba(0, 150, 255, 0.3);
}

/* Hover effects */
.signal-cell.timeframe:hover {
  background: rgba(0, 150, 255, 0.1);
}

.signal-cell:not(.timeframe):hover {
  background: rgba(0, 150, 255, 0.05);
  cursor: pointer;
}

/* Pulse animation for the default selected timeframe */
@keyframes timeframe-pulse {
  0% { box-shadow: 0 0 5px rgba(0, 150, 255, 0.5); }
  50% { box-shadow: 0 0 15px rgba(0, 150, 255, 0.8); }
  100% { box-shadow: 0 0 5px rgba(0, 150, 255, 0.5); }
}

.signal-cell.timeframe.selected-timeframe {
  animation: timeframe-pulse 2s infinite;
}
.signal-circle {
  position: relative;
  width: 36px !important;
  height: 36px !important;
  min-width: 36px !important;
  min-height: 36px !important;
  line-height: 36px !important;
  border-radius: 50% !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 2px !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
  /* Ensure interactivity */
  z-index: 10 !important;
  pointer-events: auto !important;
}

/* Signal States */
.signal-circle.grey-light {
  background-color: #9E9E9E !important;
  border: 2px solid #E0E0E0 !important;
}

.signal-circle.green-light {
  background-color: #00C853 !important;
  border: 2px solid #00E676 !important;
  animation: pulse 2s infinite;
}

.signal-circle.blue-light {
  background-color: #2962FF !important;
  border: 2px solid #448AFF !important;
  animation: pulse 2.5s infinite;
}

.signal-circle.orange-light {
  background-color: #FF9100 !important;
  border: 2px solid #FFAB40 !important;
  color: #212121 !important;
  animation: pulse 2.5s infinite;
}

.signal-circle.red-light {
  background-color: #FF3D00 !important;
  border: 2px solid #FF6E40 !important;
  animation: pulse 1.5s infinite;
}

/* Pulse Animation */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Signal Light Container */
.signal-light-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0.5rem;
  min-width: 60px;
}

.signal-label {
  margin-top: 0.5rem;
  font-size: 0.8rem;
  color: var(--text-color);
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Signal Light Grid */
.signal-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
  gap: 1rem;
  width: 100%;
  padding: 1rem;
  justify-content: center;
}

/* Signal Light Hover Effects */
.signal-light-container:hover .signal-circle {
  transform: scale(1.1);
  box-shadow: 0 0 25px currentColor;
}

/* Signal Light Tooltip */
.signal-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  white-space: nowrap;
  z-index: 1000;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.2s ease;
  /* Ensure tooltip doesn't block interaction */
  pointer-events: none !important;
}

.signal-light-container:hover .signal-tooltip {
  opacity: 1;
}

/* Signal Light Animations */
@keyframes flash {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

/* Signal Light Sizes */
.signal-circle.small {
  width: 40px;
  height: 40px;
}

.signal-circle.medium {
  width: 60px;
  height: 60px;
}

.signal-circle.large {
  width: 80px;
  height: 80px;
}

/* Signal Light Themes */
.theme-dark .signal-circle {
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.theme-light .signal-circle {
  border: 2px solid rgba(0, 0, 0, 0.1);
}

/* Signal Light Status */
.signal-status {
  position: absolute;
  bottom: -20px;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 0.7rem;
  color: var(--text-color);
  opacity: 0.8;
}

/* Signal Light Loading State */
.signal-circle.loading {
  background: conic-gradient(
    from 0deg,
    #333 0%,
    #666 50%,
    #333 100%
  );
  animation: spin 1s linear infinite;
  box-shadow: none;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Signal Light Group */
.signal-group {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  margin: 1rem 0;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

/* Signal Light Legend */
.signal-legend {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin: 1rem 0;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .signal-grid {
    grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
    gap: 0.5rem;
    padding: 0.5rem;
  }
  
  .signal-circle.medium {
    width: 50px;
    height: 50px;
  }
  
  .signal-label {
    font-size: 0.7rem;
  }
}
