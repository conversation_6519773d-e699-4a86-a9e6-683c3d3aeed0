/**
 * Chart Container Fixes for StarCrypt
 * Addresses chart creation fallback errors and ensures proper chart container initialization
 */

class ChartContainerFixes {
  constructor() {
    this.chartContainers = new Map();
    this.missingCharts = new Set();
    this.createdCharts = new Set();
    
    this.init();
  }

  init() {
    console.log('[ChartContainerFixes] Initializing chart container fixes...');
    
    try {
      // Pre-create chart containers for all indicators
      this.preCreateChartContainers();
      
      // Fix existing chart container issues
      this.fixExistingChartContainers();
      
      // Monitor for new indicators and create charts as needed
      this.setupChartContainerMonitoring();
      
      // Override the problematic chart creation function
      this.overrideChartCreationFunction();
      
      console.log('[ChartContainerFixes] Chart container fixes applied successfully');
    } catch (error) {
      console.error('[ChartContainerFixes] Error applying chart container fixes:', error);
    }
  }

  preCreateChartContainers() {
    console.log('[ChartContainerFixes] Pre-creating chart containers for all indicators...');
    
    // List of all indicators that need charts
    const indicators = [
      'rsi', 'macd', 'bb', 'sma', 'ema', 'stoch', 'cci', 'atr', 'adx',
      'williams', 'momentum', 'roc', 'trix', 'dmi', 'psar', 'ichimoku',
      'volume', 'obv', 'mfi', 'vwap', 'fibonacci', 'pivot', 'support',
      'resistance', 'trend', 'volatility', 'correlation'
    ];
    
    indicators.forEach(indicator => {
      this.createChartContainerForIndicator(indicator);
    });
    
    console.log(`[ChartContainerFixes] Pre-created chart containers for ${indicators.length} indicators`);
  }

  createChartContainerForIndicator(indicator) {
    // Check if chart container already exists
    const existingContainer = document.getElementById(`${indicator}Chart`);
    if (existingContainer) {
      console.log(`[ChartContainerFixes] Chart container for ${indicator} already exists`);
      return existingContainer;
    }
    
    // Find the indicator row
    const indicatorRow = document.querySelector(`tr[data-indicator="${indicator}"]`);
    if (!indicatorRow) {
      console.warn(`[ChartContainerFixes] No indicator row found for ${indicator}`);
      return null;
    }
    
    // Find or create the chart cell
    let chartCell = indicatorRow.querySelector('.chart-cell');
    if (!chartCell) {
      chartCell = document.createElement('td');
      chartCell.className = 'chart-cell';
      indicatorRow.appendChild(chartCell);
    }
    
    // Create the chart container
    const chartContainer = document.createElement('div');
    chartContainer.id = `${indicator}Chart`;
    chartContainer.className = 'mini-chart-container';
    chartContainer.style.cssText = `
      width: 100%;
      height: 60px;
      position: relative;
      background: linear-gradient(135deg, rgba(26, 26, 46, 0.8), rgba(46, 26, 46, 0.6));
      border: 1px solid rgba(78, 205, 196, 0.3);
      border-radius: 4px;
      overflow: hidden;
    `;
    
    // Add canvas for chart
    const canvas = document.createElement('canvas');
    canvas.id = `${indicator}ChartCanvas`;
    canvas.width = 200;
    canvas.height = 60;
    canvas.style.cssText = `
      width: 100%;
      height: 100%;
      display: block;
    `;
    
    chartContainer.appendChild(canvas);
    chartCell.appendChild(chartContainer);
    
    // Store reference
    this.chartContainers.set(indicator, chartContainer);
    this.createdCharts.add(indicator);
    
    console.log(`[ChartContainerFixes] Created chart container for ${indicator}`);
    return chartContainer;
  }

  fixExistingChartContainers() {
    console.log('[ChartContainerFixes] Fixing existing chart container issues...');
    
    // Find all existing chart containers and ensure they're properly configured
    const existingCharts = document.querySelectorAll('.mini-chart-container');
    
    existingCharts.forEach(chart => {
      // Ensure proper styling
      if (!chart.style.position) {
        chart.style.position = 'relative';
      }
      
      if (!chart.style.height) {
        chart.style.height = '60px';
      }
      
      // Ensure canvas exists
      let canvas = chart.querySelector('canvas');
      if (!canvas) {
        canvas = document.createElement('canvas');
        canvas.width = 200;
        canvas.height = 60;
        canvas.style.cssText = 'width: 100%; height: 100%; display: block;';
        chart.appendChild(canvas);
      }
      
      // Store reference if we can identify the indicator
      const indicator = this.getIndicatorFromChart(chart);
      if (indicator) {
        this.chartContainers.set(indicator, chart);
      }
    });
    
    console.log(`[ChartContainerFixes] Fixed ${existingCharts.length} existing chart containers`);
  }

  setupChartContainerMonitoring() {
    console.log('[ChartContainerFixes] Setting up chart container monitoring...');
    
    // Monitor for new indicator rows being added
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check if it's an indicator row
            if (node.matches && node.matches('tr[data-indicator]')) {
              const indicator = node.dataset.indicator;
              if (indicator && !this.chartContainers.has(indicator)) {
                console.log(`[ChartContainerFixes] New indicator row detected: ${indicator}`);
                this.createChartContainerForIndicator(indicator);
              }
            }
            
            // Check for indicator rows within added nodes
            const indicatorRows = node.querySelectorAll ? node.querySelectorAll('tr[data-indicator]') : [];
            indicatorRows.forEach((row) => {
              const indicator = row.dataset.indicator;
              if (indicator && !this.chartContainers.has(indicator)) {
                console.log(`[ChartContainerFixes] New nested indicator row detected: ${indicator}`);
                this.createChartContainerForIndicator(indicator);
              }
            });
          }
        });
      });
    });
    
    // Start observing
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
    
    console.log('[ChartContainerFixes] Chart container monitoring active');
  }

  overrideChartCreationFunction() {
    console.log('[ChartContainerFixes] Overriding chart creation function...');
    
    // Override the problematic chart creation in indicator-display-fixed.js
    if (window.IndicatorDisplay) {
      const originalCreateChart = window.IndicatorDisplay.prototype.createChart;
      
      window.IndicatorDisplay.prototype.createChart = (indicator, data) => {
        console.log(`[ChartContainerFixes] Creating chart for ${indicator}...`);
        
        // Ensure container exists first
        let container = this.chartContainers.get(indicator);
        if (!container) {
          container = this.createChartContainerForIndicator(indicator);
        }
        
        if (!container) {
          console.error(`[ChartContainerFixes] Failed to create container for ${indicator}`);
          return null;
        }
        
        // Call original function if it exists
        if (originalCreateChart) {
          try {
            return originalCreateChart.call(this, indicator, data);
          } catch (error) {
            console.error(`[ChartContainerFixes] Error in original createChart for ${indicator}:`, error);
          }
        }
        
        // Fallback chart creation
        return this.createFallbackChart(indicator, data, container);
      };
    }
    
    // Also create a global chart creation function
    window.createIndicatorChart = (indicator, data) => {
      return this.createIndicatorChart(indicator, data);
    };
  }

  createIndicatorChart(indicator, data) {
    console.log(`[ChartContainerFixes] Creating indicator chart for ${indicator}`);
    
    // Get or create container
    let container = this.chartContainers.get(indicator);
    if (!container) {
      container = this.createChartContainerForIndicator(indicator);
    }
    
    if (!container) {
      console.error(`[ChartContainerFixes] No container available for ${indicator}`);
      return null;
    }
    
    return this.createFallbackChart(indicator, data, container);
  }

  createFallbackChart(indicator, data, container) {
    console.log(`[ChartContainerFixes] Creating fallback chart for ${indicator}`);
    
    const canvas = container.querySelector('canvas');
    if (!canvas) {
      console.error(`[ChartContainerFixes] No canvas found in container for ${indicator}`);
      return null;
    }
    
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error(`[ChartContainerFixes] Failed to get canvas context for ${indicator}`);
      return null;
    }
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw simple chart based on data
    if (data && Array.isArray(data) && data.length > 0) {
      this.drawSimpleChart(ctx, data, canvas.width, canvas.height);
    } else {
      this.drawPlaceholderChart(ctx, indicator, canvas.width, canvas.height);
    }
    
    console.log(`[ChartContainerFixes] Fallback chart created for ${indicator}`);
    return container;
  }

  drawSimpleChart(ctx, data, width, height) {
    // Simple line chart
    const values = data.map(d => typeof d === 'number' ? d : (d.value || d.close || 0));
    const max = Math.max(...values);
    const min = Math.min(...values);
    const range = max - min || 1;
    
    ctx.strokeStyle = '#4ECDC4';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    values.forEach((value, index) => {
      const x = (index / (values.length - 1)) * width;
      const y = height - ((value - min) / range) * height;
      
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    
    ctx.stroke();
  }

  drawPlaceholderChart(ctx, indicator, width, height) {
    // Draw placeholder
    ctx.fillStyle = 'rgba(78, 205, 196, 0.1)';
    ctx.fillRect(0, 0, width, height);
    
    ctx.strokeStyle = 'rgba(78, 205, 196, 0.3)';
    ctx.strokeRect(0, 0, width, height);
    
    ctx.fillStyle = 'rgba(78, 205, 196, 0.6)';
    ctx.font = '10px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(indicator.toUpperCase(), width / 2, height / 2 + 3);
  }

  getIndicatorFromChart(chart) {
    // Try to identify indicator from chart container
    const row = chart.closest('tr[data-indicator]');
    if (row) {
      return row.dataset.indicator;
    }
    
    // Try from ID
    if (chart.id && chart.id.endsWith('Chart')) {
      return chart.id.replace('Chart', '');
    }
    
    return null;
  }

  getChartContainer(indicator) {
    return this.chartContainers.get(indicator);
  }

  hasChart(indicator) {
    return this.chartContainers.has(indicator);
  }

  getAllCharts() {
    return Array.from(this.chartContainers.keys());
  }
}

// Initialize chart container fixes
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    window.chartContainerFixes = new ChartContainerFixes();
  }, 2000); // Initialize after DOM is ready but before other chart systems
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ChartContainerFixes;
}
