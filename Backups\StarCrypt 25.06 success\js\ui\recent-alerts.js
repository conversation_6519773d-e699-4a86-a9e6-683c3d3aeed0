// recent-alerts.js
// Implements the recent alerts functionality for StarCrypt

(function () {
  // Store alerts in memory
  window.recentAlerts = []
  const MAX_ALERTS = 20 // Maximum number of alerts to store

  // Add a new alert
  window.addAlert = function (alert) {
    if (!alert) return

    // Create alert object with timestamp
    const alertObj = {
      ...alert,
      timestamp: alert.timestamp || Date.now(),
      formattedTime: alert.formattedTime || new Date().toLocaleTimeString(),
    }

    // Add to beginning of array
    window.recentAlerts.unshift(alertObj)

    // Trim array if it exceeds maximum length
    if (window.recentAlerts.length > MAX_ALERTS) {
      window.recentAlerts = window.recentAlerts.slice(0, MAX_ALERTS)
    }

    // Update the UI
    updateRecentAlertsUI()

    // Log the alert
    if (window.logMessages) {
      window.logMessages.push(`[${new Date().toLocaleString()}] New alert: ${alertObj.message}`)
      if (window.updateLogger) window.updateLogger()
    }
  }

  // Update recent alerts for a specific pair
  window.updateRecentAlerts = function (pair) {
    // Get alerts container
    const alertsContainer = document.getElementById('recent-alerts-container')
    if (!alertsContainer) return

    // If there are no alerts yet, create some sample alerts for the pair
    if (window.recentAlerts.length === 0) {
      // Create sample alerts based on the current pair
      const sampleAlerts = [
        {
          pair,
          type: 'signal',
          signalClass: 'degen-buy',
          message: `Strong buy signal detected for ${pair.toUpperCase()} on 1h timeframe`,
          indicator: 'RSI',
          value: '28.5',
          timestamp: Date.now() - 60000,
          formattedTime: new Date(Date.now() - 60000).toLocaleTimeString(),
        },
        {
          pair,
          type: 'signal',
          signalClass: 'mild-sell',
          message: `Mild sell signal detected for ${pair.toUpperCase()} on 4h timeframe`,
          indicator: 'MACD',
          value: '-0.0023',
          timestamp: Date.now() - 300000,
          formattedTime: new Date(Date.now() - 300000).toLocaleTimeString(),
        },
        {
          pair,
          type: 'convergence',
          signalClass: 'degen-buy',
          message: `Multiple indicator convergence for ${pair.toUpperCase()} on 1h timeframe`,
          indicators: ['RSI', 'Stoch RSI', 'MACD'],
          timestamp: Date.now() - 600000,
          formattedTime: new Date(Date.now() - 600000).toLocaleTimeString(),
        },
      ]

      // Add sample alerts
      sampleAlerts.forEach(alert => window.addAlert(alert))
    } else {
      // Just update the UI with existing alerts
      updateRecentAlertsUI()
    }
  }

  // Update the recent alerts UI
  function updateRecentAlertsUI() {
    const alertsContainer = document.getElementById('recent-alerts-container')
    if (!alertsContainer) return

    // Clear container
    alertsContainer.innerHTML = ''

    // If no alerts, show message
    if (window.recentAlerts.length === 0) {
      const noAlertsMessage = document.createElement('div')
      noAlertsMessage.className = 'no-alerts-message'
      noAlertsMessage.textContent = 'No recent alerts'
      alertsContainer.appendChild(noAlertsMessage)
      return
    }

    // Create alert elements
    window.recentAlerts.forEach(alert => {
      const alertElement = document.createElement('div')
      alertElement.className = `alert-item ${alert.signalClass || ''}`

      // Create time element
      const timeElement = document.createElement('span')
      timeElement.className = 'alert-time'
      timeElement.textContent = alert.formattedTime

      // Create message element
      const messageElement = document.createElement('span')
      messageElement.className = 'alert-message'
      messageElement.textContent = alert.message

      // Add elements to alert
      alertElement.appendChild(timeElement)
      alertElement.appendChild(messageElement)

      // Add alert to container
      alertsContainer.appendChild(alertElement)
    })
  }

  // Initialize recent alerts when DOM is loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      // Create alerts container if it doesn't exist
      let alertsContainer = document.getElementById('recent-alerts-container')

      if (!alertsContainer) {
        // Find the logger container to place alerts before it
        const loggerContainer = document.querySelector('.logger-container')

        if (loggerContainer) {
          // Create alerts section
          const alertsSection = document.createElement('div')
          alertsSection.className = 'alerts-container'

          // Create header
          const header = document.createElement('div')
          header.className = 'section-header'
          header.innerHTML = '<h3>Recent Alerts</h3>'

          // Create container
          alertsContainer = document.createElement('div')
          alertsContainer.id = 'recent-alerts-container'
          alertsContainer.className = 'alerts-content'

          // Assemble section
          alertsSection.appendChild(header)
          alertsSection.appendChild(alertsContainer)

          // Insert before logger
          loggerContainer.parentNode.insertBefore(alertsSection, loggerContainer)

          // Add styles
          const style = document.createElement('style')
          style.textContent = `
            .alerts-container {
              margin-bottom: 1rem;
              border-radius: 5px;
              overflow: hidden;
              background: var(--secondary-bg);
              border: 1px solid var(--border-color);
            }
            
            .alerts-content {
              max-height: 200px;
              overflow-y: auto;
              padding: 0.5rem;
              background: rgba(0, 0, 0, 0.3);
            }
            
            .alert-item {
              padding: 0.5rem;
              margin-bottom: 0.5rem;
              border-radius: 4px;
              background: rgba(0, 0, 0, 0.5);
              border-left: 3px solid var(--text-color);
              display: flex;
              align-items: center;
            }
            
            .alert-item.degen-buy {
              border-left-color: #00FF00;
            }
            
            .alert-item.mild-buy {
              border-left-color: #0000FF;
            }
            
            .alert-item.mild-sell {
              border-left-color: #FFA500;
            }
            
            .alert-item.degen-sell {
              border-left-color: #FF0000;
            }
            
            .alert-time {
              font-family: 'Orbitron', sans-serif;
              font-size: 0.8rem;
              color: #888;
              margin-right: 0.5rem;
              min-width: 80px;
            }
            
            .alert-message {
              color: var(--text-color);
            }
            
            .no-alerts-message {
              text-align: center;
              padding: 1rem;
              color: #888;
              font-style: italic;
            }
          `
          document.head.appendChild(style)
        }
      }

      // Initialize alerts for current pair
      updateRecentAlerts(window.currentPair || 'xbtusdt')
    })
  } else {
    // DOM already loaded
    updateRecentAlerts(window.currentPair || 'xbtusdt')
  }

  // Listen for WebSocket messages to create alerts
  document.addEventListener('websocketMessage', (event) => {
    const data = event.detail

    // Check if message contains signal data
    if (data.type === 'indicatorUpdate' && data.indicator && data.signalClass) {
      // Create alert for significant signals
      if (data.signalClass === 'degen-buy' || data.signalClass === 'degen-sell') {
        window.addAlert({
          pair: data.pair || window.currentPair,
          type: 'signal',
          signalClass: data.signalClass,
          message: `${data.signalClass === 'degen-buy' ? 'Strong buy' : 'Strong sell'} signal detected for ${(data.pair || window.currentPair).toUpperCase()} on ${data.timeframe} timeframe`,
          indicator: data.indicator.toUpperCase(),
          value: data.value,
          timestamp: Date.now(),
          formattedTime: new Date().toLocaleTimeString(),
        })
      }
    }

    // Check for convergence alerts
    if (data.type === 'convergence' && data.indicators && data.indicators.length > 2) {
      window.addAlert({
        pair: data.pair || window.currentPair,
        type: 'convergence',
        signalClass: data.signalClass,
        message: `${data.indicators.length} indicator convergence for ${(data.pair || window.currentPair).toUpperCase()} on ${data.timeframe} timeframe`,
        indicators: data.indicators,
        timestamp: Date.now(),
        formattedTime: new Date().toLocaleTimeString(),
      })
    }
  })
})()
