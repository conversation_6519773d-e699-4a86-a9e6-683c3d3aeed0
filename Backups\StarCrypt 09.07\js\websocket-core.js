/**
 * Unified WebSocket Core - Combines connection management and message processing
 */
class WebSocketCore {
  /**
     * Create a new WebSocketCore instance
     * @param {Object} options - Configuration options
     * @param {string} options.url - WebSocket URL
     * @param {Array} [options.protocols=[]] - WebSocket protocols
     * @param {number} [options.maxBatchSize=10] - Maximum messages per batch
     * @param {number} [options.maxQueueSize=1000] - Max queue size before dropping
     * @param {number} [options.processDelay=10] - Delay between batches (ms)
     * @param {number} [options.maxConsecutiveErrors=10] - Max errors before pausing
     * @param {number} [options.errorResetTime=60000] - Time to reset error counter (ms)
     */
  /**
   * Process a single message
   * @param {Object} message - Message to process
   */
  processMessage = (message) => {
    if (!message || !message.type) {
      console.error('[WebSocket] Invalid message format: missing type');
      return;
    }

    if (this.currentlyProcessing.has(message._messageId)) {
      console.log(`[WebSocket] Message ${message._messageId} already being processed, skipping`);
      return;
    }

    this.currentlyProcessing.add(message._messageId);

    try {
      const handlers = this.messageHandlers.get(message.type);
      if (!handlers || handlers.size === 0) {
        console.debug(`[WebSocket] No handlers for message type: ${message.type}`);
        return;
      }

      handlers.forEach(handler => {
        if (typeof handler !== 'function') {
          console.error(`[WebSocket] Invalid handler for message type ${message.type}`);
          return;
        }

        try {
          // Using setTimeout to make handler execution async and non-blocking for the loop
          setTimeout(() => {
            try {
              handler(message);
            } catch (error) {
              console.error(`[WebSocket] Error in ${message.type} handler:`, {
                error,
                messageId: message._messageId,
                handler: handler.name || 'anonymous',
              });
              this.handleError(error); // Ensure handleError is defined and handles 'this'
            }
          }, 0);
        } catch (error) {
          console.error(`[WebSocket] Error scheduling handler for ${message.type}:`, error);
          this.handleError(error); // Ensure handleError is defined and handles 'this'
        }
      });
    } finally {
      this.currentlyProcessing.delete(message._messageId);
    }
  };

  constructor(options = {}) {
    // Connection settings
    this.url = options.url || `ws://${window.location.host}/ws`;
    this.protocols = options.protocols || [];
    this.ws = null;

    // Reconnection settings
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 10;
    this.reconnectInterval = options.reconnectInterval || 1000;
    this.maxReconnectInterval = options.maxReconnectInterval || 30000;
    this.connectionTimeout = options.connectionTimeout || 5000;
    this.shouldReconnect = true;

    // Message processing settings
    this.maxBatchSize = options.maxBatchSize || 10;
    this.maxQueueSize = options.maxQueueSize || 1000;
    this.processDelay = options.processDelay || 10;
    this.maxConsecutiveErrors = options.maxConsecutiveErrors || 10;
    this.errorResetTime = options.errorResetTime || 60000;

    // State
    this.messageQueue = [];
    this.messageHandlers = new Map();
    this.processedMessages = new Set();
    this.currentlyProcessing = new Set();
    this.isProcessing = false;
    this.isPaused = false;
    this.consecutiveErrors = 0;
    this.lastErrorTime = 0;

    // Connection state
    this.isConnected = false;
    this.isConnecting = false;
    this.messageTimestamps = new Map(); // Initialize messageTimestamps

    // Ping/pong
    this.pingInterval = options.pingInterval || 30000;
    this.pingTimeout = options.pingTimeout || 10000;
    this.lastPongTime = 0;
    this.pingTimer = null; // Timer for sending pings
    this.pongTimeoutTimer = null; // Timer for expecting pongs

    // Bind methods (processMessage binding removed)
    this.connect = this.connect.bind(this);
    this.disconnect = this.disconnect.bind(this);
    // this.send = this.send.bind(this); // Changed to arrow function, no explicit bind needed.
    // this.onMessage, this.onOpen, this.onClose, this.onError are now arrow functions, no explicit bind needed.
    // this.handlePing and this.handlePong are now arrow functions, no explicit bind needed.
    // this.checkConnection is now an arrow function, no explicit bind needed.
    // this.queueMessage is now an arrow function, no explicit bind needed.
    // this.processQueue is now an arrow function, no explicit bind needed.
    // this.processBatch is now an arrow function, no explicit bind needed.
    // this.processMessage = this.processMessage.bind(this); // Removed this line
    // addMessageHandler, removeMessageHandler, pauseProcessing, resumeProcessing, cleanup, and cleanupOldMessages are now arrow functions, no explicit bind needed.

    // Start cleanup interval
    this.cleanupInterval = setInterval(() => this.cleanupOldMessages(), 30000);
  }

  /**
     * Connect to the WebSocket server
     */
  connect() {
    if (this.isConnected || this.isConnecting) {
      console.log('[WebSocket] Already connected or connecting')
      return
    }

    this.isConnecting = true
    console.log(`[WebSocket] Connecting to ${this.url}...`)

    try {
      this.ws = new WebSocket(this.url, this.protocols)
      this.ws.binaryType = 'arraybuffer'

      // Set up event handlers
      this.ws.onopen = this.onOpen
      this.ws.onmessage = (event) => {
        if (event.data === 'PONG') {
          this.handlePong();
          return;
        }
        this.onMessage(event);
      };
      this.ws.onclose = this.onClose
      this.ws.onerror = this.onError

      // Set connection timeout
      this.connectionTimeoutId = setTimeout(() => {
        if (!this.isConnected) {
          console.warn('[WebSocket] Connection timeout')
          this.ws.close()
        }
      }, this.connectionTimeout)
    } catch (error) {
      console.error('[WebSocket] Error creating WebSocket:', error)
      this.isConnecting = false
      this.scheduleReconnect()
    }
  }

  /**
     * Disconnect from the WebSocket server
     */
  disconnect() {
    if (!this.ws) {
      console.log('[WebSocket] No WebSocket instance to disconnect')
      return
    }

    this.shouldReconnect = false
    this.clearTimers()

    try {
      if (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING) {
        this.ws.close(1000, 'User disconnected')
      }
    } catch (error) {
      console.error('[WebSocket] Error during disconnect:', error)
    }

    this.ws = null
    this.isConnected = false
    this.isConnecting = false
    this.messageHandlers.clear()
    this.messageQueue.length = 0
    this.currentlyProcessing.clear()
  }

  // WebSocket Event Handlers
  onOpen = (event) => {
    console.log('[WebSocket] Connection opened:', event);
    this.isConnected = true;
    this.isConnecting = false;
    this.reconnectAttempts = 0; // Reset reconnect attempts on successful connection
    clearTimeout(this.connectionTimeoutId);
    this.lastPongTime = Date.now(); // Initialize pong time
    this.startPing();

    // Process any messages that were queued while disconnected
    if (this.messageQueue.length > 0) {
      console.log(`[WebSocket] Processing ${this.messageQueue.length} queued messages.`);
      this.processQueue().catch(console.error);
    }
  }

  onClose = (event) => {
    console.log(`[WebSocket] Connection closed (code: ${event.code}, reason: ${event.reason}, clean: ${event.wasClean})`);
    this.isConnected = false;
    this.isConnecting = false;
    this.clearTimers(); // Clear ping/pong timers

    if (this.shouldReconnect && event.code !== 1000) { // Don't reconnect if explicitly closed (code 1000) or already reconnecting
      console.log('[WebSocket] Attempting to reconnect...');
      this.scheduleReconnect();
    } else {
      console.log('[WebSocket] Reconnect not attempted or explicit disconnect.');
    }
  }

  onError = (error) => {
    console.error('[WebSocket] Error:', error);
    // Note: The 'close' event will usually follow an error that terminates the connection.
    // If the WebSocket is still open, this might be a non-fatal error.
    // If it's fatal, onClose will handle reconnection logic.
    this.isConnecting = false; // Ensure we are not stuck in a connecting state
    // Consider if this.handleError(error) is needed for specific error counting/pausing logic.
  }

  // Ping/Pong and Connection Management
  clearTimers() {
    clearTimeout(this.connectionTimeoutId);
    clearTimeout(this.pingTimer);
    clearTimeout(this.pongTimeoutTimer);
    // If there was an old interval based ping, ensure it's cleared (legacy check)
    if (this.pingIntervalId) clearInterval(this.pingIntervalId);
  }

  startPing = () => {
    this.clearTimers(); // Clear existing timers before starting new ones
    this.pingTimer = setTimeout(() => {
      if (this.isConnected && this.ws && this.ws.readyState === WebSocket.OPEN) {
        try {
          console.debug('[WebSocket] Sending PING');
          this.ws.send('PING');
          this.pongTimeoutTimer = setTimeout(() => {
            if (Date.now() - this.lastPongTime > this.pingTimeout) {
              console.warn('[WebSocket] Pong timeout. Closing connection.');
              if (this.ws) this.ws.close(1006, 'Pong timeout'); // 1006: Abnormal Closure
            }
          }, this.pingTimeout);
        } catch (error) {
          console.error('[WebSocket] Error sending PING:', error);
          // this.handleError(error); // Decide if this error should count towards maxConsecutiveErrors
          if (this.ws) this.ws.close(1006, 'Ping send error');
        }
      } else {
        console.debug('[WebSocket] Cannot send PING, not connected or WebSocket not open.');
      }
    }, this.pingInterval);
  }

  handlePong = () => {
    console.debug('[WebSocket] PONG received');
    this.lastPongTime = Date.now();
    // Clear the immediate pong timeout, and schedule the next ping
    clearTimeout(this.pongTimeoutTimer);
    // Restart the ping cycle
    this.startPing();
  }

  // Method to initiate reconnection (assuming it exists or will be added if not)
  // scheduleReconnect() is called in onClose, ensure it's defined.
  // If not, a basic version:
  scheduleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const interval = Math.min(this.reconnectInterval * Math.pow(2, this.reconnectAttempts -1), this.maxReconnectInterval);
      console.log(`[WebSocket] Scheduling reconnect attempt ${this.reconnectAttempts} in ${interval}ms`);
      setTimeout(this.connect, interval);
    } else {
      console.error('[WebSocket] Max reconnect attempts reached.');
      this.shouldReconnect = false; // Stop trying
    }
  }


  /**
     * Send a message through WebSocket
     * @param {Object|string} message - Message to send
     */
  send = (message) => {
    if (!this.isConnected) {
      console.warn('[WebSocket] Not connected, queuing message')
      this.queueMessage(message)
      return
    }

    try {
      if (typeof message === 'object') {
        this.ws.send(JSON.stringify(message))
      } else {
        this.ws.send(message)
      }
    } catch (error) {
      console.error('[WebSocket] Error sending message:', error)
      this.handleError(error)
    }
  }

  /**
     * Handle incoming WebSocket message
     * @param {MessageEvent} event - WebSocket message event
     */
  onMessage = (event) => {
    const now = Date.now()

    // Throttle message processing
    if (now - this.lastMessageTime < this.messageThrottleTime) {
      return
    }

    // Check if we're already processing
    if (this.processingDepth > 0) {
      console.warn('[WebSocket] Message processing already in progress, queuing message')
      this.queueMessage(event.data)
      return
    }

    try {
      // Increment processing depth
      this.processingDepth++

      // Process message synchronously
      this.processMessageSync(event.data)
    } catch (error) {
      console.error('[WebSocket] Error processing message:', error)
      this.onError(error)
    } finally {
      // Decrement processing depth
      this.processingDepth = Math.max(0, this.processingDepth - 1)

      // Queue next message if available
      if (this.messageQueue.length > 0 && !this.isPaused) {
        setTimeout(() => {
          if (this.messageQueue.length > 0) {
            const data = this.messageQueue.shift()
            this.processMessageSync(data)
          }
        }, this.processingCooldown)
      }
    }
  }

  /**
     * Process message synchronously
     * @param {any} data - Message data
     */
  processMessageSync(data) {
    if (!this.isConnected || this.isPaused) {
      return
    }

    // Parse message
    let message
    try {
      message = JSON.parse(data)
    } catch (error) {
      console.error('[WebSocket] Invalid message format:', data)
      return
    }

    // Check if we already processed this message
    const messageId = message.id || JSON.stringify(message)
    if (this.processedMessages.has(messageId)) {
      return
    }
    this.processedMessages.add(messageId)

    // Get handlers for this message type
    const handlers = this.messageHandlers.get(message.type)
    if (!handlers || handlers.size === 0) {
      console.warn(`[WebSocket] No handlers for message type: ${message.type}`)
      return
    }

    // Process handlers synchronously
    for (const handler of handlers) {
      try {
        handler(message)
      } catch (error) {
        console.error(`[WebSocket] Handler error for type ${message.type}:`, error)
        this.onError(error)
      }
    }
  }

  /**
     * Queue a message for processing
     * @param {Object} message - Message to queue
     * @returns {string} - Message ID
     */
  queueMessage = (message) => {
    if (!message || typeof message !== 'object') {
      console.error('[WebSocket] Invalid message format')
      return null
    }

    // Generate unique ID if not provided
    if (!message._messageId) {
      message._messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }

    // Check if message was already processed
    if (this.processedMessages.has(message._messageId)) {
      return message._messageId
    }

    // Add timestamp
    message._timestamp = message._timestamp || Date.now()

    // Add to queue if not full
    if (this.messageQueue.length < this.maxQueueSize) {
      this.messageQueue.push(message)

      // Start processing if not already running
      if (!this.isProcessing && !this.isPaused) {
        this.processQueue().catch(console.error)
      }
    } else {
      console.warn('[WebSocket] Message queue full, dropping message:', message._messageId)
    }

    return message._messageId
  }

  /**
     * Process the message queue
     */
  processQueue = () => {
    // Check if we should process
    const now = Date.now()
    if (this.isProcessing ||
            this.isPaused ||
            this.messageQueue.length === 0 ||
            now - this.lastProcessTime < this.processingCooldown) {
      return
    }

    this.isProcessing = true
    this.lastProcessTime = now

    try {
      // Process messages one at a time with delay
      const message = this.messageQueue.shift()
      if (message) {
        this.processMessageSync(message)
      }
    } catch (error) {
      console.error('[WebSocket] Error processing queue:', error)
      this.handleError(error)
    } finally {
      this.isProcessing = false

      // Schedule next processing if more messages
      if (this.messageQueue.length > 0 && !this.isPaused) {
        setTimeout(() => this.processQueue(), this.processDelay)
      }
    }
  }

  /**
     * Process a batch of messages
     * @param {Array} batch - Batch of messages to process
     */
  processBatch = async (batch) => {
    for (const message of batch) {
      if (this.isPaused) break

      try {
        await this.processMessage(message)
        this.processedMessages.add(message._messageId)
        this.consecutiveErrors = 0
      } catch (error) {
        console.error('[WebSocket] Error processing message:', {
          error,
          messageId: message._messageId,
          type: message.type,
        })
        this.handleError(error)
      }
    }
  }

  processMessage = (message) => {
    if (!message || !message.type) {
      console.error('[WebSocket] Invalid message format: missing type')
      return
    }

    if (this.currentlyProcessing.has(message._messageId)) {
      console.log(`[WebSocket] Message ${message._messageId} already being processed, skipping`)
      return
    }

    this.currentlyProcessing.add(message._messageId)

    try {
      const handlers = this.messageHandlers.get(message.type)
      if (!handlers || handlers.size === 0) {
        console.debug(`[WebSocket] No handlers for message type: ${message.type}`)
        return
      }

      handlers.forEach(handler => {
        if (typeof handler !== 'function') {
          console.error(`[WebSocket] Invalid handler for message type ${message.type}`)
          return
        }

        try {
          setTimeout(() => {
            try {
              handler(message)
            } catch (error) {
              console.error(`[WebSocket] Error in ${message.type} handler:`, {
                error,
                messageId: message._messageId,
                handler: handler.name || 'anonymous',
              })
              this.handleError(error)
            }
          }, 0)
        } catch (error) {
          console.error(`[WebSocket] Error scheduling handler for ${message.type}:`, error)
          this.handleError(error)
        }
      })
    } finally {
      this.currentlyProcessing.delete(message._messageId)
      this.processedMessages.add(message._messageId)

      if (this.processedMessages.size > this.maxQueueSize * 2) {
        this.processedMessages.delete([...this.processedMessages][0])
      }
    }
  }

  /**
     * Add a message handler
     * @param {string} type - Message type to handle
     * @param {Function} handler - Handler function
     * @returns {Function} - Unsubscribe function
     */
  addMessageHandler = (type, handler) => {
    if (typeof handler !== 'function') {
      throw new Error('[WebSocket] Handler must be a function')
    }

    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, new Set())
    }

    this.messageHandlers.get(type).add(handler)

    return () => this.removeMessageHandler(type, handler)
  }

  /**
     * Remove a message handler
     * @param {string} type - Message type
     * @param {Function} handler - Handler to remove
     */
  removeMessageHandler = (type, handler) => {
    if (this.messageHandlers.has(type)) {
      const handlers = this.messageHandlers.get(type)
      handlers.delete(handler)

      if (handlers.size === 0) {
        this.messageHandlers.delete(type)
      }
    }
  }

  /**
     * Handle errors and update error state
     * @param {Error} error - Error to handle
     */
  handleError(error) {
    const now = Date.now()

    if (now - this.lastErrorTime > this.errorResetTime) {
      this.consecutiveErrors = 0
    }

    this.consecutiveErrors++
    this.lastErrorTime = now

    if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
      console.warn('[WebSocket] Too many consecutive errors, pausing processing')
      this.pauseProcessing()
    }
  }

  /**
     * Pause message processing
     * @param {number} [duration] - Duration in ms
     */
  pauseProcessing = (duration) => {
    this.isPaused = true

    if (duration > 0) {
      setTimeout(() => this.resumeProcessing(), duration)
    }
  }

  /**
     * Resume message processing
     */
  resumeProcessing = () => {
    if (this.isPaused) {
      this.isPaused = false
      this.consecutiveErrors = 0
      this.processQueue().catch(console.error)
    }
  }

  /**
     * Clean up resources
     */
  cleanup = () => {
    this.disconnect()
    clearInterval(this.cleanupInterval)
    this.messageQueue.length = 0
    this.messageHandlers.clear()
    this.processedMessages.clear()
    this.currentlyProcessing.clear()
  }

  /**
     * Clean up old messages
     */
  cleanupOldMessages = () => {
    const now = Date.now()
    const cutoff = now - 300000 // 5 minutes

    for (const [id, timestamp] of this.messageTimestamps) {
      if (timestamp < cutoff) {
        this.messageTimestamps.delete(id)
      }
    }
  }
}

// Export for CommonJS and browser
if (typeof module !== 'undefined' && module.exports) {
  module.exports = WebSocketCore
} else if (typeof window !== 'undefined') {
  window.WebSocketCore = WebSocketCore
}

// Initialize global instance
if (typeof window !== 'undefined') {
  window.wsCore = new WebSocketCore()
}
