/**
 * Indicator <PERSON><PERSON>dule
 * Manages the indicator selection menu and related functionality
 */

(function() {
    'use strict';
    
    // State
    let enabledIndicators = [];
    let allIndicators = [];
    
    /**
     * Initialize the indicator menu
     */
    function initializeIndicatorMenu() {
        // Get existing indicator lists from global variables if available
        if (window.INDICATORS) {
            allIndicators = [
                ...window.INDICATORS.momentum || [],
                ...window.INDICATORS.trend || [],
                ...window.INDICATORS.volume || [],
                ...window.INDICATORS.volatility || [],
                ...window.INDICATORS.advanced || []
            ];
        }
        
        // Get enabled indicators from strategy
        if (window.currentStrategy && window.TRADING_STRATEGIES) {
            const strategy = window.TRADING_STRATEGIES[window.currentStrategy];
            if (strategy && strategy.indicators) {
                enabledIndicators = [...strategy.indicators];
            }
        }
        
        // Ensure container exists with proper ID that matches menu-handler expectations
        ensureMenuContainerExists();
        renderIndicatorMenu();
        attachIndicatorMenuEventHandlers();
        
        console.log('Indicator menu initialized with', enabledIndicators.length, 'enabled indicators');
    }
    
    /**
     * Ensures the indicator menu container exists
     */
    function ensureMenuContainerExists() {
        let container = document.getElementById('indicatorMenu');
        
        if (!container) {
            console.log('Creating indicator menu container with ID: indicatorMenu');
            container = document.createElement('div');
            container.id = 'indicatorMenu';
            container.className = 'menu-content';
            // Start hidden
            container.style.display = 'none';
            container.style.position = 'absolute';
            container.style.top = '100%';
            container.style.right = '0';
            container.style.width = '280px';
            container.style.backgroundColor = '#1a1a2e';
            container.style.border = '1px solid #303045';
            container.style.zIndex = '1000';
            container.style.padding = '10px';
            container.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.5)';
            
            // Find the tickerContainer to append to
            const tickerContainer = document.getElementById('tickerContainer');
            if (tickerContainer) {
                tickerContainer.appendChild(container);
                console.log('Appended indicatorMenu container to tickerContainer');
            } else {
                // Fallback to body
                document.body.appendChild(container);
                console.log('Appended indicatorMenu container to body (fallback)');
            }
        }
        return container;
    }
    
    /**
     * Renders the indicator menu with checkboxes
     */
    function renderIndicatorMenu() {
        // Always ensure container exists
        const container = ensureMenuContainerExists();
        if (!container) {
            console.error('Failed to create indicator menu container');
            return;
        }
        
        // Group indicators by category
        const categories = {
            momentum: [],
            trend: [],
            volume: [],
            volatility: [],
            advanced: []
        };
        
        // Put indicators in their categories if window.INDICATORS exists
        if (window.INDICATORS) {
            Object.keys(window.INDICATORS).forEach(category => {
                if (window.INDICATORS[category] && Array.isArray(window.INDICATORS[category])) {
                    categories[category] = window.INDICATORS[category];
                }
            });
        }
        
        // Create HTML content
        let html = '<h3>Indicator Selection</h3>';
        html += '<div class="indicator-sections">';
        
        // Generate sections for each category
        Object.keys(categories).forEach(category => {
            if (categories[category].length > 0) {
                html += `
                    <div class="indicator-section">
                        <h4>${capitalize(category)} Indicators</h4>
                        <div class="indicator-group">
                `;
                
                // Add checkboxes for each indicator in this category
                categories[category].forEach(indicator => {
                    const isChecked = enabledIndicators.includes(indicator);
                    html += `
                        <div class="indicator-checkbox">
                            <label>
                                <input type="checkbox" class="indicator-toggle" 
                                       data-indicator="${indicator}" 
                                       ${isChecked ? 'checked' : ''}>
                                ${getIndicatorDisplayName(indicator)}
                            </label>
                            <span class="indicator-info" data-indicator="${indicator}">ⓘ</span>
                        </div>
                    `;
                });
                
                html += `
                        </div>
                    </div>
                `;
            }
        });
        
        html += '</div>';
        html += `
            <div class="indicator-actions">
                <button id="applyIndicatorsButton" class="apply-indicators-button">Apply Indicators</button>
                <button id="resetIndicatorsButton" class="reset-indicators-button">Reset to Default</button>
            </div>
        `;
        
        container.innerHTML = html;
    }
    
    /**
     * Attach event handlers to the indicator menu elements
     */
    function attachIndicatorMenuEventHandlers() {
        const container = document.getElementById('indicatorMenu');
        if (!container) return;
        
        // Toggle indicators when checkboxes are clicked
        const checkboxes = container.querySelectorAll('.indicator-toggle');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const indicator = this.dataset.indicator;
                if (this.checked) {
                    if (!enabledIndicators.includes(indicator)) {
                        enabledIndicators.push(indicator);
                    }
                } else {
                    enabledIndicators = enabledIndicators.filter(ind => ind !== indicator);
                }
            });
        });
        
        // Show indicator info tooltip
        const infoIcons = container.querySelectorAll('.indicator-info');
        infoIcons.forEach(icon => {
            icon.addEventListener('click', function() {
                const indicator = this.dataset.indicator;
                showIndicatorInfo(indicator);
            });
        });
        
        // Apply button handler
        const applyButton = document.getElementById('applyIndicatorsButton');
        if (applyButton) {
            applyButton.addEventListener('click', function() {
                applyIndicatorSelection();
            });
        }
        
        // Reset button handler
        const resetButton = document.getElementById('resetIndicatorsButton');
        if (resetButton) {
            resetButton.addEventListener('click', function() {
                resetIndicatorsToDefault();
            });
        }
    }
    
    /**
     * Apply the selected indicators
     */
    function applyIndicatorSelection() {
        console.log('Applying indicator selection:', enabledIndicators);
        
        // Save to local storage
        localStorage.setItem('enabledIndicators', JSON.stringify(enabledIndicators));
        
        // Update UI
        if (typeof updateEnabledIndicatorsForStrategy === 'function') {
            updateEnabledIndicatorsForStrategy(window.currentStrategy);
        }
        
        // Update signal matrix
        if (typeof updateSignalMatrix === 'function') {
            updateSignalMatrix();
        }
        
        // Notify user
        showNotification('Indicator selection applied');
    }
    
    /**
     * Reset indicators to default based on current strategy
     */
    function resetIndicatorsToDefault() {
        if (window.currentStrategy && window.TRADING_STRATEGIES) {
            const strategy = window.TRADING_STRATEGIES[window.currentStrategy];
            if (strategy && strategy.indicators) {
                enabledIndicators = [...strategy.indicators];
                
                // Update checkboxes to reflect reset
                const checkboxes = document.querySelectorAll('.indicator-toggle');
                checkboxes.forEach(checkbox => {
                    const indicator = checkbox.dataset.indicator;
                    checkbox.checked = enabledIndicators.includes(indicator);
                });
                
                showNotification('Indicators reset to strategy defaults');
            }
        }
    }
    
    /**
     * Show detailed information about an indicator
     */
    function showIndicatorInfo(indicator) {
        let description = getIndicatorDescription(indicator);
        let detailedInfo = getIndicatorDetailedInfo(indicator);
        
        const modal = document.createElement('div');
        modal.className = 'indicator-modal';
        modal.innerHTML = `
            <div class="indicator-modal-content">
                <span class="indicator-modal-close">&times;</span>
                <h3>${getIndicatorDisplayName(indicator)}</h3>
                <p class="indicator-description">${description}</p>
                <div class="indicator-details">${detailedInfo}</div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Close button functionality
        const closeButton = modal.querySelector('.indicator-modal-close');
        closeButton.addEventListener('click', function() {
            modal.remove();
        });
        
        // Close when clicking outside the modal
        window.addEventListener('click', function(event) {
            if (event.target === modal) {
                modal.remove();
            }
        });
    }
    
    /**
     * Get a user-friendly display name for an indicator
     */
    function getIndicatorDisplayName(indicator) {
        const nameMap = {
            'rsi': 'RSI',
            'stochRsi': 'Stochastic RSI',
            'macd': 'MACD',
            'bollingerBands': 'Bollinger Bands',
            'adx': 'ADX',
            'atr': 'ATR',
            'williamsR': 'Williams %R',
            'ultimateOscillator': 'Ultimate Oscillator',
            'mfi': 'Money Flow Index',
            'vwap': 'VWAP',
            'volume': 'Volume',
            'fractal': 'Fractals',
            'ml': 'ML Predictor',
            'sentiment': 'Sentiment',
            'entropy': 'Entropy',
            'correlation': 'Correlation',
            'time_anomaly': 'Time Anomaly'
        };
        
        return nameMap[indicator] || indicator;
    }
    
    /**
     * Show a temporary notification
     */
    function showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'system-notification';
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
    
    /**
     * Capitalize first letter of a string
     */
    function capitalize(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
    }
    
    /**
     * Get description for an indicator
     */
    function getIndicatorDescription(indicator) {
        // Use global function if available
        if (typeof window.getIndicatorDescription === 'function') {
            return window.getIndicatorDescription(indicator);
        }
        
        const descriptions = {
            'rsi': 'Relative Strength Index measures momentum by comparing the magnitude of recent gains to recent losses.',
            'stochRsi': 'Stochastic RSI applies the Stochastic oscillator formula to RSI values instead of price.',
            'macd': 'Moving Average Convergence Divergence shows the relationship between two moving averages of a security\'s price.',
            'bollingerBands': 'Bollinger Bands measure deviation from a simple moving average, expanding and contracting with volatility.',
            'adx': 'Average Directional Index measures the strength of a trend regardless of direction.',
            'atr': 'Average True Range measures market volatility.',
            'williamsR': 'Williams %R is a momentum indicator measuring overbought/oversold levels.',
            'ultimateOscillator': 'Ultimate Oscillator uses multiple timeframes to avoid false signals.',
            'mfi': 'Money Flow Index measures buying and selling pressure using price and volume.',
            'vwap': 'Volume Weighted Average Price shows the average price weighted by volume.',
            'volume': 'Trading volume represents the total number of shares or contracts traded.',
            'fractal': 'Fractals identify potential reversal points in the market.',
            'ml': 'Machine Learning Predictor uses AI to forecast price movements.',
            'sentiment': 'Market sentiment analysis from social media and news sources.',
            'entropy': 'Measures market randomness and predictability.',
            'correlation': 'Inter-market correlation between different assets.',
            'time_anomaly': 'Identifies unusual patterns in time-series data.'
        };
        
        return descriptions[indicator] || 'No description available.';
    }
    
    /**
     * Get detailed technical information about an indicator
     */
    function getIndicatorDetailedInfo(indicator) {
        // Use global function if available
        if (typeof window.getIndicatorDetailedInfo === 'function') {
            return window.getIndicatorDetailedInfo(indicator);
        }
        
        const details = {
            'rsi': 'RSI is calculated using the formula: RSI = 100 - (100 / (1 + RS)), where RS = Average Gain / Average Loss over a specified period, typically 14 periods.',
            'macd': 'MACD is calculated by subtracting the 26-period EMA from the 12-period EMA. The 9-period EMA of the MACD is called the "signal line."',
            'bollingerBands': 'Bollinger Bands consist of a middle band (20-period SMA) and two outer bands placed 2 standard deviations above and below the middle band.'
        };
        
        return details[indicator] || 'No detailed information available.';
    }
    
    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Delay initialization slightly to ensure containers are ready
        setTimeout(initializeIndicatorMenu, 600);
    });
    
    // Export public functions to global scope
    window.renderIndicatorMenu = renderIndicatorMenu;
    window.getIndicatorDescription = window.getIndicatorDescription || getIndicatorDescription;
    window.getIndicatorDetailedInfo = window.getIndicatorDetailedInfo || getIndicatorDetailedInfo;
    
})();
