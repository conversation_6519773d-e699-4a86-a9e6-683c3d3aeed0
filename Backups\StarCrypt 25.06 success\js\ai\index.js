// Main entry point for AI module
const AIEngine = require('./ai-engine')
const StrategyManager = require('./strategy-manager')
const MarketData = require('./market-data')
const Backtester = require('./backtester')

// Initialize AI components
async function initializeAI() {
  try {
    console.log('Initializing AI components...')

    // Initialize AI Engine (TensorFlow.js)
    await AIEngine.initialize()

    // Initialize Strategy Manager
    await StrategyManager.initialize()

    console.log('AI components initialized successfully')
    return true
  } catch (error) {
    console.error('Failed to initialize AI components:', error)
    throw error
  }
}

// Export API
module.exports = {
  // Core components
  AIEngine,
  StrategyManager,
  MarketData,
  Backtester,

  // Initialization
  initializeAI,

  // High-level API
  async getMarketAnalysis(pair, timeframe) {
    try {
      const data = await MarketData.getProcessedData(pair, timeframe)
      const signals = await StrategyManager.getAllSignals(data)
      const consensus = await StrategyManager.getConsensusSignal(data)

      return {
        timestamp: new Date().toISOString(),
        pair,
        timeframe,
        currentPrice: data.closes[data.closes.length - 1],
        indicators: data.indicators,
        signals,
        consensus,
        marketRegime: data.indicators.regime,
      }
    } catch (error) {
      console.error(`Error getting market analysis for ${pair} ${timeframe}:`, error)
      throw error
    }
  },

  async getTradingSignal(pair, timeframe, strategyId = null) {
    try {
      const data = await MarketData.getProcessedData(pair, timeframe)

      if (strategyId) {
        // Get signal from specific strategy
        return await StrategyManager.getStrategySignal(strategyId, data)
      }
      // Get consensus signal from all strategies
      return await StrategyManager.getConsensusSignal(data)
    } catch (error) {
      console.error(`Error getting trading signal for ${pair} ${timeframe}:`, error)
      throw error
    }
  },

  async runBacktest(strategy, pair, timeframe, startDate, endDate) {
    try {
      console.log(`Starting backtest for ${pair} ${timeframe} from ${startDate} to ${endDate}`)

      // Load and prepare strategy
      const strategyConfig = await StrategyManager.loadStrategy(strategy)

      // Run backtest
      const result = await Backtester.runBacktest(
        strategyConfig,
        pair,
        timeframe,
        startDate,
        endDate,
      )

      // Generate and log report
      const report = Backtester.generateReport()
      console.log(`\n${report}`)

      // Save results
      const filename = `backtest_${strategy}_${pair}_${timeframe}_${new Date().toISOString().replace(/[:.]/g, '-')}.json`
      await Backtester.saveResults(filename)

      return {
        success: true,
        result,
        report,
      }
    } catch (error) {
      console.error('Backtest failed:', error)
      return {
        success: false,
        error: error.message,
      }
    }
  },

  // Model management
  async trainModel(strategy, trainingData) {
    try {
      console.log(`Training model for strategy: ${strategy}`)

      // Load strategy
      const strategyConfig = await StrategyManager.loadStrategy(strategy)

      // Train model
      const result = await StrategyManager.trainAIModels(strategyConfig, trainingData)

      console.log(`Model training completed for ${strategy}`)
      return {
        success: true,
        strategy,
        result,
      }
    } catch (error) {
      console.error(`Error training model for ${strategy}:`, error)
      return {
        success: false,
        strategy,
        error: error.message,
      }
    }
  },

  // Market data utilities
  async getTechnicalAnalysis(pair, timeframe) {
    try {
      const data = await MarketData.getProcessedData(pair, timeframe)
      return {
        timestamp: new Date().toISOString(),
        pair,
        timeframe,
        currentPrice: data.closes[data.closes.length - 1],
        indicators: data.indicators,
      }
    } catch (error) {
      console.error(`Error getting technical analysis for ${pair} ${timeframe}:`, error)
      throw error
    }
  },

  // Strategy management
  async addStrategy(strategyConfig) {
    try {
      // Validate strategy config
      if (!strategyConfig.id || !strategyConfig.name || !strategyConfig.logic) {
        throw new Error('Invalid strategy configuration: missing required fields')
      }

      // Add to strategy manager
      await StrategyManager.addStrategy(strategyConfig)

      console.log(`Added new strategy: ${strategyConfig.name} (${strategyConfig.id})`)
      return {
        success: true,
        strategyId: strategyConfig.id,
      }
    } catch (error) {
      console.error('Error adding strategy:', error)
      return {
        success: false,
        error: error.message,
      }
    }
  },

  // Get all available strategies
  async listStrategies() {
    return StrategyManager.listStrategies()
  },
}

// Auto-initialize when imported
initializeAI().catch(console.error)
