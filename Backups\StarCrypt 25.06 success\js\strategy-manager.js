/**
 * Unified Strategy Manager - Handles strategy selection and timeframe management
 */
class StrategyManager {
  constructor() {
    // Strategy state
    this.strategies = window.TRADING_STRATEGIES
    this.currentStrategy = null
    this.strategyContainer = null
    this.strategyMenu = null
    this.isHandlingStrategyChange = false // Flag to prevent recursive event handling

    // Timeframe state
    this.timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']
    this.activeTimeframes = [...this.timeframes]
    this.currentTimeframe = '1h'

    // UI elements
    this.timeframeContainer = null
    this.timeframeButtons = null

    // WebSocket and Signal references
    this.signalManager = window.StarCrypt?.SignalManager
    this.webSocketProcessor = window.StarCrypt?.WebSocketProcessor

    // Initialization is now handled by the DOMContentLoaded listener
  }

  initialize() {
    // Cache elements
    this.strategyContainer = document.getElementById('strategyControls')
    this.strategyMenu = document.getElementById('strategyMenu')
    this.timeframeContainer = document.getElementById('timeframeControls')

    if (!this.strategyContainer || !this.strategyMenu || !this.timeframeContainer) {
      console.error('[StrategyManager] Required elements not found')
      return
    }

    // Load saved state
    this.loadSavedState()

    // Setup event listeners
    this.setupEventListeners()

    // Render UI
    this.renderStrategyMenu()
    this.renderTimeframeControls()

    // Initialize WebSocket handlers
    this.initializeWebSocketHandlers()

    console.log('[StrategyManager] Initialized')
  }

  setupEventListeners() {
    // Strategy menu toggle
    document.addEventListener('click', (e) => {
      if (e.target.id === 'strategyButton') {
        this.toggleStrategyMenu()
      }
    })

    // Timeframe selection
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('timeframe-btn')) {
        const timeframe = e.target.dataset.timeframe
        if (timeframe) {
          this.setTimeframe(timeframe)
        }
      }
    })

    // Strategy change handler - only process if the event didn't originate from this manager
    document.addEventListener('strategyChanged', (e) => {
      if (e.detail.source !== 'strategyManager') {
        this.handleStrategyChange(e.detail.strategyId, true)
      }
    })

    // WebSocket connection handler
    document.addEventListener('websocketStatus', (e) => {
      if (e.detail.connected) {
        console.log('[StrategyManager] WebSocket connected, updating state')
        this.updateSignalManagerTimeframe()
      }
    })
  }

  initializeWebSocketHandlers() {
    if (!this.webSocketProcessor) {
      console.warn('[StrategyManager] WebSocketProcessor not available')
      return
    }

    // Listen for connection status
    this.webSocketProcessor.addEventListener('status', (status) => {
      if (status === 'connected') {
        this.updateSignalManagerTimeframe()
      }
    })
  }

  loadSavedState() {
    // Load saved strategy
    const savedStrategy = localStorage.getItem('currentStrategy') || 'admiral_toa'
    if (this.strategies[savedStrategy]) {
      this.currentStrategy = savedStrategy
    } else {
      console.warn(`Saved strategy ${savedStrategy} not found, using default`)
      this.currentStrategy = 'admiral_toa'
    }

    // Load saved timeframe
    const savedTimeframe = localStorage.getItem('currentTimeframe') || '1h'
    if (this.timeframes.includes(savedTimeframe)) {
      this.currentTimeframe = savedTimeframe
    }
  }

  renderStrategyMenu() {
    if (!this.strategyMenu) return

    const strategyOptions = Object.entries(this.strategies).map(([id, strategy]) => `
            <option value="${id}" ${id === this.currentStrategy ? 'selected' : ''}>
                ${strategy.name}
            </option>
        `).join('')

    this.strategyMenu.innerHTML = `
            <div class="strategy-controls">
                <h3>Strategy Selector</h3>
                <select id="strategySelect" onchange="window.StrategyManager.handleStrategyChange(this.value)">
                    ${strategyOptions}
                </select>
                <div class="strategy-description" id="strategyDescription">
                    ${this.getStrategyDescription(this.currentStrategy)}
                </div>
                <div id="strategyName" style="font-weight: bold; margin-top: 10px;"></div>
                <div id="strategyHelperText" style="font-style: italic; margin-bottom: 10px;"></div>
                <div id="currentStrategyName" style="display: none;"></div>
                <div id="strategyIndicatorsContent" style="margin-top: 10px;"></div>
            </div>
        `
  }

  renderTimeframeControls() {
    if (!this.timeframeContainer) return

    const buttons = this.timeframes.map(timeframe => `
            <button class="timeframe-btn ${this.currentTimeframe === timeframe ? 'active' : ''}"
                    data-timeframe="${timeframe}">
                ${timeframe.toUpperCase()}
            </button>
        `).join('')

    this.timeframeContainer.innerHTML = `
            <div class="timeframe-selector">
                <div class="timeframe-buttons">${buttons}</div>
            </div>
        `
  }

  toggleStrategyMenu() {
    if (this.strategyMenu) {
      this.strategyMenu.classList.toggle('open')
    }
  }

  handleStrategyChange(strategyId, isInternalChange = false) {
    if (!this.strategies[strategyId] || this.currentStrategy === strategyId) return

    // Prevent re-entrancy
    if (this.isHandlingStrategyChange) return
    this.isHandlingStrategyChange = true

    try {
      this.currentStrategy = strategyId
      localStorage.setItem('currentStrategy', strategyId)

      // Update UI
      this.renderStrategyMenu()
      this.updateStrategyDescription()

      // Update indicators
      this.updateIndicatorsForStrategy(strategyId)

      // Only notify if this wasn't triggered by our own notification
      if (!isInternalChange) {
        this.notifyStrategyChange(strategyId)
      }
    } finally {
      this.isHandlingStrategyChange = false
    }
  }

  updateStrategyDescription() {
    const description = this.getStrategyDescription(this.currentStrategy)
    const descElement = document.querySelector('.strategy-description')
    if (descElement) {
      descElement.innerHTML = description
    }
  }

  getStrategyDescription(strategyId) {
    const strategy = this.strategies[strategyId]
    if (!strategy) return ''

    return `
            <h4>${strategy.name}</h4>
            <p>${strategy.description}</p>
            ${strategy.helperText || ''}
        `
  }

  setTimeframe(timeframe) {
    if (!this.timeframes.includes(timeframe)) return

    this.currentTimeframe = timeframe
    localStorage.setItem('currentTimeframe', timeframe)

    // Update UI
    this.renderTimeframeControls()

    // Update signal manager
    this.updateSignalManagerTimeframe()

    // Dispatch event
    this.dispatchEvent('timeframeChanged', { timeframe })
  }

  updateSignalManagerTimeframe() {
    if (this.signalManager) {
      this.signalManager.setCurrentTimeframe(this.currentTimeframe)
      this.signalManager.updateAllSignalLights()
    }
  }

  updateIndicatorsForStrategy(strategyId) {
    const strategy = this.strategies[strategyId]
    if (!strategy) return

    // Update indicators based on strategy
    const indicators = this.getStrategySpecificIndicators(strategy)
    this.updateIndicatorMenu(indicators)
  }

  getStrategySpecificIndicators(strategy) {
    return strategy.indicators || []
  }

  updateIndicatorMenu(indicators) {
    const menu = document.getElementById('indicatorMenu')
    if (!menu) return

    menu.innerHTML = this.createIndicatorCheckboxes(menu, indicators)
    this.attachIndicatorMenuHandler()
  }

  createIndicatorCheckboxes(menu, indicators) {
    return indicators.map(indicator => `
            <label>
                <input type="checkbox" name="indicator" value="${indicator}" checked>
                ${indicator}
            </label>
        `).join('')
  }

  attachIndicatorMenuHandler() {
    document.querySelectorAll('input[name="indicator"]').forEach(input => {
      input.addEventListener('change', () => {
        this.handleIndicatorChange(input.value, input.checked)
      })
    })
  }

  handleIndicatorChange(indicator, checked) {
    // Update indicator state
    this.updateIndicatorState(indicator, checked)

    // Update signal manager
    this.updateSignalManagerIndicators()
  }

  updateIndicatorState(indicator, checked) {
    // Store indicator state
    const indicatorState = JSON.parse(localStorage.getItem('indicatorState') || '{}')
    indicatorState[indicator] = checked
    localStorage.setItem('indicatorState', JSON.stringify(indicatorState))
  }

  updateSignalManagerIndicators() {
    if (this.signalManager) {
      const indicatorState = JSON.parse(localStorage.getItem('indicatorState') || '{}')
      this.signalManager.updateIndicators(indicatorState)
    }
  }

  notifyStrategyChange(strategyId) {
    if (this.isHandlingStrategyChange) return
    
    try {
      this.isHandlingStrategyChange = true
      const event = new CustomEvent('strategyChanged', {
        detail: { 
          strategyId,
          source: 'strategyManager'
        },
      })
      document.dispatchEvent(event)
    } finally {
      this.isHandlingStrategyChange = false
    }
  }

  dispatchEvent(type, detail) {
    const event = new CustomEvent(`strategyManager:${type}`, {
      detail,
    })
    document.dispatchEvent(event)
  }
}

// Create and expose singleton instance
const strategyManager = new StrategyManager()
window.StrategyManager = strategyManager

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  strategyManager.initialize()
})
