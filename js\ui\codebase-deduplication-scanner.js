/**
 * Codebase Deduplication Scanner for StarCrypt
 * Identifies and eliminates redundant code, event handlers, and WebSocket connections
 */

class CodebaseDeduplicationScanner {
  constructor() {
    this.duplicateHandlers = new Map();
    this.duplicateElements = new Map();
    this.duplicateWebSockets = new Set();
    this.duplicateEventListeners = new Map();
    this.performanceMetrics = {
      handlersRemoved: 0,
      elementsRemoved: 0,
      listenersRemoved: 0,
      websocketsRemoved: 0
    };
    
    this.init();
  }

  init() {
    console.log('[CodebaseDeduplicationScanner] Starting comprehensive codebase scan...');
    
    try {
      // Scan for different types of duplications
      this.scanWebSocketDuplicates();
      this.scanEventListenerDuplicates();
      this.scanDOMElementDuplicates();
      this.scanFunctionDuplicates();
      this.scanIndicatorProcessorDuplicates();
      
      // Apply fixes
      this.applyDuplicationFixes();
      
      // Report results
      this.reportResults();
      
      console.log('[CodebaseDeduplicationScanner] Codebase scan completed successfully');
    } catch (error) {
      console.error('[CodebaseDeduplicationScanner] Error during codebase scan:', error);
    }
  }

  scanWebSocketDuplicates() {
    console.log('[CodebaseDeduplicationScanner] Scanning for WebSocket duplicates...');
    
    // Check for multiple WebSocket instances
    const wsInstances = [];
    
    if (window.ws) {
      wsInstances.push({ name: 'window.ws', instance: window.ws });
    }
    
    if (window.websocket) {
      wsInstances.push({ name: 'window.websocket', instance: window.websocket });
    }
    
    if (window.socket) {
      wsInstances.push({ name: 'window.socket', instance: window.socket });
    }
    
    // Check for WebSocket handlers in different files
    const wsHandlerFiles = [
      'js/ui/websocket-handler.js',
      'js/ui/indicator-processors.js',
      'js/websocket-processor-fixed.js'
    ];
    
    wsHandlerFiles.forEach(file => {
      if (document.querySelector(`script[src="${file}"]`)) {
        this.duplicateWebSockets.add(file);
      }
    });
    
    console.log(`[CodebaseDeduplicationScanner] Found ${wsInstances.length} WebSocket instances and ${this.duplicateWebSockets.size} handler files`);
  }

  scanEventListenerDuplicates() {
    console.log('[CodebaseDeduplicationScanner] Scanning for event listener duplicates...');
    
    // Find elements with multiple event listeners
    const elementsWithListeners = document.querySelectorAll('*');
    
    elementsWithListeners.forEach(element => {
      const listeners = this.getEventListeners(element);
      if (listeners.length > 1) {
        const key = this.getElementKey(element);
        this.duplicateEventListeners.set(key, {
          element: element,
          listenerCount: listeners.length,
          listeners: listeners
        });
      }
    });
    
    console.log(`[CodebaseDeduplicationScanner] Found ${this.duplicateEventListeners.size} elements with duplicate event listeners`);
  }

  scanDOMElementDuplicates() {
    console.log('[CodebaseDeduplicationScanner] Scanning for DOM element duplicates...');
    
    // Scan for duplicate signal circles
    const signalCircles = document.querySelectorAll('.signal-circle, .circle');
    const signalMap = new Map();
    
    signalCircles.forEach(circle => {
      const key = `${circle.dataset.ind || 'unknown'}-${circle.dataset.tf || 'unknown'}`;
      if (signalMap.has(key)) {
        signalMap.get(key).push(circle);
      } else {
        signalMap.set(key, [circle]);
      }
    });
    
    // Find duplicates
    signalMap.forEach((circles, key) => {
      if (circles.length > 1) {
        this.duplicateElements.set(`signal-${key}`, circles.slice(1)); // Keep first, mark rest as duplicates
      }
    });
    
    // Scan for duplicate menu boxes
    const menuBoxes = document.querySelectorAll('.menu-box');
    const menuMap = new Map();
    
    menuBoxes.forEach(menu => {
      if (menu.id) {
        if (menuMap.has(menu.id)) {
          menuMap.get(menu.id).push(menu);
        } else {
          menuMap.set(menu.id, [menu]);
        }
      }
    });
    
    menuMap.forEach((menus, id) => {
      if (menus.length > 1) {
        this.duplicateElements.set(`menu-${id}`, menus.slice(1));
      }
    });
    
    console.log(`[CodebaseDeduplicationScanner] Found ${this.duplicateElements.size} sets of duplicate DOM elements`);
  }

  scanFunctionDuplicates() {
    console.log('[CodebaseDeduplicationScanner] Scanning for function duplicates...');
    
    // Check for duplicate global functions
    const globalFunctions = [
      'updateSignalLights',
      'processIndicatorUpdates',
      'updateIndicatorDisplay',
      'handleWebSocketMessage'
    ];
    
    globalFunctions.forEach(funcName => {
      const implementations = this.findFunctionImplementations(funcName);
      if (implementations.length > 1) {
        this.duplicateHandlers.set(funcName, implementations);
      }
    });
    
    console.log(`[CodebaseDeduplicationScanner] Found ${this.duplicateHandlers.size} duplicate function implementations`);
  }

  scanIndicatorProcessorDuplicates() {
    console.log('[CodebaseDeduplicationScanner] Scanning for indicator processor duplicates...');
    
    // Check for multiple indicator processing systems
    const processorSystems = [
      'window.indicatorProcessor',
      'window.indicatorProcessors',
      'window.IndicatorProcessor'
    ];
    
    let activeProcessors = 0;
    processorSystems.forEach(processor => {
      if (this.getNestedProperty(window, processor)) {
        activeProcessors++;
      }
    });
    
    if (activeProcessors > 1) {
      console.warn(`[CodebaseDeduplicationScanner] Found ${activeProcessors} active indicator processors - potential conflict`);
    }
  }

  applyDuplicationFixes() {
    console.log('[CodebaseDeduplicationScanner] Applying deduplication fixes...');
    
    // Fix WebSocket duplicates
    this.fixWebSocketDuplicates();
    
    // Fix event listener duplicates
    this.fixEventListenerDuplicates();
    
    // Fix DOM element duplicates
    this.fixDOMElementDuplicates();
    
    // Fix function duplicates
    this.fixFunctionDuplicates();
  }

  fixWebSocketDuplicates() {
    console.log('[CodebaseDeduplicationScanner] Fixing WebSocket duplicates...');
    
    // Keep only window.ws, remove others
    if (window.websocket && window.websocket !== window.ws) {
      try {
        if (window.websocket.close) {
          window.websocket.close();
        }
        delete window.websocket;
        this.performanceMetrics.websocketsRemoved++;
        console.log('[CodebaseDeduplicationScanner] Removed duplicate window.websocket');
      } catch (e) {
        console.warn('[CodebaseDeduplicationScanner] Error removing window.websocket:', e);
      }
    }
    
    if (window.socket && window.socket !== window.ws) {
      try {
        if (window.socket.close) {
          window.socket.close();
        }
        delete window.socket;
        this.performanceMetrics.websocketsRemoved++;
        console.log('[CodebaseDeduplicationScanner] Removed duplicate window.socket');
      } catch (e) {
        console.warn('[CodebaseDeduplicationScanner] Error removing window.socket:', e);
      }
    }
  }

  fixEventListenerDuplicates() {
    console.log('[CodebaseDeduplicationScanner] Fixing event listener duplicates...');
    
    this.duplicateEventListeners.forEach((data, key) => {
      const { element, listenerCount } = data;
      
      // Clone element to remove all listeners
      const newElement = element.cloneNode(true);
      element.parentNode.replaceChild(newElement, element);
      
      // Add single appropriate listener based on element type
      this.addSingleEventListener(newElement);
      
      this.performanceMetrics.listenersRemoved += (listenerCount - 1);
    });
    
    console.log(`[CodebaseDeduplicationScanner] Fixed ${this.duplicateEventListeners.size} elements with duplicate listeners`);
  }

  fixDOMElementDuplicates() {
    console.log('[CodebaseDeduplicationScanner] Fixing DOM element duplicates...');
    
    this.duplicateElements.forEach((elements, key) => {
      elements.forEach(element => {
        if (element.parentNode) {
          element.parentNode.removeChild(element);
          this.performanceMetrics.elementsRemoved++;
        }
      });
    });
    
    console.log(`[CodebaseDeduplicationScanner] Removed ${this.performanceMetrics.elementsRemoved} duplicate DOM elements`);
  }

  fixFunctionDuplicates() {
    console.log('[CodebaseDeduplicationScanner] Fixing function duplicates...');
    
    this.duplicateHandlers.forEach((implementations, funcName) => {
      // Keep the most recent implementation (usually the last one)
      const keepImplementation = implementations[implementations.length - 1];
      
      // Remove older implementations
      for (let i = 0; i < implementations.length - 1; i++) {
        try {
          delete implementations[i];
          this.performanceMetrics.handlersRemoved++;
        } catch (e) {
          console.warn(`[CodebaseDeduplicationScanner] Error removing duplicate ${funcName}:`, e);
        }
      }
    });
    
    console.log(`[CodebaseDeduplicationScanner] Fixed ${this.duplicateHandlers.size} duplicate function implementations`);
  }

  // Helper methods
  getEventListeners(element) {
    // This is a simplified check - in reality, we can't easily get all listeners
    const listeners = [];
    
    // Check for common event attributes
    const eventAttrs = ['onclick', 'onmouseenter', 'onmouseleave', 'onchange'];
    eventAttrs.forEach(attr => {
      if (element[attr]) {
        listeners.push(attr);
      }
    });
    
    // Check for data attributes that indicate listeners
    if (element.dataset.hasClickListener) {
      listeners.push('click-listener');
    }
    
    return listeners;
  }

  getElementKey(element) {
    return `${element.tagName}-${element.className}-${element.id || 'no-id'}`;
  }

  findFunctionImplementations(funcName) {
    const implementations = [];
    
    // Check global scope
    if (window[funcName]) {
      implementations.push(window[funcName]);
    }
    
    // Check for function definitions in script tags (simplified)
    const scripts = document.querySelectorAll('script');
    scripts.forEach(script => {
      if (script.textContent && script.textContent.includes(`function ${funcName}`)) {
        implementations.push(`script-${funcName}`);
      }
    });
    
    return implementations;
  }

  getNestedProperty(obj, path) {
    return path.split('.').reduce((current, prop) => current && current[prop], obj);
  }

  addSingleEventListener(element) {
    // Add appropriate single event listener based on element type
    if (element.classList.contains('signal-circle') || element.classList.contains('circle')) {
      element.addEventListener('click', (e) => {
        console.log('[CodebaseDeduplicationScanner] Signal circle clicked (deduplicated)');
      });
    } else if (element.classList.contains('menu-button')) {
      element.addEventListener('click', (e) => {
        console.log('[CodebaseDeduplicationScanner] Menu button clicked (deduplicated)');
      });
    }
    
    // Mark as having a listener to prevent future duplication
    element.dataset.hasClickListener = 'true';
  }

  reportResults() {
    console.log('[CodebaseDeduplicationScanner] === DEDUPLICATION RESULTS ===');
    console.log(`WebSockets removed: ${this.performanceMetrics.websocketsRemoved}`);
    console.log(`Event listeners removed: ${this.performanceMetrics.listenersRemoved}`);
    console.log(`DOM elements removed: ${this.performanceMetrics.elementsRemoved}`);
    console.log(`Function handlers removed: ${this.performanceMetrics.handlersRemoved}`);
    
    const totalRemoved = Object.values(this.performanceMetrics).reduce((sum, val) => sum + val, 0);
    console.log(`Total duplications removed: ${totalRemoved}`);
    
    if (totalRemoved > 0) {
      console.log('[CodebaseDeduplicationScanner] ✅ Codebase optimization completed - performance should be improved');
    } else {
      console.log('[CodebaseDeduplicationScanner] ℹ️ No significant duplications found');
    }
  }
}

// Initialize deduplication scanner
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    window.codebaseDeduplicationScanner = new CodebaseDeduplicationScanner();
  }, 5000); // Run after other systems have initialized
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CodebaseDeduplicationScanner;
}
