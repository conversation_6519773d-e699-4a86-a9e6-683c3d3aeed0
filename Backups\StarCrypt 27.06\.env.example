# API Keys (replace with your actual keys in .env file)
KRAKEN_API_KEY=your_kraken_api_key_here
KRAKEN_API_SECRET=your_kraken_api_secret_here
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_API_SECRET=your_binance_api_secret_here

# Database Configuration
DB_HOST=localhost
DB_PORT=27017
DB_NAME=starcrypt
DB_USER=
DB_PASS=

# Server Configuration
PORT=3001
NODE_ENV=development

# AI Configuration
AI_MODEL_PATH=./models
AI_LOG_LEVEL=info

# WebSocket Configuration
WS_PORT=8080

# Cache Configuration
CACHE_TTL=300000  # 5 minutes in milliseconds

# Security
JWT_SECRET=your_jwt_secret_here
SESSION_SECRET=your_session_secret_here
CORS_ORIGIN=http://localhost:3000

# Logging
LOG_LEVEL=debug
LOG_TO_FILE=true
LOG_FILE_PATH=./logs/app.log

# Feature Flags
ENABLE_AI=true
ENABLE_BACKTESTING=true
ENABLE_ALERTS=false

# Performance
MAX_WORKERS=4
BATCH_SIZE=100

# Email Configuration (for alerts)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
SMTP_FROM=<EMAIL>

# Monitoring
SENTRY_DSN=your_sentry_dsn_here

# External Services
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
NEWS_API_KEY=your_news_api_key

# Note: Never commit the actual .env file to version control
# Add .env to your .gitignore file
