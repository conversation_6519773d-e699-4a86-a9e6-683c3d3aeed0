const tf = require('@tensorflow/tfjs-node')
const TechnicalIndicators = require('technicalindicators')
const { fetchOHLC } = require('../../server')

class MarketDataProcessor {
  constructor() {
    this.dataCache = new Map()
    this.normalizationParams = new Map()
    this.cacheTTL = 5 * 60 * 1000 // 5 minutes cache TTL
  }

  // Fetch and preprocess OHLCV data
  async getProcessedData(pair, timeframe, forceUpdate = false) {
    const cacheKey = `${pair}_${timeframe}`
    const now = Date.now()

    // Check cache first
    if (!forceUpdate && this.dataCache.has(cacheKey)) {
      const { timestamp, data } = this.dataCache.get(cacheKey)
      if (now - timestamp < this.cacheTTL) {
        return data
      }
    }

    try {
      // Fetch fresh data
      const ohlcv = await fetchOHLC(pair, timeframe, forceUpdate)
      if (!ohlcv || ohlcv.length < 100) { // Ensure we have enough data
        throw new Error(`Insufficient data for ${pair} ${timeframe}`)
      }

      // Process the data
      const processed = this.processOHLCV(ohlcv)

      // Update cache
      this.dataCache.set(cacheKey, {
        timestamp: now,
        data: processed,
      })

      return processed
    } catch (error) {
      console.error(`Error processing market data for ${pair} ${timeframe}:`, error)

      // Return cached data if available, even if stale
      if (this.dataCache.has(cacheKey)) {
        console.log('Using stale cached data due to error')
        return this.dataCache.get(cacheKey).data
      }

      throw error
    }
  }

  // Process raw OHLCV data into features
  processOHLCV(ohlcv) {
    // Extract individual series
    const timestamps = ohlcv.map(d => d[0])
    const opens = ohlcv.map(d => parseFloat(d[1]))
    const highs = ohlcv.map(d => parseFloat(d[2]))
    const lows = ohlcv.map(d => parseFloat(d[3]))
    const closes = ohlcv.map(d => parseFloat(d[4]))
    const volumes = ohlcv.map(d => parseFloat(d[5]))

    // Calculate technical indicators
    const rsi = this.calculateRSI(closes)
    const macd = this.calculateMACD(closes)
    const bb = this.calculateBollingerBands(closes)
    const atr = this.calculateATR(highs, lows, closes)
    const adx = this.calculateADX(highs, lows, closes)
    const mfi = this.calculateMFI(highs, lows, closes, volumes)
    const stoch = this.calculateStochastic(highs, lows, closes)
    const obv = this.calculateOBV(closes, volumes)

    // Calculate price derivatives
    const returns = this.calculateReturns(closes)
    const volatilities = this.calculateVolatility(returns, 20)

    // Calculate volume profile
    const volumeProfile = this.calculateVolumeProfile(closes, volumes)

    // Calculate support/resistance levels
    const { support, resistance } = this.calculateSupportResistance(closes)

    // Calculate market regime
    const regime = this.calculateMarketRegime(closes, rsi, adx)

    return {
      timestamps,
      opens,
      highs,
      lows,
      closes,
      volumes,
      indicators: {
        rsi,
        macd,
        bollingerBands: bb,
        atr,
        adx,
        mfi,
        stoch,
        obv,
        returns,
        volatilities,
        volumeProfile,
        support,
        resistance,
        regime,
      },
      metadata: {
        symbol: ohlcv.symbol,
        timeframe: ohlcv.timeframe,
        lastUpdated: new Date().toISOString(),
      },
    }
  }

  // Technical indicator calculations
  calculateRSI(closes, period = 14) {
    return TechnicalIndicators.RSI.calculate({
      values: closes,
      period,
    })
  }

  calculateMACD(closes, fast = 12, slow = 26, signal = 9) {
    return TechnicalIndicators.MACD.calculate({
      values: closes,
      fastPeriod: fast,
      slowPeriod: slow,
      signalPeriod: signal,
      SimpleMAOscillator: false,
      SimpleMASignal: false,
    })
  }

  calculateBollingerBands(closes, period = 20, stdDev = 2) {
    return TechnicalIndicators.BollingerBands.calculate({
      values: closes,
      period,
      stdDev,
    })
  }

  calculateATR(highs, lows, closes, period = 14) {
    return TechnicalIndicators.ATR.calculate({
      high: highs,
      low: lows,
      close: closes,
      period,
    })
  }

  calculateADX(highs, lows, closes, period = 14) {
    return TechnicalIndicators.ADX.calculate({
      high: highs,
      low: lows,
      close: closes,
      period,
    })
  }

  calculateMFI(highs, lows, closes, volumes, period = 14) {
    return TechnicalIndicators.MFI.calculate({
      high: highs,
      low: lows,
      close: closes,
      volume: volumes,
      period,
    })
  }

  calculateStochastic(highs, lows, closes, period = 14, signalPeriod = 3) {
    return TechnicalIndicators.Stochastic.calculate({
      high: highs,
      low: lows,
      close: closes,
      period,
      signalPeriod,
    })
  }

  calculateOBV(closes, volumes) {
    return TechnicalIndicators.OBV.calculate({
      close: closes,
      volume: volumes,
    })
  }

  calculateReturns(closes) {
    const returns = []
    for (let i = 1; i < closes.length; i++) {
      returns.push((closes[i] - closes[i - 1]) / closes[i - 1])
    }
    return returns
  }

  calculateVolatility(returns, window = 20) {
    const volatilities = []
    for (let i = window; i <= returns.length; i++) {
      const windowReturns = returns.slice(i - window, i)
      const mean = windowReturns.reduce((a, b) => a + b, 0) / windowReturns.length
      const variance = windowReturns.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / windowReturns.length
      volatilities.push(Math.sqrt(variance))
    }
    return volatilities
  }

  calculateVolumeProfile(closes, volumes, numLevels = 10) {
    // Simple volume profile by price levels
    const minPrice = Math.min(...closes)
    const maxPrice = Math.max(...closes)
    const range = maxPrice - minPrice
    const levelSize = range / numLevels

    const levels = Array(numLevels).fill(0)

    for (let i = 0; i < closes.length; i++) {
      const price = closes[i]
      const volume = volumes[i]
      const level = Math.min(Math.floor((price - minPrice) / levelSize), numLevels - 1)
      levels[level] += volume
    }

    return levels
  }

  calculateSupportResistance(closes, lookback = 20) {
    // Simple support/resistance detection using recent highs and lows
    const support = Math.min(...closes.slice(-lookback))
    const resistance = Math.max(...closes.slice(-lookback))

    return { support, resistance }
  }

  calculateMarketRegime(closes, rsi, adx, adxThreshold = 25) {
    // Simple market regime detection
    const lastClose = closes[closes.length - 1]
    const sma50 = this.calculateSMA(closes, 50)
    const sma200 = this.calculateSMA(closes, 200)
    const lastRsi = rsi[rsi.length - 1]
    const lastAdx = adx[adx.length - 1]

    const trend = lastClose > sma50[sma50.length - 1] ? 'bullish' : 'bearish'
    const volatility = this.calculateVolatility(closes, 20).pop()
    const isTrending = lastAdx > adxThreshold
    const isOverbought = lastRsi > 70
    const isOversold = lastRsi < 30

    return {
      trend,
      isTrending,
      volatility,
      isOverbought,
      isOversold,
      strength: lastAdx / 100, // Normalized to [0,1]
    }
  }

  calculateSMA(data, period) {
    return TechnicalIndicators.SMA.calculate({
      values: data,
      period,
    })
  }

  // Normalize data for neural networks
  normalizeData(data, min = null, max = null) {
    if (!min || !max) {
      min = Math.min(...data)
      max = Math.max(...data)
    }

    const range = max - min || 1 // Avoid division by zero
    return {
      normalized: data.map(x => (x - min) / range),
      min,
      max,
    }
  }

  // Denormalize data
  denormalizeData(normalized, min, max) {
    return normalized * (max - min) + min
  }

  // Prepare data for LSTM
  createLSTMDataset(data, lookback = 50, predictionHorizon = 5) {
    const X = []
    const y = []

    for (let i = lookback; i < data.length - predictionHorizon; i++) {
      // Input sequence (lookback periods)
      const sequence = data.slice(i - lookback, i)

      // Target (price change after predictionHorizon periods)
      const futurePrice = data[i + predictionHorizon - 1]
      const currentPrice = data[i - 1]
      const priceChange = (futurePrice - currentPrice) / currentPrice

      X.push(sequence)
      y.push(priceChange > 0 ? 1 : 0) // Binary classification
    }

    return {
      X: tf.tensor3d(X, [X.length, lookback, 1]),
      y: tf.oneHot(tf.tensor1d(y, 'int32'), 2).toFloat(),
    }
  }
}

// Export singleton instance
module.exports = new MarketDataProcessor()
