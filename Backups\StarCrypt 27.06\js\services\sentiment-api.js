/**
 * StockGeist.ai Sentiment API Integration
 * 
 * This module provides integration with the StockGeist.ai API for cryptocurrency sentiment analysis.
 * StockGeist.ai offers sentiment data for 400+ cryptocurrencies based on social media activity.
 * 
 * Free tier includes:
 * - 10,000 credits per month
 * - Unrestricted access to all endpoints
 * - Real-time data via REST API or streams
 */

class SentimentAPI {
  constructor() {
    this.apiKey = ''; // You'll need to sign up at https://api.stockgeist.ai/dashboard/signup to get an API key
    this.baseUrl = 'https://api.stockgeist.ai/v1';
    this.sentimentCache = new Map(); // Cache sentiment data to reduce API calls
    this.cacheTTL = 15 * 60 * 1000; // 15 minutes cache TTL
  }

  /**
   * Initialize the API with your StockGeist.ai API key
   * @param {string} apiKey - Your StockGeist.ai API key
   */
  initialize(apiKey) {
    if (!apiKey) {
      console.warn('[SentimentAPI] No API key provided. Please sign up at https://api.stockgeist.ai/dashboard/signup');
      return false;
    }
    
    this.apiKey = apiKey;
    console.log('[SentimentAPI] Initialized with API key');
    return true;
  }

  /**
   * Get sentiment data for a specific cryptocurrency
   * @param {string} symbol - Cryptocurrency symbol (e.g., 'BTC', 'ETH')
   * @returns {Promise<Object>} - Sentiment data object
   */
  async getSentiment(symbol) {
    try {
      // Normalize symbol
      const normalizedSymbol = symbol.toLowerCase().replace('usdt', '');
      
      // Check cache first
      const cacheKey = `sentiment_${normalizedSymbol}`;
      const cachedData = this.sentimentCache.get(cacheKey);
      
      if (cachedData && (Date.now() - cachedData.timestamp < this.cacheTTL)) {
        console.log(`[SentimentAPI] Using cached sentiment data for ${normalizedSymbol}`);
        return cachedData.data;
      }
      
      // If not in cache or expired, fetch from API
      console.log(`[SentimentAPI] Fetching sentiment data for ${normalizedSymbol}`);
      
      if (!this.apiKey) {
        return {
          error: 'API key not configured',
          symbol: normalizedSymbol,
          sentiment: 'neutral',
          score: 0.5,
          volume: 0,
          timestamp: Date.now()
        };
      }
      
      // Make API request to StockGeist.ai
      const response = await fetch(`${this.baseUrl}/crypto/${normalizedSymbol}/sentiment`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }
      
      const data = await response.json();
      
      // Transform API response to our format
      const sentimentData = {
        symbol: normalizedSymbol,
        sentiment: data.sentiment || 'neutral',
        score: data.score || 0.5,
        volume: data.volume || 0,
        socialActivity: data.activity || 0,
        timestamp: Date.now()
      };
      
      // Cache the result
      this.sentimentCache.set(cacheKey, {
        data: sentimentData,
        timestamp: Date.now()
      });
      
      return sentimentData;
      
    } catch (error) {
      console.error(`[SentimentAPI] Error fetching sentiment for ${symbol}:`, error.message);
      
      // Return fallback data in case of error
      return {
        error: error.message,
        symbol: symbol.toLowerCase(),
        sentiment: 'neutral',
        score: 0.5,
        volume: 0,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get sentiment data for multiple cryptocurrencies
   * @param {Array<string>} symbols - Array of cryptocurrency symbols
   * @returns {Promise<Object>} - Object with sentiment data for each symbol
   */
  async getBatchSentiment(symbols) {
    try {
      const results = {};
      
      // Process symbols in batches to avoid rate limiting
      const batchSize = 5;
      const batches = [];
      
      // Split symbols into batches
      for (let i = 0; i < symbols.length; i += batchSize) {
        batches.push(symbols.slice(i, i + batchSize));
      }
      
      // Process each batch sequentially
      for (const batch of batches) {
        const batchPromises = batch.map(symbol => this.getSentiment(symbol));
        const batchResults = await Promise.all(batchPromises);
        
        // Add results to the results object
        batchResults.forEach((result, index) => {
          results[batch[index]] = result;
        });
        
        // Small delay between batches to avoid rate limiting
        if (batches.indexOf(batch) < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
      
      return results;
      
    } catch (error) {
      console.error('[SentimentAPI] Error fetching batch sentiment:', error.message);
      return { error: error.message };
    }
  }

  /**
   * Get historical sentiment data for a cryptocurrency
   * @param {string} symbol - Cryptocurrency symbol
   * @param {string} timeframe - Timeframe for historical data (e.g., '1d', '7d', '30d')
   * @returns {Promise<Object>} - Historical sentiment data
   */
  async getHistoricalSentiment(symbol, timeframe = '7d') {
    try {
      // Normalize symbol
      const normalizedSymbol = symbol.toLowerCase().replace('usdt', '');
      
      // Check cache first
      const cacheKey = `historical_${normalizedSymbol}_${timeframe}`;
      const cachedData = this.sentimentCache.get(cacheKey);
      
      if (cachedData && (Date.now() - cachedData.timestamp < this.cacheTTL)) {
        console.log(`[SentimentAPI] Using cached historical sentiment data for ${normalizedSymbol}`);
        return cachedData.data;
      }
      
      // If not in cache or expired, fetch from API
      console.log(`[SentimentAPI] Fetching historical sentiment data for ${normalizedSymbol}`);
      
      if (!this.apiKey) {
        return {
          error: 'API key not configured',
          symbol: normalizedSymbol,
          data: [],
          timestamp: Date.now()
        };
      }
      
      // Make API request to StockGeist.ai
      const response = await fetch(`${this.baseUrl}/crypto/${normalizedSymbol}/historical?timeframe=${timeframe}`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }
      
      const data = await response.json();
      
      // Transform API response to our format
      const historicalData = {
        symbol: normalizedSymbol,
        timeframe: timeframe,
        data: data.data || [],
        timestamp: Date.now()
      };
      
      // Cache the result
      this.sentimentCache.set(cacheKey, {
        data: historicalData,
        timestamp: Date.now()
      });
      
      return historicalData;
      
    } catch (error) {
      console.error(`[SentimentAPI] Error fetching historical sentiment for ${symbol}:`, error.message);
      
      // Return fallback data in case of error
      return {
        error: error.message,
        symbol: symbol.toLowerCase(),
        timeframe: timeframe,
        data: [],
        timestamp: Date.now()
      };
    }
  }

  /**
   * Clear the sentiment cache
   */
  clearCache() {
    this.sentimentCache.clear();
    console.log('[SentimentAPI] Cache cleared');
  }
}

// Create and export a singleton instance
const sentimentAPI = new SentimentAPI();

// Make it available globally
if (typeof window !== 'undefined') {
  window.sentimentAPI = sentimentAPI;
}

// For Node.js environment
if (typeof module !== 'undefined' && module.exports) {
  module.exports = sentimentAPI;
}
