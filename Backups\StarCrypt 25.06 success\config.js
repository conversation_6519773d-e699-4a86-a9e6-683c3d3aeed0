// Server configuration
const PORT = process.env.PORT || 3000
const WS_PORT = process.env.WS_PORT || 8080

// Trading configuration
const DEFAULT_PAIR = 'xbtusd'
const DEFAULT_TIMEFRAME = '1h'
const TIMEFRAMES = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']
const SUPPORTED_PAIRS = [
  'xbtusd', 'ethusd', 'ltcusd', 'xrpusd',
  'adausd', 'solusd', 'dotusd', 'dogeusd',
]

// API configuration
const API_BASE_URL = 'https://api.kraken.com/0/public'
const REQUEST_DELAY = 1000 // 1 second delay between API requests
const MAX_RETRIES = 3
const RATE_LIMIT_REQUESTS = 15 // Max requests per minute
const RATE_LIMIT_WINDOW = 60000 // 1 minute in milliseconds

// WebSocket configuration
const WS_URL = 'wss://ws.kraken.com'
const WS_RECONNECT_INTERVAL = 5000 // 5 seconds
const WS_HEARTBEAT_INTERVAL = 30000 // 30 seconds

// Data configuration
const MAX_CANDLES = 720 // Maximum number of candles to store per timeframe
const DATA_UPDATE_INTERVAL = 60000 // 1 minute

module.exports = {
  // Server
  PORT,
  WS_PORT,

  // Trading
  DEFAULT_PAIR,
  DEFAULT_TIMEFRAME,
  TIMEFRAMES,
  SUPPORTED_PAIRS,

  // API
  API_BASE_URL,
  REQUEST_DELAY,
  MAX_RETRIES,
  RATE_LIMIT_REQUESTS,
  RATE_LIMIT_WINDOW,

  // WebSocket
  WS_URL,
  WS_RECONNECT_INTERVAL,
  WS_HEARTBEAT_INTERVAL,

  // Data
  MAX_CANDLES,
  DATA_UPDATE_INTERVAL,
}
