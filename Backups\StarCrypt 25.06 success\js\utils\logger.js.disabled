/**
 * Logger utility for StarCrypt
 * Provides centralized logging functionality with different log levels
 * and automatic console integration
 */

// Initialize logger state
let isInitialized = false
const MAX_LOG_ENTRIES = 1000

// Default log levels
const LOG_LEVELS = {
  error: 0,
  warn: 1,

    const Logger = {
        /**
         * Adds a message to the log array and triggers a UI update.
         * @param {string} message - The message to be logged.
         * @param {string} [type='info'] - The type of message (e.g., 'info', 'warn', 'error', 'success').
         */
        add: function(message, type = 'info') {
            if (typeof message !== 'string' || message.trim() === '') {
                return; // Do not log empty messages
        } catch (e) {
          // Silently handle any errors during legacy log processing
        }
      })
    }

    // Save original console methods
    const originalConsole = {
      log: console.log,
      error: console.error,
      warn: console.warn,
      info: console.info,
      debug: console.debug,
    }

    // Safely override console methods
    Object.keys(originalConsole).forEach(level => {
      try {
        const originalMethod = originalConsole[level]

        console[level] = function (...args) {
          try {
            // Call original console method first
            originalMethod.apply(console, args)

            // Safely log to our system
            if (level in LOG_LEVELS) {
              const message = args.map(arg =>
                typeof arg === 'object' ? JSON.stringify(arg) : String(arg),
              ).join(' ')

              addToLog(level, message)
            }
          } catch (e) {
            // If our logging fails, still ensure the original console works
            originalMethod.apply(console, args)
          }
        }
      } catch (e) {
        // If we can't override a console method, continue with others
      }
    })

    // Log successful initialization
    originalConsole.log('[INFO] Logger initialized successfully')
  } catch (error) {
    // If initialization fails completely, at least ensure console works
    console.error('Logger initialization failed:', error)
  }
}

// Export public API
window.Logger = {
  log: log.bind(null, 'info'),
  info: log.bind(null, 'info'),
  warn: log.bind(null, 'warn'),
  error: log.bind(null, 'error'),
  debug: log.bind(null, 'debug'),
  trace: log.bind(null, 'trace'),
  setLevel: setLogLevel,
  getLogs: () => [...logMessages],
  clear: () => { logMessages = [] },
  initialize: initializeLogger,
}

// Initialize the logger with a small delay to ensure the DOM is ready
// and to prevent any potential race conditions
const initLogger = () => {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(initializeLogger, 100)
    })
  } else {
    setTimeout(initializeLogger, 100)
  }
}

// Start the initialization
initLogger()
