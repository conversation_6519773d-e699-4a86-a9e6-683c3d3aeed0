class AIAnalysisService {
  constructor() {
    this.models = {
      pricePrediction: null,
      trendAnalysis: null,
      anomalyDetection: null,
      sentimentAnalysis: null,
      riskAssessment: null,
    }
    this.initialized = false
    this.history = []
    this.maxHistorySize = 1000
    this.isTraining = false
  }

  async initialize() {
    if (this.initialized) return true

    try {
      console.log('Initializing AI Analysis Service...')

      // Load TensorFlow.js or other ML libraries
      await this.loadLibraries()

      // Initialize ML models in sequence to avoid overwhelming the system
      await this.initializePricePredictionModel()
      await this.initializeTrendAnalysisModel()
      await this.initializeAnomalyDetectionModel()
      await this.initializeSentimentAnalysisModel()
      await this.initializeRiskAssessmentModel()

      // Start model training in the background
      this.trainModelsInBackground()

      this.initialized = true
      console.log('AI Analysis Service initialized successfully')
      return true
    } catch (error) {
      console.error('Failed to initialize AI Analysis Service:', error)
      this.initialized = false
      throw error // Re-throw to allow handling by the caller
    }
  }

  async loadLibraries() {
    // Load TensorFlow.js or other required ML libraries
    if (typeof tf === 'undefined') {
      await this.loadScript('https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@3.18.0/dist/tf.min.js')
    }
  }

  async initializePricePredictionModel() {
    try {
      // In a production app, we would load a pre-trained model here
      // For now, we'll use a simple moving average model
      this.models.pricePrediction = {
        predict: async (data) => {
          if (!data || typeof data !== 'object') {
            throw new Error('Invalid data for price prediction')
          }

          // Simple moving average as a placeholder
          const closePrices = this.history.slice(-20).map(d => d.close)
          const sum = closePrices.reduce((a, b) => a + b, 0)
          const avg = closePrices.length > 0 ? sum / closePrices.length : data.close

          // Add some randomness for demo purposes
          const prediction = data.close * 0.7 + avg * 0.3
          const confidence = Math.min(0.9, 0.7 + Math.random() * 0.2)

          return {
            prediction,
            confidence,
            timestamp: Date.now(),
            indicators: {
              support: data.close * 0.98,
              resistance: data.close * 1.02,
              trend: prediction > data.close ? 'bullish' : 'bearish',
              strength: Math.abs(prediction - data.close) / data.close,
            },
            modelInfo: {
              name: 'Hybrid Price Predictor',
              version: '1.0.0',
              lastTrained: new Date().toISOString(),
            },
          }
        },
        train: async (data) => {
          // In a real implementation, this would update the model with new data
          console.log('Training price prediction model with', data.length, 'samples')
          return { success: true }
        },
      }

      console.log('Price prediction model initialized')
    } catch (error) {
      console.error('Failed to initialize price prediction model:', error)
      throw error
    }
  }

  async initializeRiskAssessmentModel() {
    try {
      this.models.riskAssessment = {
        assess: async (data) => {
          if (!data || typeof data !== 'object') {
            throw new Error('Invalid data for risk assessment')
          }

          // Simple risk assessment based on recent volatility
          const recentPrices = this.history.slice(-10).map(d => d.close)
          const priceChanges = []

          for (let i = 1; i < recentPrices.length; i++) {
            priceChanges.push(Math.abs((recentPrices[i] - recentPrices[i - 1]) / recentPrices[i - 1]))
          }

          const avgVolatility = priceChanges.length > 0 ?
            priceChanges.reduce((a, b) => a + b, 0) / priceChanges.length : 0.01

          const riskScore = Math.min(1, Math.max(0, avgVolatility * 10))

          return {
            riskScore,
            volatility: avgVolatility,
            positionSize: Math.max(0.1, Math.min(1, 1 - riskScore * 0.8)),
            stopLoss: data.close * (1 - (0.02 + riskScore * 0.03)),
            takeProfit: data.close * (1 + (0.04 + riskScore * 0.06)),
            timestamp: Date.now(),
            resistance: data.close * 1.02,
            trend: Math.random() > 0.5 ? 'bullish' : 'bearish',
            strength: Math.random(),
          }
        },
        train: async (data) => {
          console.log('Training risk assessment model with', data.length, 'samples')
          return { success: true }
        },
      }

      console.log('Risk assessment model initialized')
    } catch (error) {
      console.error('Failed to initialize risk assessment model:', error)
      throw error
    }
  }

  async initializeTrendAnalysisModel() {
    // Initialize trend analysis model
    this.models.trendAnalysis = {
      analyze: (data) => {
        // Implement advanced trend analysis
        return {
          primaryTrend: Math.random() > 0.5 ? 'up' : 'down',
          strength: Math.random(),
          confidence: Math.random(),
          keyLevels: this.calculateKeyLevels(data),
          volumeAnalysis: this.analyzeVolume(data),
        }
      },
    }
  }

  async initializeAnomalyDetectionModel() {
    // Initialize anomaly detection
    this.models.anomalyDetection = {
      detect: (data) => {
        // Implement anomaly detection
        return {
          anomalies: [],
          volatility: Math.random(),
          riskScore: Math.random(),
        }
      },
    }
  }

  async initializeSentimentAnalysisModel() {
    // Initialize sentiment analysis
    this.models.sentimentAnalysis = {
      analyze: async (textData) => {
        // Implement sentiment analysis
        return {
          sentiment: Math.random() > 0.5 ? 'positive' : 'negative',
          score: Math.random() * 2 - 1,
          confidence: Math.random(),
        }
      },
    }
  }

  // Helper methods
  calculateKeyLevels(data) {
    if (!data || typeof data !== 'object') {
      return {
        support: 0,
        resistance: 0,
        pivot: 0,
      }
    }

    // Calculate key support/resistance levels
    return {
      support: data.low * 0.99,
      resistance: data.high * 1.01,
      pivot: (data.high + data.low + data.close) / 3,
    }
  }

  analyzeVolume(data) {
    if (!data || typeof data !== 'object') {
      return {
        volumeMA: 0,
        trend: 'neutral',
        anomaly: false,
      }
    }

    // Analyze volume patterns
    return {
      volumeMA: (data.volume || 0) * (0.9 + Math.random() * 0.2),
      trend: Math.random() > 0.5 ? 'increasing' : 'decreasing',
      anomaly: Math.random() > 0.8,
    }
  }

  loadScript(url) {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = url
      script.onload = () => resolve()
      script.onerror = () => reject(new Error(`Failed to load script: ${url}`))
      document.head.appendChild(script)
    })
  }

  // Public API
  async analyzeMarket(marketData) {
    if (!this.initialized) {
      await this.initialize()
    }

    try {
      if (!marketData || typeof marketData !== 'object') {
        throw new Error('Invalid market data provided')
      }

      // Add to history for future analysis
      this.updateHistory(marketData)

      // Run all analyses in parallel with error handling for each
      const analysisPromises = [
        this.safeAnalysis(this.analyzePricePrediction, marketData, 'pricePrediction'),
        this.safeAnalysis(this.analyzeTrend, marketData, 'trendAnalysis'),
        this.safeAnalysis(this.detectAnomalies, marketData, 'anomalyDetection'),
        this.safeAnalysis(this.analyzeSentiment, marketData, 'sentimentAnalysis'),
        this.safeAnalysis(this.assessRisk, marketData, 'riskAssessment'),
      ]

      const [
        pricePrediction,
        trendAnalysis,
        anomalyDetection,
        sentimentAnalysis,
        riskAssessment,
      ] = await Promise.all(analysisPromises)

      // Generate action recommendation based on analysis
      const actionRecommendation = this.generateActionRecommendation({
        pricePrediction,
        trendAnalysis,
        anomalyDetection,
        sentimentAnalysis,
        riskAssessment,
      })

      const result = {
        timestamp: Date.now(),
        marketData,
        pricePrediction,
        trendAnalysis,
        anomalyDetection,
        sentimentAnalysis,
        riskAssessment,
        actionRecommendation,
      }

      // Emit event with analysis results
      this.emitAnalysisUpdate(result)

      return result
    } catch (error) {
      console.error('Error analyzing market data:', error)
      this.emitError(error)
      throw error
    }
  }

  // Helper method to safely run analysis and handle errors
  async safeAnalysis(analysisFn, data, analysisName) {
    try {
      return await analysisFn.call(this, data)
    } catch (error) {
      console.error(`Error in ${analysisName}:`, error)
      return {
        error: error.message,
        timestamp: Date.now(),
        analysis: analysisName,
        status: 'error',
      }
    }
  }

  // Emit analysis update event
  emitAnalysisUpdate(analysis) {
    if (typeof window !== 'undefined') {
      const event = new CustomEvent('aiAnalysisUpdate', { detail: analysis })
      window.dispatchEvent(event)
    }
  }

  // Emit error event
  emitError(error) {
    if (typeof window !== 'undefined') {
      const event = new CustomEvent('aiAnalysisError', {
        detail: {
          message: error.message,
          timestamp: Date.now(),
          stack: error.stack,
        },
      })
      window.dispatchEvent(event)
    }
  }

  // Update history
  updateHistory(data) {
    this.history.push(data)
  }

  // Analyze price prediction
  async analyzePricePrediction(data) {
    return this.models.pricePrediction.predict(data)
  }

  // Analyze trend
  async analyzeTrend(data) {
    return this.models.trendAnalysis.analyze(data)
  }

  // Detect anomalies
  async detectAnomalies(data) {
    return this.models.anomalyDetection.detect(data)
  }

  // Analyze sentiment
  async analyzeSentiment(data) {
    return this.models.sentimentAnalysis.analyze(data)
  }

  // Assess risk
  async assessRisk(data) {
    return this.models.riskAssessment.assess(data)
  }

  // Generate action recommendation
  generateActionRecommendation(analysis) {
    try {
      const {
        pricePrediction,
        trendAnalysis,
        sentimentAnalysis,
        riskAssessment,
      } = analysis

      // Default values
      let action = 'hold'
      let confidence = 0.5
      let reason = 'Neutral market conditions'
      let stopLoss = 0
      let takeProfit = 0
      let riskRewardRatio = 1.5

      // Get current price or use the close price from market data
      const currentPrice = pricePrediction?.marketData?.close ||
                         (analysis.marketData ? analysis.marketData.close : 0)

      if (!currentPrice) {
        throw new Error('Unable to determine current price')
      }

      // Calculate technical score (0-1)
      const technicalScore = this.calculateTechnicalScore(analysis)

      // Calculate sentiment score (0-1)
      const sentimentScore = sentimentAnalysis?.score ?
        (sentimentAnalysis.score + 1) / 2 : 0.5

      // Combine scores with weights
      const buySignal = technicalScore * 0.6 + sentimentScore * 0.4

      // Determine action based on combined score
      if (buySignal > 0.7) {
        action = 'strong_buy'
        confidence = this.normalizeConfidence(buySignal * 0.9)
        reason = 'Strong buy signal from technical and sentiment analysis'
      } else if (buySignal > 0.55) {
        action = 'buy'
        confidence = this.normalizeConfidence(buySignal * 0.8)
        reason = 'Buy signal from technical and sentiment analysis'
      } else if (buySignal < 0.3) {
        action = 'strong_sell'
        confidence = this.normalizeConfidence((1 - buySignal) * 0.9)
        reason = 'Strong sell signal from technical and sentiment analysis'
      } else if (buySignal < 0.45) {
        action = 'sell'
        confidence = this.normalizeConfidence((1 - buySignal) * 0.8)
        reason = 'Sell signal from technical and sentiment analysis'
      } else {
        action = 'hold'
        confidence = 0.6 // Higher confidence in hold signals to prevent overtrading
        reason = 'Market conditions are neutral'
      }

      // Adjust stop loss and take profit based on volatility
      const atrMultiplier = riskAssessment?.volatility ?
        Math.max(1, Math.min(3, 1 / (riskAssessment.volatility * 100))) : 2

      if (action.includes('buy')) {
        stopLoss = currentPrice * (1 - (0.01 * atrMultiplier))
        takeProfit = currentPrice * (1 + (0.02 * atrMultiplier))
      } else if (action.includes('sell')) {
        stopLoss = currentPrice * (1 + (0.01 * atrMultiplier))
        takeProfit = currentPrice * (1 - (0.02 * atrMultiplier))
      }

      // Calculate risk/reward ratio
      if (stopLoss > 0 && takeProfit > 0) {
        const risk = Math.abs(currentPrice - stopLoss)
        const reward = Math.abs(takeProfit - currentPrice)
        riskRewardRatio = reward / Math.max(risk, 0.0001) // Avoid division by zero
      }

      return {
        action,
        confidence: Math.min(0.99, Math.max(0.01, confidence)), // Clamp between 0.01 and 0.99
        reason,
        timestamp: Date.now(),
        currentPrice,
        stopLoss,
        takeProfit,
        riskRewardRatio: parseFloat(riskRewardRatio.toFixed(2)),
        indicators: {
          technicalScore: parseFloat(technicalScore.toFixed(2)),
          sentimentScore: parseFloat(sentimentScore.toFixed(2)),
          combinedScore: parseFloat(buySignal.toFixed(2)),
        },
      }
    } catch (error) {
      console.error('Error generating action recommendation:', error)

      // Return a safe default recommendation in case of error
      return {
        action: 'hold',
        confidence: 0.5,
        reason: `Unable to generate recommendation: ${error.message}`,
        timestamp: Date.now(),
        error: true,
      }
    }
  }

  // Calculate technical score based on various indicators
  calculateTechnicalScore(analysis) {
    const {
      pricePrediction,
      trendAnalysis,
      anomalyDetection,
    } = analysis

    let score = 0.5 // Neutral score
    let weightSum = 0

    // Price momentum (30% weight)
    if (pricePrediction?.indicators?.trend) {
      const momentum = pricePrediction.indicators.trend === 'bullish' ? 1 : 0
      score += momentum * 0.3
      weightSum += 0.3
    }

    // Trend strength (25% weight)
    if (trendAnalysis?.strength) {
      const trendDir = trendAnalysis.trend === 'up' ? 1 : -1
      score += (trendAnalysis.strength * trendDir) * 0.25
      weightSum += 0.25
    }

    // Volatility (20% weight, higher volatility can be good or bad)
    if (pricePrediction?.indicators?.volatility) {
      // Normalize volatility to 0-1 range (assuming 0-5% is optimal)
      const normalizedVol = Math.min(1, pricePrediction.indicators.volatility * 20)
      // Slightly prefer some volatility over none
      score += (0.3 + normalizedVol * 0.4) * 0.2
      weightSum += 0.2
    }

    // Volume (15% weight)
    if (trendAnalysis?.volumeAnalysis?.volumeTrend) {
      const volScore = trendAnalysis.volumeAnalysis.volumeTrend === 'increasing' ? 1 : 0.5
      score += volScore * 0.15
      weightSum += 0.15
    }

    // Anomaly detection (10% weight, negative impact)
    if (anomalyDetection?.hasAnomaly) {
      score -= 0.1
      weightSum += 0.1
    }

    // Normalize the score based on actual weights used
    const normalizedScore = weightSum > 0 ? (score / weightSum) : 0.5

    // Apply sigmoid function to get a smooth 0-1 range
    return 1 / (1 + Math.exp(-10 * (normalizedScore - 0.5)))
  }

  // Normalize confidence to a reasonable range
  normalizeConfidence(score) {
    // Apply a sigmoid function to get a value between 0.5 and 0.95
    return 0.5 + (0.45 / (1 + Math.exp(-10 * (score - 0.5))))
  }

  // Train models in background
  async trainModelsInBackground() {
    if (this.isTraining) return

    this.isTraining = true

    try {
      // Use a subset of history for training
      const trainingData = this.history.slice(-1000)

      if (trainingData.length < 100) {
        console.log('Not enough data for training. Waiting for more data...')
        return
      }

      console.log('Starting background model training with', trainingData.length, 'samples')

      // Train each model
      await Promise.all([
        this.models.pricePrediction.train?.(trainingData),
        this.models.trendAnalysis.train?.(trainingData),
        this.models.anomalyDetection.train?.(trainingData),
        this.models.sentimentAnalysis.train?.(trainingData),
        this.models.riskAssessment.train?.(trainingData),
      ])

      console.log('Background model training completed')
    } catch (error) {
      console.error('Error during background training:', error)
    } finally {
      this.isTraining = false

      // Schedule next training
      setTimeout(() => this.trainModelsInBackground(), 30 * 60 * 1000) // Every 30 minutes
    }
  }
}

// Export as singleton
export const aiAnalysisService = new AIAnalysisService()
