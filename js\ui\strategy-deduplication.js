/**
 * Strategy Deduplication Fix for StarCrypt
 * Prevents duplicate strategy loading and consolidates strategy functions
 */

class StrategyDeduplication {
  constructor() {
    this.isStrategyLoading = false;
    this.lastStrategyChange = 0;
    this.strategyChangeDebounce = 1000; // 1 second debounce
    this.init();
  }

  init() {
    console.log('[StrategyDeduplication] Initializing strategy deduplication...');
    
    try {
      this.consolidateStrategyFunctions();
      this.preventDuplicateLoading();
      this.fixStrategySelector();
      this.cleanupDuplicateEventListeners();
      
      console.log('[StrategyDeduplication] Strategy deduplication applied successfully');
    } catch (error) {
      console.error('[StrategyDeduplication] Error applying strategy deduplication:', error);
    }
  }

  consolidateStrategyFunctions() {
    // Create a single, consolidated strategy application function
    window.applyStrategyConsolidated = (strategy, source = 'unknown') => {
      const now = Date.now();
      
      // Prevent rapid successive calls
      if (this.isStrategyLoading || (now - this.lastStrategyChange) < this.strategyChangeDebounce) {
        console.log(`[StrategyDeduplication] Ignoring duplicate strategy change to ${strategy} from ${source}`);
        return false;
      }
      
      this.isStrategyLoading = true;
      this.lastStrategyChange = now;
      
      console.log(`[StrategyDeduplication] Applying strategy ${strategy} from ${source}`);
      
      try {
        // Update current strategy
        window.currentStrategy = strategy;
        
        // Update strategy selector without triggering events
        this.updateStrategySelector(strategy);
        
        // Update indicators for strategy
        this.updateIndicatorsForStrategy(strategy);
        
        // Update strategy info panel
        this.updateStrategyInfoPanel(strategy);
        
        // Update Admiral helper
        this.updateAdmiralHelper(strategy);
        
        // Show strategy animation (only once)
        this.showStrategyAnimation(strategy);
        
        // Send to server (only once)
        this.sendStrategyToServer(strategy);
        
        // Update thresholds
        this.loadStrategyThresholds(strategy);
        
        console.log(`[StrategyDeduplication] Successfully applied strategy ${strategy}`);
        
        // Show single confirmation
        this.showStrategyConfirmation(strategy);
        
        return true;
        
      } catch (error) {
        console.error(`[StrategyDeduplication] Error applying strategy ${strategy}:`, error);
        return false;
      } finally {
        // Reset loading flag after a delay
        setTimeout(() => {
          this.isStrategyLoading = false;
        }, this.strategyChangeDebounce);
      }
    };
  }

  updateStrategySelector(strategy) {
    const selectors = [
      document.getElementById('mainStrategySelector'),
      document.getElementById('strategySelector'),
      document.querySelector('.strategy-selector select')
    ];
    
    selectors.forEach(selector => {
      if (selector && selector.value !== strategy) {
        // Temporarily remove event listeners to prevent cascading
        const originalOnChange = selector.onchange;
        selector.onchange = null;
        
        selector.value = strategy;
        
        // Restore event listener after a brief delay
        setTimeout(() => {
          selector.onchange = originalOnChange;
        }, 100);
      }
    });
  }

  updateIndicatorsForStrategy(strategy) {
    if (!window.TRADING_STRATEGIES || !window.TRADING_STRATEGIES[strategy]) {
      console.error(`[StrategyDeduplication] Strategy ${strategy} not found`);
      return;
    }
    
    const strategyDetails = window.TRADING_STRATEGIES[strategy];
    const strategyIndicators = strategyDetails.indicators || [];
    
    // Update enabled indicators
    if (window.enabledIndicators) {
      window.enabledIndicators.forEach(indicator => {
        indicator.enabled = strategyIndicators.includes(indicator.name);
      });
    }
    
    // Update signal matrix
    if (typeof window.updateSignalMatrix === 'function') {
      window.updateSignalMatrix();
    }
  }

  updateStrategyInfoPanel(strategy) {
    const strategyDetails = window.TRADING_STRATEGIES?.[strategy];
    if (!strategyDetails) return;
    
    // Update main strategy info content
    const mainInfoContent = document.getElementById('mainStrategyInfoContent');
    if (mainInfoContent) {
      mainInfoContent.innerHTML = `<p>${strategyDetails.description}</p>`;
    }
    
    // Update strategy info content
    const infoContent = document.getElementById('strategyInfoContent');
    if (infoContent) {
      infoContent.innerHTML = `<p>${strategyDetails.description}</p>`;
    }
    
    // Update indicators list
    const indicatorsContent = document.getElementById('mainStrategyIndicatorsContent');
    if (indicatorsContent && strategyDetails.indicators) {
      indicatorsContent.innerHTML = strategyDetails.indicators
        .map(ind => `<span class="indicator-tag">${ind.toUpperCase()}</span>`)
        .join('');
    }
    
    // Update strategy highlight in Admiral chat
    const strategyHighlight = document.getElementById('currentStrategyHighlight');
    if (strategyHighlight) {
      strategyHighlight.textContent = strategyDetails.name;
    }
  }

  updateAdmiralHelper(strategy) {
    const strategyDetails = window.TRADING_STRATEGIES?.[strategy];
    if (!strategyDetails || !strategyDetails.helperText) return;
    
    const helperContainer = document.querySelector('.helper-container');
    if (helperContainer) {
      helperContainer.innerHTML = `
        <div class="helper-header">
          <h3>Admiral T.O.A. Strategy Guide</h3>
          <span class="strategy-name">${strategyDetails.name}</span>
        </div>
        <div class="helper-content">
          ${strategyDetails.helperText.map((step, index) => 
            `<div class="helper-step">
              <span class="step-number">${index + 1}</span>
              <span class="step-text">${step}</span>
            </div>`
          ).join('')}
        </div>
      `;
    }
  }

  showStrategyAnimation(strategy) {
    // Remove any existing animation to prevent duplicates
    const existingAnimation = document.querySelector('.strategy-animation-overlay');
    if (existingAnimation) {
      existingAnimation.remove();
    }
    
    const strategyDetails = window.TRADING_STRATEGIES?.[strategy];
    if (!strategyDetails) return;
    
    const animationContainer = document.createElement('div');
    animationContainer.className = 'strategy-animation-overlay';
    animationContainer.innerHTML = `
      <div class="strategy-animation-content">
        <div class="strategy-animation-header">
          <h2>Strategy Activated</h2>
        </div>
        <div class="strategy-animation-body">
          <div class="strategy-name">${strategyDetails.name}</div>
          <div class="strategy-indicators">
            ${strategyDetails.indicators.map(ind => 
              `<span class="animated-indicator">${ind.toUpperCase()}</span>`
            ).join('')}
          </div>
        </div>
        <button class="dismiss-animation" onclick="this.parentElement.parentElement.remove()">×</button>
      </div>
    `;
    
    document.body.appendChild(animationContainer);
    
    // Auto-dismiss after 3 seconds
    setTimeout(() => {
      if (animationContainer.parentNode) {
        animationContainer.style.opacity = '0';
        setTimeout(() => {
          if (animationContainer.parentNode) {
            animationContainer.remove();
          }
        }, 500);
      }
    }, 3000);
  }

  sendStrategyToServer(strategy) {
    if (window.ws && window.ws.readyState === WebSocket.OPEN) {
      const message = {
        type: 'setStrategy',
        strategy: strategy,
        timestamp: Date.now()
      };
      
      window.ws.send(JSON.stringify(message));
      console.log(`[StrategyDeduplication] Sent strategy ${strategy} to server`);
      
      // Log the action
      if (window.logMessages && window.updateLogger) {
        window.logMessages.push(`[${new Date().toLocaleString()}] Strategy changed to ${strategy}`);
        window.updateLogger();
      }
    }
  }

  loadStrategyThresholds(strategy) {
    const strategyDetails = window.TRADING_STRATEGIES?.[strategy];
    if (!strategyDetails || !strategyDetails.thresholds) return;
    
    // Update thresholds for each indicator in the strategy
    strategyDetails.indicators.forEach(indicator => {
      const thresholds = strategyDetails.thresholds[indicator];
      if (thresholds && window.thresholds) {
        window.thresholds[indicator] = { ...thresholds };
      }
    });
    
    // Render threshold sliders if function exists
    if (typeof window.renderThresholdSliders === 'function') {
      window.renderThresholdSliders(strategy);
    }
  }

  showStrategyConfirmation(strategy) {
    const strategyDetails = window.TRADING_STRATEGIES?.[strategy];
    if (!strategyDetails) return;
    
    // Create a single, non-duplicate confirmation
    const confirmation = document.createElement('div');
    confirmation.className = 'strategy-confirmation';
    confirmation.innerHTML = `
      <div class="confirmation-content">
        <span class="confirmation-icon">✓</span>
        <span class="confirmation-text">Strategy: ${strategyDetails.name}</span>
      </div>
    `;
    
    // Remove any existing confirmations
    document.querySelectorAll('.strategy-confirmation').forEach(el => el.remove());
    
    document.body.appendChild(confirmation);
    
    // Auto-remove after 2 seconds
    setTimeout(() => {
      if (confirmation.parentNode) {
        confirmation.style.opacity = '0';
        setTimeout(() => {
          if (confirmation.parentNode) {
            confirmation.remove();
          }
        }, 300);
      }
    }, 2000);
  }

  preventDuplicateLoading() {
    // Override existing strategy functions to use consolidated version
    const originalFunctions = [
      'applySelectedStrategy',
      'updateIndicatorsForStrategy',
      'showStrategyAnimation'
    ];
    
    originalFunctions.forEach(funcName => {
      if (window[funcName]) {
        const originalFunc = window[funcName];
        window[funcName] = (...args) => {
          console.log(`[StrategyDeduplication] Intercepted ${funcName}, redirecting to consolidated function`);
          if (args[0]) {
            return window.applyStrategyConsolidated(args[0], funcName);
          }
          return originalFunc.apply(this, args);
        };
      }
    });
  }

  fixStrategySelector() {
    // Remove duplicate options from strategy selectors
    const selectors = document.querySelectorAll('select[id*="strategy" i], select[id*="Strategy"]');
    
    selectors.forEach(selector => {
      const seenOptions = new Set();
      const optionsToRemove = [];
      
      Array.from(selector.options).forEach(option => {
        if (seenOptions.has(option.value)) {
          optionsToRemove.push(option);
        } else {
          seenOptions.add(option.value);
        }
      });
      
      optionsToRemove.forEach(option => option.remove());
    });
  }

  cleanupDuplicateEventListeners() {
    // Remove and re-add event listeners to prevent duplicates
    const strategySelectors = document.querySelectorAll('select[id*="strategy" i], select[id*="Strategy"]');
    
    strategySelectors.forEach(selector => {
      // Clone and replace to remove all event listeners
      const newSelector = selector.cloneNode(true);
      selector.parentNode.replaceChild(newSelector, selector);
      
      // Add single event listener
      newSelector.addEventListener('change', (e) => {
        const strategy = e.target.value;
        if (strategy && strategy !== window.currentStrategy) {
          window.applyStrategyConsolidated(strategy, 'selector');
        }
      });
    });
  }

  // Add styles for new elements
  applyStyles() {
    const style = document.createElement('style');
    style.textContent = `
      .strategy-animation-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        opacity: 1;
        transition: opacity 0.5s ease;
      }
      
      .strategy-animation-content {
        background: rgba(0, 20, 40, 0.95);
        border: 2px solid #00ffff;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        position: relative;
        max-width: 400px;
      }
      
      .strategy-animation-header h2 {
        color: #00ffff;
        margin: 0 0 15px 0;
        font-family: 'Orbitron', sans-serif;
      }
      
      .strategy-name {
        color: #ffffff;
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 15px;
      }
      
      .strategy-indicators {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        justify-content: center;
      }
      
      .animated-indicator {
        background: rgba(0, 255, 255, 0.2);
        border: 1px solid #00ffff;
        padding: 4px 8px;
        border-radius: 4px;
        color: #00ffff;
        font-size: 12px;
        animation: pulse 1s infinite;
      }
      
      .dismiss-animation {
        position: absolute;
        top: 10px;
        right: 15px;
        background: none;
        border: none;
        color: #ff6b6b;
        font-size: 20px;
        cursor: pointer;
      }
      
      .strategy-confirmation {
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(0, 255, 0, 0.1);
        border: 1px solid #00ff00;
        border-radius: 5px;
        padding: 10px 15px;
        color: #00ff00;
        z-index: 9999;
        opacity: 1;
        transition: opacity 0.3s ease;
      }
      
      .confirmation-content {
        display: flex;
        align-items: center;
        gap: 8px;
      }
      
      .confirmation-icon {
        font-size: 16px;
        font-weight: bold;
      }
      
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
      }
    `;
    document.head.appendChild(style);
  }
}

// Initialize strategy deduplication
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    window.strategyDeduplication = new StrategyDeduplication();
    window.strategyDeduplication.applyStyles();
  }, 2000);
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = StrategyDeduplication;
}
