// Import services and components
import { aiIntegrationService } from './services/AIIntegrationService.js'
import PriceChart from './components/PriceChart.js'

// Main Application Initialization
class StarCryptApp {
  constructor() {
    this.initialized = false
    this.modules = {
      websocket: null,
    }
    this.priceChart = null

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.init())
    } else {
      setTimeout(() => this.init(), 0)
    }
  }

  // Initialize the application
  async init() {
    if (this.initialized) return

    try {
      console.log('Initializing StarCrypt...')

      // Initialize UI components
      this.initUI()

      // Initialize AI services and dashboard
      await this.initAIServices()

      // Initialize WebSocket connection
      this.initWebSocket()

      // Initialize chart
      this.initChart()

      // Hide loading overlay
      this.hideLoadingOverlay()

      this.initialized = true
      console.log('StarCrypt initialized successfully')
    } catch (error) {
      console.error('Initialization error:', error)
      this.showError('Failed to initialize application. Please check the console for details.')
    }
  }

  // Initialize AI services and dashboard
  async initAIServices() {
    try {
      console.log('Initializing AI services...')

      // Initialize AI integration service
      await aiIntegrationService.initialize('ai-dashboard-container')

      // Listen for analysis updates
      window.addEventListener('aiAnalysisUpdate', (event) => {
        this.handleAIAnalysisUpdate(event.detail)
      })

      console.log('AI services initialized')
    } catch (error) {
      console.error('Failed to initialize AI services:', error)
      throw error
    }
  }

  // Handle AI analysis updates
  handleAIAnalysisUpdate(analysis) {
    // Here you can implement custom logic to handle the analysis results
    // For example, update UI, trigger alerts, or execute trades

    const { actionRecommendation } = analysis

    // Example: Update a status indicator based on the recommended action
    const statusElement = document.getElementById('ai-status-indicator')
    if (statusElement) {
      statusElement.className = `ai-status ${actionRecommendation.action}`
      statusElement.title = `AI Recommendation: ${actionRecommendation.action} (${Math.round(actionRecommendation.confidence * 100)}% confidence)`
    }

    // You can add more custom logic here based on the analysis
  }

  // Initialize the price chart
  initChart() {
    try {
      console.log('=== Initializing Price Chart ===')

      // Check if chart container exists
      const chartContainer = document.getElementById('chart-container')
      if (!chartContainer) {
        console.error('Chart container not found')
        this.showError('Chart container not found. Please check your HTML structure.')
        return
      }

      // Clean up any existing chart first
      this.cleanupChart()

      // Initialize the PriceChart component
      this.priceChart = new PriceChart('chart-container')
      console.log('PriceChart initialized successfully')
    } catch (error) {
      console.error('Error initializing chart:', error)
      this.showError(`Failed to initialize chart. ${error.message}`)
    }
  }

  // Clean up chart resources
  cleanupChart() {
    if (this.priceChart) {
      try {
        this.priceChart.destroy()
        this.priceChart = null
      } catch (e) {
        console.warn('Error cleaning up PriceChart:', e)
      }
    }

    // Clean up any canvas elements in the chart container
    const chartContainer = document.getElementById('chart-container')
    if (chartContainer) {
      chartContainer.innerHTML = ''
    }
  }

  // Initialize WebSocket connection
  initWebSocket() {
    console.log('Initializing WebSocket...')
    // The WebSocket connection is now managed by MarketDataService
    // No need to implement it here as it's handled by the AI integration service
  }

  // Initialize UI components
  initUI() {
    console.log('Initializing UI components...')
    // TODO: Initialize other UI components
  }

  // Show error message to user
  showError(message) {
    console.error('Error:', message)

    // Safely show error in UI if error container exists
    const errorContainer = document.getElementById('error-container')
    if (errorContainer) {
      errorContainer.textContent = message
      errorContainer.style.display = 'block'

      // Auto-hide after 5 seconds
      setTimeout(() => {
        if (errorContainer) {
          errorContainer.style.display = 'none'
        }
      }, 5000)
    }
  }

  // Hide loading overlay
  hideLoadingOverlay() {
    const overlay = document.getElementById('loadingOverlay')
    if (overlay) {
      overlay.style.display = 'none'
    }
  }
}

// Initialize the application when the script loads
const app = new StarCryptApp()

// Make app globally available for debugging
window.StarCryptApp = app
