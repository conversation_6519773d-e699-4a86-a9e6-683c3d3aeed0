const fs = require('fs');

// Read the file
const content = fs.readFileSync('./js/ui/websocket-handler.js', 'utf8');

// Check for basic balance of braces
const openParens = (content.match(/\(/g) || []).length;
const closeParens = (content.match(/\)/g) || []).length;
const openBraces = (content.match(/\{/g) || []).length;
const closeBraces = (content.match(/\}/g) || []).length;
const openSquare = (content.match(/\[/g) || []).length;
const closeSquare = (content.match(/\]/g) || []).length;

console.log(`Open parentheses: ${openParens}, Close parentheses: ${closeParens}`);
console.log(`Open braces: ${openBraces}, Close braces: ${closeBraces}`);
console.log(`Open square brackets: ${openSquare}, Close square brackets: ${closeSquare}`);

// Check for unterminated template literals
const backticks = (content.match(/`/g) || []).length;
console.log(`Backticks (should be even): ${backticks}`);

// Check for unterminated multi-line comments
const commentOpen = (content.match(/\/\*/g) || []).length;
const commentClose = (content.match(/\*\//g) || []).length;
console.log(`Comment open: ${commentOpen}, Comment close: ${commentClose}`);

// Check for quotation marks - they should be in pairs
const singleQuotes = (content.match(/'/g) || []).length;
const doubleQuotes = (content.match(/"/g) || []).length;
console.log(`Single quotes: ${singleQuotes}, Double quotes: ${doubleQuotes}`);

// Try to identify where the syntax error occurs with more detail
try {
  // Just try to parse the whole file at once
  new Function(content);
  console.log('✅ Full file parses successfully - no syntax errors!');
} catch (e) {
  console.log(`❌ Full file parsing failed: ${e.message}`);
  
  // Try to locate the syntax error by incrementally testing lines
  const lines = content.split('\n');
  let testCode = '';
  let lastSuccessfulLine = 0;
  
  for (let i = 0; i < lines.length; i++) {
    testCode += lines[i] + '\n';
    
    try {
      new Function(testCode);
      lastSuccessfulLine = i;
    } catch (e) {
      console.log(`\nSyntax error detected at line ${i+1}:\n"${lines[i]}"`); 
      console.log(`Error: ${e.message}`);
      
      // Provide the surrounding context for better debugging
      console.log('\nSurrounding context:');
      const start = Math.max(0, i - 5);
      const end = Math.min(lines.length - 1, i + 5);
      
      for (let j = start; j <= end; j++) {
        const indicator = j === i ? '>> ' : '   ';
        console.log(`${indicator}${j+1}: ${lines[j]}`);
      }
      
      break;
    }
  }
}

console.log('\nAnalysis complete');
